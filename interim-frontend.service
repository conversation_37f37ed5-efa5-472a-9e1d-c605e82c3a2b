[Unit]
Description=Interim Frontend Next.js Application
Documentation=https://nextjs.org/docs
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/root/.encrypted/InterimFrontend-main
Environment=NODE_ENV=production
Environment=PORT=3001
ExecStart=/usr/bin/npm start
Restart=on-failure
RestartSec=10
KillMode=mixed
KillSignal=SIGINT
TimeoutStopSec=5
SyslogIdentifier=interim-frontend

# Security settings
NoNewPrivileges=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/root/.encrypted/InterimFrontend-main

[Install]
WantedBy=multi-user.target
