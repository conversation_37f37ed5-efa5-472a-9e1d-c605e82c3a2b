{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "baseUrl": ".", "paths": {"@/*": ["./*"]}, "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules"]}