#!/bin/bash

# Setup script for Interim Frontend Service
# This script will create a systemd service to keep the Next.js app running on port 3001

echo "🚀 Setting up Interim Frontend Service..."

# Check if we're running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run this script as root (use sudo)"
    exit 1
fi

# Check if Node.js and npm are installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

# Build the Next.js application
echo "📦 Building the Next.js application..."
cd /root/.encrypted/InterimFrontend-main
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please check for errors and try again."
    exit 1
fi

# Copy service file to systemd directory
echo "📋 Installing systemd service..."
cp interim-frontend.service /etc/systemd/system/

# Reload systemd daemon
echo "🔄 Reloading systemd daemon..."
systemctl daemon-reload

# Enable the service to start on boot
echo "✅ Enabling service to start on boot..."
systemctl enable interim-frontend.service

# Start the service
echo "🚀 Starting the service..."
systemctl start interim-frontend.service

# Check service status
echo "📊 Checking service status..."
sleep 3
systemctl status interim-frontend.service --no-pager

# Show service logs
echo ""
echo "📝 Recent service logs:"
journalctl -u interim-frontend.service --no-pager -n 10

echo ""
echo "✅ Setup complete!"
echo ""
echo "🔧 Service Management Commands:"
echo "  Start service:   sudo systemctl start interim-frontend"
echo "  Stop service:    sudo systemctl stop interim-frontend"
echo "  Restart service: sudo systemctl restart interim-frontend"
echo "  Check status:    sudo systemctl status interim-frontend"
echo "  View logs:       sudo journalctl -u interim-frontend -f"
echo ""
echo "🌐 Your application should now be running on: http://localhost:3001"
echo "🔄 The service will automatically restart if it crashes and start on system boot."
