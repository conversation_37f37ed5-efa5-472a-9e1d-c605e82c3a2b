#!/bin/bash

# PM2 Setup script for Interim Frontend
# This script will use PM2 to keep the Next.js app running on port 3001

echo "🚀 Setting up Interim Frontend with PM2..."

# Check if we're in the correct directory
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found. Please run this script from the project directory."
    exit 1
fi

# Install PM2 globally if not already installed
if ! command -v pm2 &> /dev/null; then
    echo "📦 Installing PM2..."
    npm install -g pm2
fi


# Start the application with PM2
echo "🚀 Starting application with PM2..."
pm2 start npm --name "interim-frontend" -- run dev

# Save PM2 configuration
echo "💾 Saving PM2 configuration..."
pm2 save

# Setup PM2 to start on system boot
echo "🔄 Setting up PM2 to start on boot..."
pm2 startup

echo ""
echo "✅ Setup complete!"
echo ""
echo "🔧 PM2 Management Commands:"
echo "  View status:     pm2 status"
echo "  View logs:       pm2 logs interim-frontend"
echo "  Restart app:     pm2 restart interim-frontend"
echo "  Stop app:        pm2 stop interim-frontend"
echo "  Monitor:         pm2 monit"
echo ""
echo "🌐 Your application should now be running on: http://localhost:3001"
echo "🔄 PM2 will automatically restart if it crashes and start on system boot."

# Show current status
echo ""
echo "📊 Current PM2 status:"
pm2 status
