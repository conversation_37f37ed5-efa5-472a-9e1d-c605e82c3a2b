{"name": "name", "version": "1.0.0", "private": true, "author": "author", "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@nextui-org/card": "^2.0.34", "@nextui-org/react": "^2.4.8", "@nextui-org/system": "^2.2.6", "@nextui-org/theme": "^2.2.11", "@types/jwt-decode": "^3.1.0", "animate.css": "^4.1.1", "aos": "^2.3.4", "autoprefixer": "^10.4.20", "axios": "^1.7.2", "babel-loader": "^9.2.1", "bcryptjs": "^2.4.3", "bootstrap": "^5.3.3", "chart.js": "^4.4.8", "eslint": "8.41.0", "eslint-config-next": "13.4.3", "formidable": "^3.5.1", "framer-motion": "^11.12.0", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.456.0", "mdb-react-ui-kit": "^8.0.0", "mongoose": "^8.5.2", "multer": "^1.4.5-lts.1", "next": "^13.4.3", "next-auth": "^4.24.7", "next-connect": "^1.0.0", "nodemailer": "^6.9.14", "postcss": "^8.4.40", "primeicons": "^7.0.0", "react": "^18.2.0", "react-bootstrap": "^2.10.4", "react-chartjs-2": "^5.3.0", "react-circular-progressbar": "^2.1.0", "react-countup": "^6.5.3", "react-dom": "^18.2.0", "react-hook-form": "^7.52.1", "react-icons": "^5.3.0", "react-modal-video": "^2.0.1", "react-router-dom": "^6.26.0", "react-select": "^5.8.3", "react-slick": "^0.29.0", "react-step-wizard": "^5.3.11", "react-toastify": "^10.0.5", "react-tsparticles": "^2.12.2", "slick-carousel": "^1.8.1", "styled-components": "^6.1.12", "swiper": "^10.2.0", "tailwindcss": "^3.4.7", "tsparticles": "^2.12.0", "typescript": "^5.5.4", "wowjs": "^1.1.3"}, "devDependencies": {"@types/node": "^22.0.2", "@types/react": "^18.3.3"}}