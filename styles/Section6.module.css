/* Desktop styles - preserved */
.section6 {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f5fa 0%, #e4f0f9 100%);
  position: relative;
  padding-bottom: 100px;
  overflow: hidden;
}

.particle {
  position: absolute;
  border-radius: 50%;
  background-color: #2D8BBA;
}

.leftSide {
  flex: 1;
  padding: 60px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  z-index: 1;
}

.rightSide {
  flex: 1;
  padding: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.mainTitle {
  color: #025882;
  font-size: 4.5rem;
  font-weight: 700;
  margin-bottom: 5px;
  line-height: 1.2;
  font-family: 'Helvetica World Bold, Helvetica, Arial, sans-serif';
}

.subTitle {
  color: #2D8BBA;
  font-size: 2.5rem;
  font-family: 'Helvetica World Regular, Helvetica, Arial, sans-serif';
  margin-bottom: 0;
}

.subTitleLast {
  color: #2D8BBA;
  font-size: 2.5rem;
  margin-bottom: 40px;
  font-family: 'Helvetica World Regular, Helvetica, Arial, sans-serif';
}

.imageContainer {
  position: relative;
  width: 100%;
  height: 400px;
  margin-bottom: 40px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(45,139,186,0.15);
}

.rightTitle {
  color: #2D8BBA;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 30px;
  font-family: 'Helvetica World Bold, Helvetica, Arial, sans-serif';
}

.description {
  color: #333;
  font-size: 1.1rem;
  margin-bottom: 30px;
  line-height: 1.6;
  font-family: 'GlacialIndifference-Regular';
}

.list {
  list-style: none;
  padding: 0;
  margin-bottom: 40px;
  font-family: 'GlacialIndifference-Regular';
}

.listItem {
  color: #333;
  font-size: 1.1rem;
  margin-bottom: 15px;
  padding-left: 20px;
  position: relative;
  font-family: 'GlacialIndifference-Regular';
}

.listBullet {
  position: absolute;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #2D8BBA;
  border-radius: 50%;
  top: 8px;
}

.button {
  background: #2D8BBA;
  color: #fff;
  padding: 15px 30px;
  border: none;
  border-radius: 30px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 5px 15px rgba(45,139,186,0.2);
}

.button:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(45,139,186,0.3);
}

/* Tablet styles (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  .leftSide,
  .rightSide {
    padding: 40px;
  }

  .mainTitle {
    font-size: 3.5rem;
  }

  .subTitle,
  .subTitleLast {
    font-size: 2rem;
  }

  .rightTitle {
    font-size: 1.8rem;
  }

  .description,
  .listItem {
    font-size: 1rem;
  }

  .imageContainer {
    height: 300px;
  }
}

/* Mobile styles (up to 767px) */
@media (max-width: 767px) {
  .section6 {
    flex-direction: column;
    min-height: auto;
    padding-bottom: 50px;
  }

  .leftSide {
    padding: 30px 20px;
    align-items: center;
    text-align: center;
    order: 2;
  }

  .rightSide {
    padding: 30px 20px;
    order: 1;
  }

  .mainTitle {
    font-size: 2.5rem;
    margin-bottom: 15px;
    text-align: center;
  }

  .subTitle {
    font-size: 1.5rem;
    text-align: center;
    margin-bottom: 10px;
  }

  .subTitleLast {
    font-size: 1.5rem;
    text-align: center;
    margin-bottom: 30px;
  }

  .imageContainer {
    height: 250px;
    margin-bottom: 30px;
    border-radius: 15px;
  }

  .rightTitle {
    color: #2D8BBA;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
    font-family: 'Helvetica World Bold', Helvetica, Arial, sans-serif;
  }

  .description {
    font-size: 1rem;
    margin-bottom: 25px;
    text-align: center;
  }

  .list {
    margin-bottom: 30px;
  }

  .listItem {
    font-size: 0.95rem;
    margin-bottom: 12px;
    text-align: left;
  }

  .button {
    padding: 12px 25px;
    font-size: 1rem;
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
    display: block;
  }
}

/* Small mobile styles (up to 480px) */
@media (max-width: 480px) {
  .leftSide,
  .rightSide {
    padding: 20px 15px;
  }

  .mainTitle {
    font-size: 2rem;
    line-height: 1.3;
  }

  .subTitle,
  .subTitleLast {
    font-size: 1.3rem;
  }

  .imageContainer {
    height: 200px;
    border-radius: 10px;
  }

  .rightTitle {
    font-size: 1.5rem;
    text-align: center;
  }

  .description {
    font-size: 0.95rem;
    line-height: 1.5;
  }

  .listItem {
    font-size: 0.9rem;
    margin-bottom: 10px;
  }

  .button {
    padding: 10px 20px;
    font-size: 0.95rem;
  }
}

/* Particle animation */
@keyframes float {
  0% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
  100% { transform: translateY(10px) rotate(-5deg); }
}
