.estimateArea {
  padding: 80px 0;
  background: #f8f9fa;
  position: relative;
  overflow: hidden;
}

.sectionTitle {
  margin-bottom: 3rem;
  text-align: center;
}

.sectionTitle h2 {
  color: #2c3e50;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.sectionTitle .subTitle {
  color: #2D8BBA;
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 2px;
  display: block;
}

.searchSection {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.selectWrapper {
  position: relative;
  width: 100%;
}

.selectInput {
  appearance: none;
  width: 100%;
  padding: 0.8rem 1rem;
  padding-right: 2.5rem;
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  font-size: 1rem;
  color: #2c3e50;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.selectInput:hover {
  border-color: #2D8BBA;
}

.selectInput:focus {
  outline: none;
  border-color: #2D8BBA;
  box-shadow: 0 0 0 3px rgba(52, 133, 182, 0.1);
}

.selectIcon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #2D8BBA;
  pointer-events: none;
  transition: transform 0.3s ease;
}

.selectWrapper:hover .selectIcon {
  transform: translateY(-50%) translateY(-2px);
}

.searchForm {
  display: flex;
  gap: 1rem;
  width: 100%;
}

.searchInputWrapper {
  position: relative;
  flex: 1;
}

.searchInputWrapper i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #2D8BBA;
  transition: all 0.3s ease;
}

.searchInput {
  width: 100%;
  padding: 0.8rem 1rem;
  padding-left: 2.8rem;
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  font-size: 1rem;
  color: #2c3e50;
  transition: all 0.3s ease;
  background: white;
}

.searchInput:focus {
  outline: none;
  border-color: #2D8BBA;
  box-shadow: 0 0 0 3px rgba(52, 133, 182, 0.1);
}

.searchInput:focus + i {
  transform: translateY(-50%) scale(1.1);
}

.searchButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #2D8BBA;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  white-space: nowrap;
}

.searchButton i {
  transition: transform 0.3s ease;
}

.searchButton:hover {
  background: #2a6b93;
  transform: translateY(-2px);
}

.searchButton:hover i {
  transform: translateX(4px);
}

.tableContainer {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.table th {
  background: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  padding: 1rem;
  text-align: left;
  border-bottom: 2px solid #e1e8ed;
}

.table td {
  padding: 1rem;
  border-bottom: 1px solid #e1e8ed;
  color: #4a5568;
}

.table tr:hover {
  background: #f8f9fa;
}

.viewButton {
  display: inline-block;
  background: #2D8BBA;
  color: white !important;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  text-decoration: none;
  transition: all 0.3s ease;
  text-align: center;
}

.viewButton:hover {
  background: #2a6b93;
  transform: translateY(-1px);
  color: white;
}

.loadingSpinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.loadingSpinner::after {
  content: "";
  width: 30px;
  height: 30px;
  border: 2px solid #e1e8ed;
  border-top-color: #2D8BBA;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

.estimateImg {
  text-align: center;
  position: relative;
  padding-top: 2rem;
}

.sideImage {
  max-width: 100%;
  height: auto;
  animation: float 6s ease-in-out infinite;
}

.estimateShape {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 0;
  opacity: 0.1;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

@media (max-width: 768px) {
  .searchForm {
    flex-direction: column;
  }
  
  .searchInput, .selectInput {
    width: 100%;
  }
  
  .searchButton {
    width: 100%;
    justify-content: center;
  }
  
  .table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .estimateImg {
    margin-top: 2rem;
  }
}
