.section {
  height: 100vh;
  width: 100%;
  position: relative;
  display: block;
  font-family: 'Google Sans', sans-serif;
}

.backgroundImage {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.container {
  position: relative;
  height: 100%;
  z-index: 2;
}

.content {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 2rem 4rem;
}

.leftSide {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.title {
  color: white;
  font-size: 72px;
  font-weight: bold;
  line-height: 1.2;
  text-align: center;
  font-family: 'Helvetica World Bold, Helvetica, Arial, sans-serif';
  text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
}

.rightSide {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.imageContainer {
  position: relative;
  width: 100%;
  max-width: 600px;
  aspect-ratio: 4/3;
  margin-bottom: 2.5rem;
}

.imageWrapper {
  position: absolute;
  inset: 0;
  border-radius: 30px;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.image {
  transition: transform 0.3s ease;
}

.image:hover {
  transform: scale(1.05);
}

.button {
  background: white;
  color: #0066FF;
  padding: 1rem 3rem;
  border-radius: 9999px;
  font-size: 20px;
  font-weight: 600;
  font-family: 'Helvetica World Bold, Helvetica, Arial, sans-serif';
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.arrow {
  transition: transform 0.2s ease;
}

.button:hover .arrow {
  transform: translateX(4px);
}

/* Section2.module.css */
.section2 {
  padding: 40px 0;
  background: #fff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
}

.leftCol {
  padding: 0 15px;
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.heading {
  color: #2D8BBA;
  font-size: clamp(32px, 4vw, 48px);
  font-weight: 700;
  margin-top: 20px;
  margin-bottom: 25px;
  line-height: 1.2;
  font-family: 'Helvetica World Bold', Helvetica, Arial, sans-serif;
}

.description {
  font-size: clamp(16px, 2vw, 20px);
  line-height: 1.8;
  color: #666;
  margin-bottom: 30px;
  font-family: 'GlacialIndifference-Regular';
}

.buttonRow {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.buttonCol {
  display: flex;
  justify-content: flex-end;
  padding-right: 15px;
  margin-top: clamp(-30px, -4vh, -20px);
}

.contactButton {
  background: #2D8BBA;
  color: #fff;
  padding: 15px 40px;
  border: 2px solid #2D8BBA;
  border-radius: 9999px;
  font-size: clamp(14px, 1.5vw, 16px);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: 'Helvetica World Bold', Helvetica, Arial, sans-serif;
  display: flex;
  align-items: center;
  gap: 10px;
}

.contactButton:hover {
  background: #236b8e;
  border-color: #236b8e;
}

.rightCol {
  padding: 0 15px;
  display: flex;
  justify-content: flex-end;
  flex-direction: column;
  align-items: flex-end;
}

.imageWrapper {
  border-radius: 20px;
  overflow: hidden;
  width: clamp(300px, 80%, 540px);
  height: clamp(320px, 65vh, 540px);
  box-shadow: 0 8px 24px rgba(45,139,186,0.10);
}

.img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  border-radius: 20px;
}

.content-image-wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
}

.left-col {
  width: 50%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.right-col {
  width: 50%;
  max-width: 600px;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

@media (max-width: 991px) {
  .row {
    flex-direction: column-reverse;
  }
  .rightCol, .leftCol {
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0;
  }
  .buttonRow, .buttonCol {
    justify-content: center;
    padding-right: 0;
    margin-top: 10px;
  }
  .imageWrapper {
    width: 100%;
    height: 260px;
    margin-top: 20px;
  }
  .content-image-wrapper {
    flex-direction: column;
  }
  .left-col, .right-col {
    width: 100%;
    max-width: 100%;
  }
  .right-col {
    margin-top: 30px;
  }
}

@media (max-width: 600px) {
  .section2 {
    padding: 24px 0;
  }
  .heading {
    font-size: 1.4rem;
    margin-top: 10px;
    margin-bottom: 15px;
    text-align: center;
  }
  .description {
    font-size: 1rem;
    margin-bottom: 18px;
    text-align: center;
  }
  .contactButton {
    font-size: 0.95rem;
    padding: 10px 18px;
    width: 100%;
    max-width: 320px;
    justify-content: center;
  }
  .imageWrapper {
    height: 180px;
    border-radius: 12px;
  }
}

.hideMobile {
  display: block;
}
@media (max-width: 767px) {
  .hideMobile {
    display: none !important;
  }
}
