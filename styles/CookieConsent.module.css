/* CookieConsent.module.css */
.cookieOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.cookiePopup {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.cookieHeader {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.cookieTitle {
  color: #2D8BBA;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  font-family: 'Helvetica World Bold', Helvetica, Arial, sans-serif;
}

.cookieSubtitle {
  color: #666;
  font-size: 0.95rem;
  margin: 0;
  line-height: 1.5;
}

.cookieBody {
  padding: 20px 24px;
}

.cookieDescription {
  color: #333;
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 20px;
}

.cookieCategories {
  margin-bottom: 24px;
}

.categoryItem {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
}

.categoryHeader {
  padding: 16px;
  background: #f9fafb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background 0.2s;
}

.categoryHeader:hover {
  background: #f3f4f6;
}

.categoryTitle {
  font-weight: 600;
  color: #374151;
  margin: 0;
  font-size: 0.95rem;
}

.categoryToggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggleSwitch {
  position: relative;
  width: 44px;
  height: 24px;
  background: #d1d5db;
  border-radius: 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.toggleSwitch.active {
  background: #2D8BBA;
}

.toggleSwitch.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toggleSlider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: transform 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggleSwitch.active .toggleSlider {
  transform: translateX(20px);
}

.categoryContent {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  background: white;
}

.categoryDescription {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.cookieList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.cookieItem {
  color: #555;
  font-size: 0.85rem;
  padding: 4px 0;
  border-bottom: 1px solid #f3f4f6;
}

.cookieItem:last-child {
  border-bottom: none;
}

.cookieActions {
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: space-between;
}

.cookieButton {
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid transparent;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
}

.acceptAll {
  background: #2D8BBA;
  color: white;
  border-color: #2D8BBA;
}

.acceptAll:hover {
  background: #236b8e;
  border-color: #236b8e;
  color: white;
}

.rejectAll {
  background: transparent;
  color: #666;
  border-color: #d1d5db;
}

.rejectAll:hover {
  background: #f3f4f6;
  color: #333;
}

.customize {
  background: transparent;
  color: #2D8BBA;
  border-color: #2D8BBA;
}

.customize:hover {
  background: #2D8BBA;
  color: white;
}

.privacyLink {
  color: #2D8BBA;
  text-decoration: underline;
  font-size: 0.9rem;
}

.privacyLink:hover {
  color: #236b8e;
}

@media (max-width: 768px) {
  .cookieOverlay {
    padding: 10px;
    align-items: flex-end;
  }
  
  .cookiePopup {
    max-height: 85vh;
    border-radius: 12px 12px 0 0;
  }
  
  .cookieActions {
    flex-direction: column;
  }
  
  .cookieButton {
    width: 100%;
    min-width: auto;
  }
  
  .categoryHeader {
    padding: 12px 16px;
  }
  
  .cookieHeader {
    padding: 20px 20px 12px;
  }
  
  .cookieBody {
    padding: 16px 20px;
  }
  
  .cookieActions {
    padding: 16px 20px;
  }
}
