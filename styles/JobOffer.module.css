.wrapper {
  margin-top: 2rem;
  min-height: 100vh;
  background: #f5f7fa;
  padding: 2rem 1rem;
  padding-top: 100px;
}

.jobCard {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Header Section */
.cardHeader {
  background: linear-gradient(135deg, #2D8BBA 0%, #2d7299 100%);
  padding: 3rem 2rem;
  position: relative;
  overflow: hidden;
}

.cardHeader::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: url('/assets/images/pattern-bg.png') repeat;
  opacity: 0.1;
  animation: moveBackground 20s linear infinite;
}

.headerContent {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.jobInfo {
  flex-grow: 1;
}

.contractBadge {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(8px);
  border-radius: 50px;
  color: white;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.contractBadge:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.jobTitle {
  font-size: 2.5rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.locationInfo {
  display: flex;
  gap: 2rem;
  color: rgba(255, 255, 255, 0.9);
}

.locationInfo span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.locationInfo i {
  opacity: 0.7;
}

/* Recruiter Info */
.recruiterInfo {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  padding: 1.5rem;
  border-radius: 15px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.recruiterInfo:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.recruiterImage {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  margin: 0 auto 1rem;
  border: 3px solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
}

.recruiterImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recruiterInfo h3 {
  color: white;
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
}

.recruiterInfo p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
}

/* Quick Info Section */
.quickInfo {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  padding: 2rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.infoCard {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.infoCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(52, 133, 182, 0.15);
  border-color: #2D8BBA;
}

.iconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #ebf5ff;
  border-radius: 12px;
  color: #2D8BBA;
  font-size: 1.25rem;
}

.infoContent h4 {
  color: #64748b;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.infoContent p {
  color: #1e293b;
  font-weight: 600;
}

/* Job Details Section */
.jobDetails {
  padding: 2rem;
}

.detailSection {
  padding: 2rem;
  background: white;
  border-radius: 12px;
  margin-bottom: 2rem;
  border: 1px solid #e2e8f0;
}

.detailSection h2 {
  font-size: 1.5rem;
  color: #1e293b;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.detailSection h2 i {
  color: #2D8BBA;
}

.content {
  color: #475569;
  line-height: 1.7;
}

/* Apply Section */
.applySection {
  padding: 3rem 2rem;
  text-align: center;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.applySection h2 {
  font-size: 2rem;
  color: #1e293b;
  margin-bottom: 1.5rem;
}

.applyContent {
  max-width: 500px;
  margin: 0 auto;
}

.applyContent p {
  color: #64748b;
  margin-bottom: 1.5rem;
}

.applyButton {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #2D8BBA 0%, #2d7299 100%);
  color: white;
  font-weight: 500;
  border-radius: 50px;
  transition: all 0.3s ease;
}

.applyButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(52, 133, 182, 0.25);
}

.spinner {
  width: 50px;
  height: 50px;
  border: 3px solid #e2e8f0;
  border-top-color: #2D8BBA;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes moveBackground {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 100px 100px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .wrapper {
    padding: 1rem;
    padding-top: 80px;
  }

  .headerContent {
    flex-direction: column;
  }

  .jobTitle {
    font-size: 2rem;
  }

  .quickInfo {
    grid-template-columns: 1fr;
  }

  .recruiterInfo {
    width: 100%;
  }

  .locationInfo {
    flex-direction: column;
    gap: 1rem;
  }

  .detailSection {
    padding: 1.5rem;
  }
}
