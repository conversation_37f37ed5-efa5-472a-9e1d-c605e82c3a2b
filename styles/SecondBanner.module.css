.bannerSection {
  height: 100vh;
  width: 100%;
  position: relative;
  display: block;
  font-family: 'Google Sans', sans-serif;
  overflow: hidden;
}

.backgroundImage {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.bottomContent {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 4rem;
  background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);
  z-index: 2;
}

.bottomLeft h2 {
  color: white;
  font-size: 5.8rem;
  font-weight: 1600;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.jobButton {
  background-color: #2D8BBA;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 5px;
  font-size: 1.2rem;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.3s ease;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
  font-family: 'Google Sans', sans-serif;
}

.jobButton:hover {
  background-color: #2a6b93;
  transform: translateY(-2px);
}

.leftSide {
  position: relative;
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  z-index: 10;
}

.trapezoid {
  position: absolute;
  top: 0;
  left: -10%;
  width: 120%;
  height: 100%;
  background: #2D8BBA;
  transform: skew(-10deg);
  z-index: -1;
}

.rightSide {
  position: relative;
  width: 50%;
  overflow: hidden;
}

.backgroundSlider {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.8s ease-in-out, visibility 0.8s ease-in-out;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.slide.active {
  opacity: 1;
  visibility: visible;
}

.sliderControls {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 10;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: #2D8BBA;
  transform: scale(1.2);
}

.container {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.content {
  text-align: center;
  color: white;
  font-family: 'Google Sans', sans-serif;
}

.titleHeading {
  opacity: 0;
  transform: translateY(30px);
  animation: slideUp 0.8s ease forwards;
}

.teamImageContainer {
  width: 200px;
  height: 200px;
  margin: 0 auto 2rem;
  position: relative;
  perspective: 1000px;
}

.teamImage {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
  animation: floatAnimation 3s ease-in-out infinite;
}

.teamImageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
  animation: rotateAnimation 8s linear infinite;
}

.mainTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 2rem;
  line-height: 1.3;
  text-align: center;
  padding: 0 1rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  font-family: 'Google Sans', sans-serif;
}

.quote {
  display: inline-block;
  font-size: 2rem;
  color: white;
  opacity: 0.8;
  margin: 0 5px;
  font-family: 'Google Sans', sans-serif;
}

.paraDesc {
  font-size: 1.5rem;
  line-height: 1.8;
  color: white;
  margin: 2rem 0;
  max-width: 800px;
  margin: 2rem auto;
  font-family: 'Google Sans', sans-serif;
}

.line1, .line2 {
  display: block;
  opacity: 0;
  transform: translateY(20px);
}

.line1 {
  animation: slideUp 0.6s ease 0.4s forwards;
}

.line2 {
  animation: slideUp 0.6s ease 0.6s forwards;
}

.buttons {
  margin-top: 3rem;
  opacity: 0;
  animation: fadeIn 0.8s ease 0.8s forwards;
}

.buttonContainer {
  display: flex;
  gap: 30px;
  justify-content: center;
}

.button {
  display: inline-block;
  padding: 18px 40px;
  background: white;
  color: #2D8BBA;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.1rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 200px;
  font-family: 'Google Sans', sans-serif;
}

.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.buttonPulse::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  animation: pulse 2s infinite;
}
.topContent {
  position: absolute;
  top: 30%; /* Vertically center the content */
  left: 60%; /* Horizontally center the content */
  transform: translate(-50%, -50%); /* Offset the content by its own size to center it */
  z-index: 10;
  text-align: left; /* Align the text inside to the left */
  width: auto; /* Allow the width to adapt to the content */
  padding-top: 20px; /* Optional: add some padding to space from the top */
}

.centeredText {
  font-family: 'Helvetica World Bold', Helvetica, Arial, sans-serif;
  font-size: 96px;
  color: #FFFFFF;
  margin-bottom: 0;
}
.leftAlignedText {
  font-family: 'Helvetica World Bold', Helvetica, Arial, sans-serif;
  font-size: 56px;
  color: #FFFFFF;
  margin-bottom: 0;
  text-align: left; /* Align the text to the left */
  line-height: 1.4;
}
.leftAlignedText2 {
  font-family: 'Helvetica World Regular', Helvetica, Arial, sans-serif;
  font-size: 56px;
  color: #FFFFFF;
  margin-bottom: 0;
  text-align: left; /* Align the text to the left */
  line-height: 1.4;
}
@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes floatAnimation {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes rotateAnimation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive text classes */
.responsiveText {
  font-family: 'Helvetica World Regular, Helvetica, Arial, sans-serif';
  font-size: 46px;
  color: #FFFFFF;
  line-height: 1.2;
  margin: 0;
  padding: 0;
}

.responsiveSpan {
  color: #2D8BBA;
  font-size: 5.5rem;
  font-weight: bold;
  display: inline-block;
}

.bannerTitle {
  font-family: 'Helvetica World Bold', Helvetica, Arial, sans-serif;
  font-size: clamp(38px, 4vw, 58px);
  color: #fff;
  margin: 0;
  text-align: left;
  position: absolute;
  bottom: clamp(100px, 10vh, 150px);
  left: clamp(20px, 5vw, 40px);
  z-index: 3;
}

.bannerButton {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 40px;
  height: 52px;
  background-color: #2D8BBA;
  color: #fff;
  border: 2px solid #2D8BBA;
  border-radius: 9999px;
  font-size: clamp(14px, 1.5vw, 16px);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: 'Helvetica World Bold', Helvetica, Arial, sans-serif;
  position: absolute;
  bottom: clamp(20px, 5vh, 40px);
  left: clamp(20px, 3vw, 40px);
  z-index: 3;
}

.bannerButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.arrowCircle {
  width: 38px;
  height: 38px;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@media (max-width: 992px) {
  .bannerSection {
    flex-direction: column;
  }

  .leftSide, .rightSide {
    width: 100%;
  }

  .leftSide {
    min-height: 60vh;
  }

  .rightSide {
    min-height: 40vh;
  }

  .trapezoid {
    left: -5%;
    width: 110%;
    transform: skew(-5deg);
  }

  .mainTitle {
    font-size: 2.5rem;
  }

  .paraDesc {
    font-size: 1.25rem;
  }

  .buttonContainer {
    flex-direction: column;
    align-items: center;
  }

  .button {
    width: 100%;
    max-width: 300px;
  }

  .teamImageContainer {
    width: 150px;
    height: 150px;
  }
}

@media (max-width: 768px) {
  .mainTitle {
    font-size: 1.8rem;
    line-height: 1.4;
    padding: 0 0.5rem;
  }
  
  .mainTitle span {
    margin: 0.1rem 0;
  }

  .paraDesc {
    font-size: 1.1rem;
  }

  .teamImageContainer {
    width: 120px;
    height: 120px;
  }

  .bannerTitle {
    font-size: 2rem;
    left: 16px;
    bottom: 90px;
    text-align: center;
    position: static;
    margin-bottom: 1.5rem;
  }
  .bannerButton {
    font-size: 1rem;
    padding: 12px 18px;
    height: 44px;
    left: 0;
    right: 0;
    margin: 0 auto;
    position: static;
    width: 100%;
    max-width: 320px;
    display: flex;
    justify-content: center;
  }
  .arrowCircle {
    width: 32px;
    height: 32px;
  }
  .bottomContent {
    flex-direction: column;
    align-items: center;
    padding: 1.5rem 0.5rem;
    gap: 1rem;
  }
  .bottomRight {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}

/* Tablet responsive styles */
@media (min-width: 769px) and (max-width: 1024px) {
  .bottomContent {
    padding: 2rem 2rem;
  }

  .bottomContent p {
    font-size: 2.5rem !important;
  }

  .bottomContent span {
    font-size: 3rem !important;
  }

  .responsiveText {
    font-size: 2.5rem !important;
  }

  .responsiveSpan {
    font-size: 3rem !important;
  }

  .bannerTitle {
    font-size: 2.8rem;
    left: 32px;
    bottom: 110px;
  }
  .bannerButton {
    font-size: 1.1rem;
    padding: 14px 28px;
    height: 48px;
    left: 32px;
    bottom: 32px;
  }
  .arrowCircle {
    width: 34px;
    height: 34px;
  }
}
