@font-face {
  font-family: 'HouschkaHead2020';
  src: url('/assets/fonts/houschkahead.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
    font-family: 'Helvetica World Bold';
    src: url('/assets/fonts/helvetica-world-bold.woff2') format('woff2'),
         url('/assets/fonts/helvetica-world-bold.woff') format('woff'),
         url('/assets/fonts/helvetica-world-bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Helvetica World Regular';
    src: url('/assets/fonts/helvetica-world-regular.woff2') format('woff2'),
         url('/assets/fonts/helvetica-world-regular.woff') format('woff'),
         url('/assets/fonts/helvetica-world-regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'GlacialIndifference-Regular';
    src: url('/assets/fonts/GlacialIndifference-Regular.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

.main-title {
  font-family: 'HouschkaHead2020', serif !important;
}

.title.main-title {
  font-size: 2.5rem;
  color: #2D8BBA;
}

.sub-title.main-title {
  font-size: 1.2rem;
  color: #2D8BBA;
}

.banner-title.main-title {
  font-size: 3.5rem;
  color: #fff;
}

@media (max-width: 768px) {
  .title.main-title {
      font-size: 2rem;
  }

  .banner-title.main-title {
      font-size: 2.5rem;
  }
}

/* Submenu styles */
.menu-item-has-children {
  position: relative;
}

.menu-item-has-children .submenu {
  position: absolute;
  left: 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 1000;
}

.menu-item-has-children:hover .submenu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.submenu li a:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}
