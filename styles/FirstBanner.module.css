/* Desktop styles - unchanged */
.bannerSection {
  background-size: cover;
  background-position: center;
  height: 50vh;
  position: relative;
  display: flex;
  align-items: center;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.content {
  position: relative;
  z-index: 1;
  width: 100%;
  padding: 60px 0;
}

.container {
  min-height: 50vh;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.leftContent {
  padding-left: 20px;
  max-width: 600px;
  align-self: center;
}

.rightContent {
  align-self: center;
  padding-right: 80px;
  max-width: 600px;
}

.title {
  color: #fff;
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 30px;
  text-align: left;
  font-family: 'Helvetica World Bold, Helvetica, Arial, sans-serif';
}

.description {
  color: #fff;
  font-size: 1.1rem;
  line-height: 1.8;
  text-align: left;
  font-family: 'GlacialIndifference-Regular';
}

.button {
  background-color: #2D8BBA;
  color: white;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  border: 2px solid #2D8BBA;
  box-shadow: 0 2px 4px rgba(52, 133, 182, 0.1);
  min-width: 130px;
  max-width: fit-content;
  margin: 0 auto;
  border-radius: 25px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(52, 133, 182, 0.2);
}

/* Tablet styles (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  .container {
    min-height: 60vh;
    padding: 0 20px;
  }

  .leftContent {
    padding-left: 10px;
    max-width: 500px;
  }

  .rightContent {
    padding-right: 40px;
    max-width: 500px;
  }

  .title {
    font-size: 3rem;
    margin-bottom: 25px;
  }

  .description {
    font-size: 1rem;
    line-height: 1.7;
  }
}

/* Mobile styles (up to 767px) */
@media (max-width: 767px) {
  .bannerSection {
    height: auto;
    min-height: 70vh;
    align-items: stretch;
  }

  .content {
    padding: 40px 0;
  }

  .container {
    min-height: auto;
    flex-direction: column;
    text-align: center;
    padding: 0 15px;
    gap: 30px;
  }

  .leftContent {
    padding-left: 0;
    max-width: 100%;
    align-self: stretch;
    order: 1;
  }

  .rightContent {
    padding-right: 0;
    max-width: 100%;
    align-self: stretch;
    order: 2;
  }

  .title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 25px;
    line-height: 1.2;
  }

  .title br {
    display: none;
  }

  .description {
    font-size: 1rem;
    text-align: center;
    line-height: 1.6;
    margin-bottom: 25px;
  }

  .button {
    margin: 20px auto 0;
    padding: 12px 24px;
    font-size: 1rem;
    min-width: 150px;
  }
}

/* Small mobile styles (up to 480px) */
@media (max-width: 480px) {
  .bannerSection {
    min-height: 60vh;
  }

  .content {
    padding: 30px 0;
  }

  .container {
    padding: 0 10px;
    gap: 20px;
  }

  .title {
    font-size: 2rem;
    margin-bottom: 20px;
  }

  .description {
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 20px;
  }

  .button {
    padding: 10px 20px;
    font-size: 0.9rem;
    min-width: 140px;
  }
}
