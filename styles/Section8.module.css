/* Section8.module.css */
.section8 {
  display: flex;
  min-height: 60vh;
  background-color: #fff;
  padding: 30px 0;
  margin-bottom: 100px;
}
.leftSide {
  flex: 0 0 40%;
  padding: 30px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  width: 100%;
}
.mainTitle {
  color: #2D8BBA;
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.2;
  text-align: left;
  width: 100%;
  font-family: 'Helvetica World Bold', Helvetica, Arial, sans-serif;
}
.subTitle {
  font-size: 2.5rem;
  margin-bottom: 20px;
  line-height: 1.3;
  color: #2D8BBA;
  text-align: left;
  width: 100%;
  font-family: 'Helvetica World Regular', Helvetica, Arial, sans-serif;
}
.description {
  color: #333;
  font-size: 1.8rem;
  line-height: 1.5;
  text-align: left;
  width: 100%;
}
.rightSide {
  flex: 0 0 60%;
  padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.cardsContainer {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 20px;
}
.card {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}
.cardImage {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 10px;
  overflow: hidden;
  flex-shrink: 0;
}
.cardIconTitle {
  display: flex;
  align-items: center;
  gap: 15px;
}
.cardTitle {
  font-size: 1.3rem;
  color: #333;
  margin: 0;
}
.cardText {
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
}
.blueLine {
  height: 2px;
  background-color: #2D8BBA;
  width: 100%;
  margin: 5px 0;
  transform-origin: left;
}

@media (max-width: 991px) {
  .section8 {
    flex-direction: column;
    padding: 20px 0;
    margin-bottom: 60px;
  }
  .leftSide, .rightSide {
    flex: 1 1 100%;
    padding: 18px 10px;
    width: 100%;
  }
  .mainTitle {
    font-size: 2.2rem;
    text-align: center;
  }
  .subTitle {
    font-size: 1.3rem;
    text-align: center;
  }
  .description {
    font-size: 1.1rem;
    text-align: center;
  }
  .cardsContainer {
    margin-top: 10px;
  }
  .card {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
  .cardImage {
    width: 120px;
    height: 120px;
  }
  .cardIconTitle {
    justify-content: center;
  }
  .cardTitle {
    font-size: 1.1rem;
    text-align: center;
  }
  .cardText {
    font-size: 0.95rem;
    text-align: center;
  }
}

@media (max-width: 600px) {
  .section8 {
    padding: 10px 0;
    margin-bottom: 50px;
  }
  .mainTitle {
    font-size: 1.3rem;
  }
  .subTitle {
    font-size: 1rem;
  }
  .description {
    font-size: 0.95rem;
  }
  .cardImage {
    width: 90px;
    height: 90px;
    border-radius: 8px;
  }
  .cardTitle {
    font-size: 0.95rem;
  }
  .cardText {
    font-size: 0.85rem;
  }
  .card {
    margin-bottom: 18px;
  }
}
