.profileContainer {
  max-width: 1200px;
  margin: 120px auto 60px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.profileHeader {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e8eef4;
  text-align: center;
}

.profileTitle {
  color: #2D8BBA;
  font-size: 32px;
  font-weight: 700;
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
}

.profileTitle:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #2D8BBA 0%, #1a365d 100%);
  border-radius: 3px;
}

.sectionContainer {
  margin-bottom: 40px;
  background-color: #fff;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.sectionContainer:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: linear-gradient(to bottom, #2D8BBA, #1a365d);
  border-radius: 5px 0 0 5px;
}

.sectionTitle {
  font-size: 24px;
  font-weight: 700;
  color: #1a365d;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f5fa;
  position: relative;
  padding-left: 10px;
}

.cardsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(270px, 1fr));
  gap: 25px;
}

.card {
  background: #fff;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.06);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  border: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
  border-color: #2D8BBA;
}

.card:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #2D8BBA, #1a365d);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.4s ease;
}

.card:hover:after {
  transform: scaleX(1);
  transform-origin: left;
}

.cardIcon {
  margin-bottom: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2D8BBA 0%, #1a365d 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 22px;
  box-shadow: 0 6px 15px rgba(45, 139, 186, 0.2);
  transition: all 0.3s ease;
}

.card:hover .cardIcon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 8px 20px rgba(45, 139, 186, 0.3);
}

.cardTitle {
  font-size: 20px;
  font-weight: 700;
  color: #1a365d;
  margin-bottom: 12px;
  transition: color 0.3s ease;
}

.card:hover .cardTitle {
  color: #2D8BBA;
}

.cardText {
  font-size: 15px;
  color: #606C72;
  margin-bottom: 20px;
  flex-grow: 1;
  line-height: 1.5;
}

.cardLink {
  text-decoration: none;
  color: #2D8BBA;
  font-weight: 600;
  font-size: 15px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  margin-top: auto;
  position: relative;
  padding-bottom: 3px;
}

.cardLink:before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #2D8BBA;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.cardLink:hover {
  color: #1a365d;
}

.cardLink:hover:before {
  transform: scaleX(1);
  transform-origin: left;
}

.formContainer {
  margin-top: 40px;
}

.cardWithForm {
  background: #fff;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  margin-bottom: 30px;
  border: 1px solid rgba(45, 139, 186, 0.1);
  position: relative;
  overflow: hidden;
}

.cardWithForm:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #2D8BBA, #1a365d);
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e8eef4;
}

.backButton {
  padding: 10px 18px;
  background-color: #f2f5f8;
  border: none;
  border-radius: 8px;
  color: #1a365d;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.backButton:hover {
  background-color: #e8eef4;
  transform: translateX(-3px);
}

.backButton:before {
  content: '←';
  margin-right: 8px;
  font-size: 18px;
}

.formGroup {
  margin-bottom: 25px;
}

.formLabel {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: #1a365d;
  font-size: 16px;
}

.formInput {
  width: 100%;
  padding: 12px 18px;
  border: 2px solid #e8eef4;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background-color: #f8fafc;
  color: #1a365d;
}

.formInput:focus {
  border-color: #2D8BBA;
  outline: none;
  box-shadow: 0 0 0 4px rgba(45, 139, 186, 0.15);
  background-color: #fff;
}

.submitButton {
  background: linear-gradient(135deg, #2D8BBA 0%, #1a365d 100%);
  color: white;
  border: none;
  padding: 14px 30px;
  border-radius: 8px;
  font-weight: 700;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
  margin-top: 15px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.submitButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(26, 54, 93, 0.3);
}

.submitButton:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255,255,255,0.1), rgba(255,255,255,0.4));
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.submitButton:hover:after {
  transform: translateX(100%);
}

.loader, .error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: #f8f9fa;
}

@media (max-width: 768px) {
  .cardsGrid {
    grid-template-columns: 1fr;
  }
  
  .profileTitle {
    font-size: 26px;
  }
  
  .sectionTitle {
    font-size: 22px;
  }
  
  .profileContainer {
    padding: 15px;
  }
  
  .sectionContainer {
    padding: 20px;
  }
  
  .card {
    padding: 20px;
  }
}
