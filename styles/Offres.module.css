/* Offres.module.css - Responsive styles for offres.js */

.heroSection {
  padding-top: 120px;
  background: #fff;
}

.bgHalf {
  background-image: url('/images/Slider/bg.jpg');
  background-position: top;
  background-size: cover;
  width: 100%;
  min-height: 220px;
  position: relative;
}

.titleHeading {
  text-align: center;
  color: #2D8BBA;
  font-weight: bold;
  margin-bottom: 1.5rem;
}

.paraDesc {
  color: #fff;
  text-align: center;
  margin: 0 auto;
  max-width: 600px;
}

.section {
  padding: 2.5rem 0;
}

.heading {
  color: #2D8BBA;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
}

.jobCard {
  background: #fff;
  border-radius: 1rem;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  transition: border 0.3s, box-shadow 0.3s;
  display: flex;
  flex-direction: column;
  position: relative;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  height: 100%;
}

.jobCard:hover {
  border: 1.5px solid #2D8BBA;
  box-shadow: 0 4px 12px rgba(45,139,186,0.08);
}

.recruiterImage {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #2D8BBA;
  position: relative;
}

.recruiterName {
  color: #2D8BBA;
  font-weight: 500;
  font-size: 0.95rem;
  margin-top: 0.5rem;
  text-align: center;
}

.jobCardHeader {
  text-align: center;
  margin-bottom: 1rem;
}

.jobCardTitle {
  color: #2D8BBA;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.jobCardAgency {
  color: #666;
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
}

.locationTag {
  background: rgba(52, 133, 182, 0.1);
  color: #2D8BBA;
  font-weight: 500;
  border-radius: 999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.95rem;
  margin: 0 0.25rem 0.5rem 0;
  display: inline-flex;
  align-items: center;
}

.jobCardDesc {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  margin-bottom: 1rem;
}

.actionButton {
  background: #2D8BBA;
  color: #fff;
  font-size: 0.95rem;
  font-weight: 500;
  border: 2px solid #2D8BBA;
  border-radius: 25px;
  min-width: 130px;
  max-width: fit-content;
  margin: 0 auto;
  padding: 0.5rem 1.5rem;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(52, 133, 182, 0.1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.actionButton:hover {
  background: transparent;
  color: #2D8BBA;
  transform: translateY(-1px);
}

.pagination {
  margin-bottom: 10%;
}

.searchBarRow {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
  justify-content: center;
}

@media (max-width: 991px) {
  .jobCard {
    margin-bottom: 1.5rem;
  }
  .section {
    padding: 1.5rem 0;
  }
}

@media (max-width: 767px) {
  .heroSection {
    padding-top: 80px;
  }
  .bgHalf {
    min-height: 120px;
  }
  .heading {
    font-size: 1.2rem;
  }
  .jobCard {
    border-radius: 0.7rem;
    box-shadow: 0 1px 2px rgba(0,0,0,0.04);
  }
  .recruiterImage {
    width: 60px;
    height: 60px;
  }
  .recruiterName {
    font-size: 0.85rem;
  }
  .jobCardTitle {
    font-size: 1rem;
  }
  .jobCardAgency {
    font-size: 0.85rem;
  }
  .locationTag {
    font-size: 0.85rem;
    padding: 0.18rem 0.6rem;
  }
  .jobCardDesc {
    font-size: 0.85rem;
    -webkit-line-clamp: 4;
  }
  .actionButton {
    font-size: 0.85rem;
    min-width: 100px;
    padding: 0.4rem 1rem;
  }
  .pagination {
    margin-bottom: 18%;
  }
  .searchBarRow {
    flex-direction: row;
    gap: 8px;
    width: 100%;
  }
  .searchBarRow > * {
    flex: 1 1 0;
    min-width: 0;
    max-width: 100%;
  }
  .searchBarRow button {
    flex: 0 0 auto;
    white-space: nowrap;
    min-width: 90px;
    margin-left: 0;
  }
}
