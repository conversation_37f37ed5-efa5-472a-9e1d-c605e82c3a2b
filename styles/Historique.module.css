.historique {
  padding: 80px 0;
  position: relative;
}

.customTitle {
  text-align: center;
  margin-bottom: 2rem;
}

.mainTitle {
  text-align: center;
  margin-bottom: 4rem;
  color: #2c3e50;
}

.timelineLine {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #2D8BBA;
  transform: translateX(-50%);
}

.row {
  display: flex;
  align-items: center;
  margin-bottom: 4rem;
  position: relative;
}

.row.reverse {
  flex-direction: row-reverse;
}

.image {
  flex: 1;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

.image img {
  max-width: 100%;
  height: auto;
  border-radius: 10px;
  transition: transform 0.6s ease-in-out;
  transform-origin: center center;
}

.image:hover img {
  transform: rotate(360deg);
}

.textCard {
  flex: 1;
  padding: 2rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin: 0 2rem;
}

.timeline {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.timelineDot {
  width: 20px;
  height: 20px;
  background: #2D8BBA;
  border-radius: 50%;
  margin: 1rem 0;
}

.yearLeft, .yearRight {
  font-weight: bold;
  color: #2D8BBA;
}

@media (max-width: 768px) {
  .row {
    flex-direction: column;
    text-align: center;
    margin-bottom: 2rem;
  }

  .row.reverse {
    flex-direction: column;
  }

  .textCard {
    margin: 1rem 0;
  }

  .timelineLine {
    left: 20px;
  }

  .timeline {
    position: relative;
    left: auto;
    transform: none;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: -1rem;
    margin-bottom: 1rem;
  }

  .timelineDot {
    margin: 0;
    flex-shrink: 0;
  }

  .yearLeft, .yearRight {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1;
  }
}

/* New responsive classes for the updated component */
.historiqueSection {
  background-color: #fff;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.headerSection {
  text-align: center;
  margin-bottom: 50px;
}

.subTitle {
  color: #000;
  font-size: 1.8rem;
  margin-bottom: 10px;
  font-family: 'Helvetica World Bold, Helvetica, Arial, sans-serif';
}

.description {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
}

.cardsContainer {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 40px;
}

.cardItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 75%;
}

.imageContainer {
  margin-bottom: 20px;
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  border-radius: 10px;
  overflow: hidden;
}

.textContainer {
  background: #fff;
  border: 2px solid #2D8BBA;
  border-radius: 50px;
  padding: 15px 20px;
  text-align: center;
  width: 100%;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.cardText {
  color: #2D8BBA;
  font-weight: 600;
  margin: 0;
  white-space: pre-line;
  font-size: 0.9rem;
  line-height: 1.3;
}

/* Tablet styles (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  .mainTitle {
    font-size: 2rem !important;
  }

  .subTitle {
    font-size: 1.5rem;
  }

  .description {
    font-size: 1.1rem;
  }

  .cardsContainer {
    gap: 15px;
  }

  .cardItem {
    width: 24%;
  }

  .textContainer {
    padding: 12px 15px;
    min-height: 70px;
  }

  .cardText {
    font-size: 0.8rem;
  }
}

/* Mobile styles (up to 767px) */
@media (max-width: 767px) {
  .historiqueSection {
    padding: 30px 0;
  }

  .container {
    padding: 0 10px;
  }

  .headerSection {
    margin-bottom: 30px;
  }

  .mainTitle {
    font-size: 1.8rem !important;
    margin-bottom: 15px !important;
    line-height: 1.3;
  }

  .subTitle {
    font-size: 1.3rem;
    margin-bottom: 15px;
  }

  .description {
    font-size: 1rem;
    padding: 0 10px;
  }

  .cardsContainer {
    flex-direction: column;
    gap: 25px;
    align-items: center;
  }

  .cardItem {
    width: 100%;
    max-width: 300px;
  }

  .imageContainer {
    max-width: 100%;
    width: 100%;
    margin: 0 auto 15px;
  }

  .textContainer {
    max-width: 280px;
    margin: 0 auto;
    padding: 12px 20px;
    min-height: 60px;
  }

  .cardText {
    font-size: 0.9rem;
    line-height: 1.2;
  }

  .cardText br {
    display: none;
  }
}

/* Small mobile styles (up to 480px) */
@media (max-width: 480px) {
  .mainTitle {
    font-size: 1.5rem !important;
  }

  .subTitle {
    font-size: 1.1rem;
  }

  .description {
    font-size: 0.95rem;
  }

  .cardItem {
    max-width: 300px;
  }

  .imageContainer {
    max-width: 100%;
    width: 100%;
  }

  .textContainer {
    max-width: 280px;
    padding: 10px 15px;
    min-height: 50px;
  }

  .cardText {
    font-size: 0.8rem;
  }
}

/* Hover effects for text containers */
.textContainer:hover .cardText {
  color: #fff !important;
}

.textContainer {
  transition: all 0.3s ease;
}

.cardText {
  transition: color 0.3s ease;
}
