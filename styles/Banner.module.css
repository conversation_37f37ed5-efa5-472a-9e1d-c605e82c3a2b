@font-face {
  font-family: 'Houschka';
  src: url('/assets/fonts/houschkahead.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

.bannerSection {
  min-height: 90vh;
  position: relative;
  background: linear-gradient(135deg, #1a365d 0%, #2D8BBA 100%);
  overflow: hidden;
  display: flex;
  align-items: center;
 
}

.backgroundPattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 150%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
              radial-gradient(circle at 80% -50%, rgba(255, 255, 255, 0.15) 0%, transparent 50%);
  animation: patternFloat 20s ease-in-out infinite;
}

.container {
  position: relative;
  z-index: 2;
  padding: 4rem 2rem;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

.content {
  position: relative;
  padding-right: 2rem;
  transform-style: preserve-3d;
  perspective: 1000px;
}

.title {
  font-size: 3.2rem;
  font-weight: 800;
  color: #ffffff;
  line-height: 1.2;
  margin-bottom: 2rem;
  position: relative;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-family: 'Houschka', sans-serif;
}

.typingText {
  display: inline-block;
  opacity: 0;
  animation: fadeIn 0.5s forwards;
  font-family: 'Houschka', sans-serif;
  font-size: 3.5rem;
  line-height: 1.3;
  letter-spacing: 0.02em;
}

.description {
  font-size: 1.25rem;
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 3rem;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease;
}

.imageContainer {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  transform: translateY(30px);
  opacity: 0;
  transition: all 0.8s ease;
}

.showImage {
  transform: translateY(0);
  opacity: 1;
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(26, 54, 93, 0.5) 0%, rgba(52, 133, 182, 0.3) 100%);
  z-index: 1;
}

.mainImage {
  width: 100%;
  height: auto;
  display: block;
  transform: scale(1);
  transition: transform 0.5s ease;
}

.imageContainer:hover .mainImage {
  transform: scale(1.05);
}

.button {
  display: inline-flex;
  align-items: center;
  padding: 1.2rem 3rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a365d;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.button:hover {
  background: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.cursor {
  display: inline-block;
  width: 3px;
  margin-left: 2px;
  opacity: 1;
  color: #ffffff;
  animation: blink 1s step-end infinite;
}

.cursorBlink {
  animation: blink 1s step-end infinite;
}

.highlightText {
  position: relative;
  display: inline-block;
  color: #ffffff;
}

.highlightText::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0.2rem;
  width: 100%;
  height: 0.5rem;
  background: rgba(255, 255, 255, 0.3);
  z-index: -1;
  transform: scaleX(0) translateZ(0);
  transform-origin: left;
  animation: expandHighlight 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards 1s;
}

.button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  transform: translateX(-100%) translateZ(0);
  transition: 0.6s;
}

.button:hover::before {
  transform: translateX(100%) translateZ(0);
}

.button svg {
  transition: transform 0.3s ease;
  margin-left: 8px;
}

.button:hover svg {
  transform: translateX(4px);
}

.shape {
  position: absolute;
  opacity: 0;
  filter: brightness(0) invert(1);
}

.shapeOne {
  top: -10%;
  right: -5%;
  width: 150px;
  animation: float 6s ease-in-out infinite, fadeIn 1s forwards 0.8s;
}

.shapeTwo {
  bottom: 15%;
  left: -8%;
  width: 120px;
  animation: float 8s ease-in-out infinite, fadeIn 1s forwards 1s;
}

.shapeThree {
  bottom: -10%;
  right: 10%;
  width: 100px;
  animation: float 7s ease-in-out infinite, fadeIn 1s forwards 1.2s;
}

/* Mobile Responsive Styles */
@media (max-width: 1200px) {
  .title {
    font-size: 3.5rem;
  }
  
  .typingText {
    font-size: 3.5rem;
  }
  
  .description {
    font-size: 1.2rem;
  }

  .container {
    padding: 3rem 1.5rem;
  }
}

@media (max-width: 991px) {
  .bannerSection {
    min-height: auto;
    padding-top: 60px;
  }

  .container {
    padding: 2rem 1.5rem;
  }

  .title {
    font-size: 3rem;
    text-align: center;
  }
  
  .typingText {
    font-size: 3rem;
  }
  
  .description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    text-align: center;
  }

  .content {
    padding-right: 0;
    margin-bottom: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .imageContainer {
    max-width: 90%;
    margin: 0 auto;
    margin-top: 2rem;
  }
}

@media (max-width: 768px) {
  .bannerSection {
    padding-top: 50px;
  }

  .container {
    padding: 2rem 1rem;
  }

  .title {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
  }
  
  .typingText {
    font-size: 2.5rem;
  }
  
  .description {
    font-size: 1rem;
    line-height: 1.6;
  }

  .button {
    padding: 1rem 2.5rem;
    font-size: 1rem;
    width: 100%;
    justify-content: center;
  }

  .imageContainer {
    max-width: 100%;
    margin-top: 1.5rem;
    border-radius: 12px;
  }
}

@media (max-width: 576px) {
  .bannerSection {
    padding-top: 40px;
  }

  .title {
    font-size: 2rem;
    margin-bottom: 1rem;
  }
  
  .typingText {
    font-size: 2rem;
    line-height: 1.2;
  }
  
  .description {
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
    padding: 0 1rem;
  }

  .button {
    padding: 0.9rem 2rem;
    font-size: 0.95rem;
  }

  .imageContainer {
    border-radius: 8px;
  }
}

@media (max-width: 375px) {
  .title {
    font-size: 1.8rem;
  }
  
  .typingText {
    font-size: 1.8rem;
  }
  
  .description {
    font-size: 0.9rem;
    padding: 0 0.5rem;
  }

  .button {
    padding: 0.8rem 1.8rem;
    font-size: 0.9rem;
  }
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateZ(0) translateX(0);
  }
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

@keyframes expandHighlight {
  to {
    transform: scaleX(1) translateZ(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) translateZ(0);
  }
  50% {
    transform: translateY(-20px) translateZ(20px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes blink {
  from, to {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

@keyframes patternFloat {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-20px) scale(1.05);
  }
}
