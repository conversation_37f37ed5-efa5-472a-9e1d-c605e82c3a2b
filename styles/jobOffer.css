.job-offer-header {
  background: linear-gradient(135deg, #2D8BBA 0%, #2d7299 100%);
  position: relative;
  overflow: hidden;
}

.job-offer-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: url('/images/pattern.png') repeat;
  opacity: 0.05;
}

.contract-badge {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

.contract-badge:hover {
  background: rgba(255, 255, 255, 0.2);
}

.recruiter-profile {
  position: relative;
  transition: transform 0.3s ease;
}

.recruiter-profile:hover {
  transform: translateY(-2px);
}

.recruiter-image {
  border: 3px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.recruiter-image:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.info-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 133, 182, 0.1);
  border-color: #2D8BBA;
}

.info-card i {
  font-size: 1.5rem;
  opacity: 0.9;
}

.section-title {
  position: relative;
  padding-bottom: 0.5rem;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: #2D8BBA;
  border-radius: 2px;
}

.content-section {
  animation: fadeIn 0.5s ease-out;
}

.apply-button {
  background: #2D8BBA;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.apply-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
}

.apply-button:hover::before {
  width: 300px;
  height: 300px;
}

.apply-button:hover {
  background: #2d7299;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(52, 133, 182, 0.2);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .job-offer-header {
    padding: 2rem 1rem;
  }
  
  .info-cards-grid {
    grid-template-columns: 1fr;
  }
  
  .recruiter-profile {
    margin-top: 1.5rem;
  }
}
