#!/bin/bash

# Service Management Script for Interim Frontend
# This script provides easy commands to manage the application service

APP_NAME="interim-frontend"
SERVICE_NAME="interim-frontend.service"

show_help() {
    echo "🔧 Interim Frontend Service Manager"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start       Start the application"
    echo "  stop        Stop the application"
    echo "  restart     Restart the application"
    echo "  status      Show application status"
    echo "  logs        Show application logs"
    echo "  build       Build the application"
    echo "  setup-pm2   Setup with PM2 (recommended)"
    echo "  setup-systemd Setup with systemd"
    echo "  help        Show this help message"
    echo ""
}

check_pm2() {
    if command -v pm2 &> /dev/null; then
        return 0
    else
        return 1
    fi
}

case "$1" in
    start)
        if check_pm2 && pm2 list | grep -q "$APP_NAME"; then
            echo "🚀 Starting with PM2..."
            pm2 start $APP_NAME
        elif systemctl list-unit-files | grep -q "$SERVICE_NAME"; then
            echo "🚀 Starting with systemd..."
            sudo systemctl start $SERVICE_NAME
        else
            echo "❌ No service configured. Run setup first."
            exit 1
        fi
        ;;
    stop)
        if check_pm2 && pm2 list | grep -q "$APP_NAME"; then
            echo "🛑 Stopping PM2 process..."
            pm2 stop $APP_NAME
        elif systemctl list-unit-files | grep -q "$SERVICE_NAME"; then
            echo "🛑 Stopping systemd service..."
            sudo systemctl stop $SERVICE_NAME
        else
            echo "❌ No running service found."
        fi
        ;;
    restart)
        if check_pm2 && pm2 list | grep -q "$APP_NAME"; then
            echo "🔄 Restarting PM2 process..."
            pm2 restart $APP_NAME
        elif systemctl list-unit-files | grep -q "$SERVICE_NAME"; then
            echo "🔄 Restarting systemd service..."
            sudo systemctl restart $SERVICE_NAME
        else
            echo "❌ No service configured. Run setup first."
            exit 1
        fi
        ;;
    status)
        echo "📊 Application Status:"
        echo ""
        if check_pm2 && pm2 list | grep -q "$APP_NAME"; then
            pm2 status $APP_NAME
        elif systemctl list-unit-files | grep -q "$SERVICE_NAME"; then
            sudo systemctl status $SERVICE_NAME --no-pager
        else
            echo "❌ No service configured."
        fi
        echo ""
        echo "🌐 Checking port 3001..."
        if netstat -tuln | grep -q ":3001 "; then
            echo "✅ Application is listening on port 3001"
        else
            echo "❌ No application listening on port 3001"
        fi
        ;;
    logs)
        if check_pm2 && pm2 list | grep -q "$APP_NAME"; then
            echo "📝 PM2 Logs (press Ctrl+C to exit):"
            pm2 logs $APP_NAME --lines 50
        elif systemctl list-unit-files | grep -q "$SERVICE_NAME"; then
            echo "📝 Systemd Logs (press Ctrl+C to exit):"
            sudo journalctl -u $SERVICE_NAME -f
        else
            echo "❌ No service configured."
        fi
        ;;
    build)
        echo "📦 Building application..."
        npm run build
        ;;
    setup-pm2)
        echo "🚀 Setting up with PM2..."
        chmod +x setup-pm2.sh
        ./setup-pm2.sh
        ;;
    setup-systemd)
        echo "🚀 Setting up with systemd..."
        chmod +x setup-service.sh
        sudo ./setup-service.sh
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
