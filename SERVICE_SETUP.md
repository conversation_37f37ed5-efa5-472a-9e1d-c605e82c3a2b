# 🚀 Interim Frontend Service Setup

This guide will help you set up the Interim Frontend application as a persistent service that runs on port 3001 and automatically restarts.

## 📋 Prerequisites

- Node.js and npm installed
- Root access to the server
- Application built and tested

## 🎯 Quick Setup (Recommended - PM2)

### Option 1: Using PM2 (Recommended)

```bash
# Make scripts executable
chmod +x setup-pm2.sh manage-service.sh

# Run the PM2 setup
./setup-pm2.sh
```

### Option 2: Using systemd

```bash
# Make scripts executable
chmod +x setup-service.sh manage-service.sh

# Run the systemd setup (requires sudo)
sudo ./setup-service.sh
```

## 🔧 Service Management

Use the management script for easy service control:

```bash
# Make management script executable
chmod +x manage-service.sh

# Start the service
./manage-service.sh start

# Stop the service
./manage-service.sh stop

# Restart the service
./manage-service.sh restart

# Check status
./manage-service.sh status

# View logs
./manage-service.sh logs

# Build application
./manage-service.sh build
```

## 📊 Manual Commands

### PM2 Commands
```bash
# Start with PM2
pm2 start ecosystem.config.js

# View status
pm2 status

# View logs
pm2 logs interim-frontend

# Restart
pm2 restart interim-frontend

# Stop
pm2 stop interim-frontend

# Monitor
pm2 monit
```

### Systemd Commands
```bash
# Start service
sudo systemctl start interim-frontend

# Stop service
sudo systemctl stop interim-frontend

# Restart service
sudo systemctl restart interim-frontend

# Check status
sudo systemctl status interim-frontend

# View logs
sudo journalctl -u interim-frontend -f

# Enable auto-start on boot
sudo systemctl enable interim-frontend
```

## 🌐 Accessing the Application

Once the service is running, your application will be available at:
- **Local**: http://localhost:3001
- **Server IP**: http://YOUR_SERVER_IP:3001

## 🔍 Troubleshooting

### Check if port 3001 is in use:
```bash
netstat -tuln | grep :3001
```

### Check application logs:
```bash
# PM2 logs
pm2 logs interim-frontend

# Systemd logs
sudo journalctl -u interim-frontend -n 50
```

### Rebuild application:
```bash
npm run build
```

### Check Node.js and npm versions:
```bash
node --version
npm --version
```

## 🔒 Security Notes

- The service runs as root user (as configured)
- Make sure your firewall allows traffic on port 3001 if accessing externally
- Consider using a reverse proxy (nginx) for production deployments

## 📝 Configuration Files

- `ecosystem.config.js` - PM2 configuration
- `interim-frontend.service` - Systemd service configuration
- `package.json` - Application configuration (port 3001 is set here)

## 🆘 Support

If you encounter issues:

1. Check the logs using the commands above
2. Ensure all dependencies are installed
3. Verify the application builds successfully
4. Check if port 3001 is available

## ✅ Verification

After setup, verify everything is working:

```bash
# Check service status
./manage-service.sh status

# Test the application
curl http://localhost:3001

# Check if it's accessible from outside (if needed)
curl http://YOUR_SERVER_IP:3001
```

The service will automatically:
- ✅ Start on system boot
- ✅ Restart if the application crashes
- ✅ Log all output for debugging
- ✅ Run on port 3001 as configured
