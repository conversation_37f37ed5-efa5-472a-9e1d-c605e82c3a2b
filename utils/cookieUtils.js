// utils/cookieUtils.js
export const getCookieConsent = () => {
  if (typeof window === 'undefined') return null;
  
  try {
    const consent = localStorage.getItem('cookieConsent');
    return consent ? JSON.parse(consent) : null;
  } catch (error) {
    console.error('Error reading cookie consent:', error);
    return null;
  }
};

export const hasCookieConsent = (category = null) => {
  const consent = getCookieConsent();
  if (!consent) return false;
  
  if (category) {
    return consent[category] === true;
  }
  
  return true; // User has given some form of consent
};

export const initializeAnalytics = () => {
  if (!hasCookieConsent('analytics')) return;
  
  // Initialize Google Analytics or other analytics tools
  // Example:
  // gtag('config', 'GA_MEASUREMENT_ID');
  console.log('Analytics initialized based on user consent');
};

export const initializeMarketing = () => {
  if (!hasCookieConsent('marketing')) return;
  
  // Initialize marketing/advertising cookies
  // Example: Facebook Pixel, Google Ads, etc.
  console.log('Marketing cookies initialized based on user consent');
};

export const clearNonEssentialCookies = () => {
  const consent = getCookieConsent();
  if (!consent) return;
  
  // Clear analytics cookies if not consented
  if (!consent.analytics) {
    // Clear Google Analytics cookies
    const analyticsCookies = document.cookie.split(';')
      .filter(cookie => cookie.trim().startsWith('_ga') || cookie.trim().startsWith('_gid'));
    
    analyticsCookies.forEach(cookie => {
      const cookieName = cookie.split('=')[0].trim();
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    });
  }
  
  // Clear marketing cookies if not consented
  if (!consent.marketing) {
    // Clear marketing cookies (Facebook, Google Ads, etc.)
    const marketingCookies = ['_fbp', '_fbc', 'fr'];
    marketingCookies.forEach(cookieName => {
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    });
  }
};

export const checkConsentExpiry = () => {
  const consentDate = localStorage.getItem('cookieConsentDate');
  if (!consentDate) return false;
  
  const consentTime = new Date(consentDate);
  const currentTime = new Date();
  const timeDiff = currentTime.getTime() - consentTime.getTime();
  const daysDiff = timeDiff / (1000 * 3600 * 24);
  
  // GDPR recommends reconfirming consent every 12 months
  return daysDiff > 365;
};

export const resetCookieConsent = () => {
  localStorage.removeItem('cookieConsent');
  localStorage.removeItem('cookieConsentDate');
  clearNonEssentialCookies();
};
