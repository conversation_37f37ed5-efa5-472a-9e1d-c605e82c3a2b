"use client";
import { useEffect, useState } from "react";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import axios from "axios";
import Layout from "@/components/layout/Layout";
import { jwtDecode } from "jwt-decode";
import styles from "@/styles/Profile.module.css";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUser,
  faEnvelope,
  faPhone,
  faEdit,
  faBriefcase,
  faBookmark,
  faFileAlt,
  faBell,
  faUserCircle,
  faList,
  faCog,
  faRocket,
  faSearch,
  faCheckCircle,
  faEye,
  faEyeSlash,
  faTrash,
  faKey
} from "@fortawesome/free-solid-svg-icons";
import Link from "next/link";

const Profile = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState("dashboard");

  // New states for enhanced functionality
  const [cvLibrary, setCvLibrary] = useState([]);
  const [applications, setApplications] = useState([]);
  const [matchedJobs, setMatchedJobs] = useState([]);
  const [cvLibraryLoading, setCvLibraryLoading] = useState(false);
  const [applicationsLoading, setApplicationsLoading] = useState(false);
  const [jobMatchLoading, setJobMatchLoading] = useState(false);
  const [uploadingCV, setUploadingCV] = useState(false);
  const [showUploadForm, setShowUploadForm] = useState(false);

  // Password change states
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [changingPassword, setChangingPassword] = useState(false);

  // Delete account states
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const token = localStorage.getItem("token");
        if (!token) {
          setError("No token found");
          window.location.href = "/signin";
          setLoading(false);
          return;
        }

        const decoded = jwtDecode(token);
        const userId = decoded.id;

        const config = {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };

        const response = await axios.get(
          `http://82.165.144.72:5000/api/users/${userId}`,
          config
        );
        setUser(response.data);
      } catch (err) {
        setError("Failed to fetch user data");
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // Fetch CV Library
  const fetchCVLibrary = async () => {
    setCvLibraryLoading(true);
    try {
      const token = localStorage.getItem("token");
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      const response = await axios.get("http://82.165.144.72:5000/api/cv-library?active=true", config);
      setCvLibrary(response.data.cvs || []);
    } catch (err) {
      console.error("Failed to fetch CV library:", err);
    } finally {
      setCvLibraryLoading(false);
    }
  };

  // Fetch Applications
  const fetchApplications = async () => {
    setApplicationsLoading(true);
    try {
      const token = localStorage.getItem("token");
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      const response = await axios.get("http://82.165.144.72:5000/api/applications/my-applications?page=1&limit=50", config);
      setApplications(response.data.applications || []);
    } catch (err) {
      console.error("Failed to fetch applications:", err);
    } finally {
      setApplicationsLoading(false);
    }
  };

  // Fetch Matched Jobs
  const fetchMatchedJobs = async () => {
    setJobMatchLoading(true);
    try {
      const token = localStorage.getItem("token");
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      const response = await axios.get("http://82.165.144.72:5000/api/job-match/matched-jobs?minScore=30&page=1&limit=20", config);
      setMatchedJobs(response.data.matchedJobs || []);
    } catch (err) {
      console.error("Failed to fetch matched jobs:", err);
    } finally {
      setJobMatchLoading(false);
    }
  };

  // Fetch data when tab changes
  useEffect(() => {
    if (activeTab === "cv-library") {
      fetchCVLibrary();
    } else if (activeTab === "applications") {
      fetchApplications();
    } else if (activeTab === "job-match") {
      fetchMatchedJobs();
    }
  }, [activeTab]);

  // Upload CV to Library
  const uploadCVToLibrary = async (formData) => {
    setUploadingCV(true);
    try {
      const token = localStorage.getItem("token");

      const response = await fetch("http://82.165.144.72:5000/api/cv-library/upload", {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        toast.success("CV ajouté à votre bibliothèque avec succès !");
        setShowUploadForm(false);
        fetchCVLibrary(); // Refresh the CV library
      } else {
        const error = await response.json();
        toast.error(error.message || "Erreur lors de l'ajout du CV");
      }
    } catch (err) {
      console.error("Failed to upload CV:", err);
      toast.error("Erreur lors de l'ajout du CV");
    } finally {
      setUploadingCV(false);
    }
  };

  // Change Password Function
  const changePassword = async (e) => {
    e.preventDefault();

    // Validation
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error("Les nouveaux mots de passe ne correspondent pas");
      return;
    }

    if (passwordData.newPassword.length < 6) {
      toast.error("Le nouveau mot de passe doit contenir au moins 6 caractères");
      return;
    }

    setChangingPassword(true);

    try {
      const token = localStorage.getItem("token");
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
      };

      const response = await axios.put(
        "http://82.165.144.72:5000/change-password",
        {
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword
        },
        config
      );

      if (response.data) {
        toast.success("Mot de passe modifié avec succès !");
        setShowPasswordForm(false);
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      }
    } catch (error) {
      console.error('Error changing password:', error);

      if (error.response && error.response.data && error.response.data.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Erreur lors de la modification du mot de passe");
      }
    } finally {
      setChangingPassword(false);
    }
  };

  if (loading) return (
    <Layout headerStyle={2} footerStyle={3}>
      <div className={styles.loader}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Chargement...</span>
        </div>
      </div>
    </Layout>
  );
  
  if (error) return (
    <Layout headerStyle={2} footerStyle={3}>
      <div className={styles.error}>
        <div className="alert alert-danger text-center mb-0" role="alert">
          {error}
        </div>
      </div>
    </Layout>
  );

  return (
    <Layout headerStyle={2} footerStyle={3}>
      <div className={styles.profileContainer} style={{marginTop: "10%", marginBottom: "10%"}}>
        <ToastContainer position="top-right" autoClose={3000} />
        <div className={styles.profileHeader}>
          <h1 className={styles.profileTitle}>
            <FontAwesomeIcon icon={faUserCircle} className="me-2" />
            Mon compte
          </h1>
        </div>

        <div className={styles.sectionContainer}>
          <h2 className={styles.sectionTitle}>
            <FontAwesomeIcon icon={faUser} className="me-2" />
            Mon Espace Personnel
          </h2>
          
          <div className={styles.cardsGrid}>
            {/* Card 1: Emplois correspondant à mon profil */}
            <Link href="/profile/job-match" passHref>
              <div className={styles.card} style={{ cursor: 'pointer' }}>
                <div className={styles.cardIcon}>
                  <FontAwesomeIcon icon={faSearch} />
                </div>
                <h3 className={styles.cardTitle}>Emplois correspondant à mon profil</h3>
                <p className={styles.cardText}>Testez Job Match, notre outil de matching intelligent</p>
                <div className={styles.cardLink}>Explorer</div>
              </div>
            </Link>

            {/* Card 2: Mes Candidatures */}
            <Link href="/profile/applications" passHref>
              <div className={styles.card} style={{ cursor: 'pointer' }}>
                <div className={styles.cardIcon}>
                  <FontAwesomeIcon icon={faList} />
                </div>
                <h3 className={styles.cardTitle}>Mes Candidatures</h3>
                <p className={styles.cardText}>Affichez les offres auxquelles vous avez postulées</p>
                <div className={styles.cardLink}>Voir l'historique</div>
              </div>
            </Link>

            {/* Card 3: Mes Alertes Emploi */}
            <Link href="/profile/job-alerts" passHref>
              <div className={styles.card} style={{ cursor: 'pointer' }}>
                <div className={styles.cardIcon}>
                  <FontAwesomeIcon icon={faBell} />
                </div>
                <h3 className={styles.cardTitle}>Mes Alertes Emploi</h3>
                <p className={styles.cardText}>Configurez des alertes emails pour recevoir les dernières offres disponibles</p>
                <div className={styles.cardLink}>Configurer</div>
              </div>
            </Link>

            {/* Card 4: Informations Personnelles */}
            <Link href="/profile/personal-info" passHref>
              <div className={styles.card} style={{ cursor: 'pointer' }}>
                <div className={styles.cardIcon}>
                  <FontAwesomeIcon icon={faUserCircle} />
                </div>
                <h3 className={styles.cardTitle}>Informations Personnelles</h3>
                <p className={styles.cardText}>Gardez vos coordonnées à jour</p>
                <div className={styles.cardLink}>Modifier</div>
              </div>
            </Link>

            {/* Card 5: Mon CV */}
            <Link href="/profile/cv-library" passHref>
              <div className={styles.card} style={{ cursor: 'pointer' }}>
                <div className={styles.cardIcon}>
                  <FontAwesomeIcon icon={faFileAlt} />
                </div>
                <h3 className={styles.cardTitle}>Mon CV</h3>
                <p className={styles.cardText}>Gérez votre bibliothèque de CVs</p>
                <div className={styles.cardLink}>Gérer mes CVs</div>
              </div>
            </Link>

            {/* Card 6: Paramètres du compte */}
            <Link href="/profile/account-settings" passHref>
              <div className={styles.card} style={{ cursor: 'pointer' }}>
                <div className={styles.cardIcon}>
                  <FontAwesomeIcon icon={faCog} />
                </div>
                <h3 className={styles.cardTitle}>Paramètres du compte</h3>
                <p className={styles.cardText}>Mettez à jour votre mot de passe et gérez votre compte</p>
                <div className={styles.cardLink}>Modifier</div>
              </div>
            </Link>
          </div>
        </div>

        {/* CV Library Management */}
        {activeTab === "cv-library" && (
          <div className={styles.formContainer}>
            <div className={styles.cardWithForm}>
              <div className={styles.cardHeader}>
                <h2 className={styles.cardTitle}>
                  <FontAwesomeIcon icon={faFileAlt} className="me-2" />
                  Ma Bibliothèque de CVs
                </h2>
                <button
                  className={styles.backButton}
                  onClick={() => setActiveTab("dashboard")}
                >
                  Retour
                </button>
              </div>

              {cvLibraryLoading ? (
                <div style={{ textAlign: 'center', padding: '40px' }}>
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Chargement...</span>
                  </div>
                </div>
              ) : (
                <div>
                  {/* Upload CV Button */}
                  <div style={{ marginBottom: '20px', textAlign: 'center' }}>
                    <button
                      onClick={() => setShowUploadForm(!showUploadForm)}
                      style={{
                        padding: '10px 20px',
                        backgroundColor: '#2D8BBA',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        cursor: 'pointer',
                        fontSize: '14px'
                      }}
                    >
                      {showUploadForm ? 'Annuler' : '+ Ajouter un nouveau CV'}
                    </button>
                  </div>

                  {/* Upload Form */}
                  {showUploadForm && (
                    <div style={{
                      border: '1px solid #ddd',
                      borderRadius: '8px',
                      padding: '20px',
                      marginBottom: '20px',
                      backgroundColor: '#f9f9f9'
                    }}>
                      <h4 style={{ marginBottom: '15px' }}>Ajouter un CV à votre bibliothèque</h4>
                      <form onSubmit={(e) => {
                        e.preventDefault();
                        const formData = new FormData(e.target);
                        uploadCVToLibrary(formData);
                      }}>
                        <div style={{ marginBottom: '15px' }}>
                          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                            Fichier CV (PDF, DOC, DOCX) *
                          </label>
                          <input
                            type="file"
                            name="cv"
                            accept=".pdf,.doc,.docx"
                            required
                            style={{ width: '100%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
                          />
                        </div>
                        <div style={{ marginBottom: '15px' }}>
                          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                            Titre *
                          </label>
                          <input
                            type="text"
                            name="title"
                            placeholder="Ex: CV Senior Developer"
                            required
                            style={{ width: '100%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
                          />
                        </div>
                        <div style={{ marginBottom: '15px' }}>
                          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                            Description
                          </label>
                          <textarea
                            name="description"
                            placeholder="Description de ce CV..."
                            rows={3}
                            style={{ width: '100%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
                          />
                        </div>
                        <div style={{ marginBottom: '15px' }}>
                          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                            Tags (séparés par des virgules)
                          </label>
                          <input
                            type="text"
                            name="tags"
                            placeholder="javascript,react,senior"
                            style={{ width: '100%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
                          />
                        </div>
                        <div style={{ marginBottom: '15px' }}>
                          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                            Adapté pour (séparés par des virgules)
                          </label>
                          <input
                            type="text"
                            name="suitableFor"
                            placeholder="CDI,CDD"
                            style={{ width: '100%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
                          />
                        </div>
                        <div style={{ marginBottom: '15px' }}>
                          <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <input
                              type="checkbox"
                              name="isDefault"
                              value="true"
                            />
                            Définir comme CV par défaut
                          </label>
                        </div>
                        <div style={{ display: 'flex', gap: '10px' }}>
                          <button
                            type="submit"
                            disabled={uploadingCV}
                            style={{
                              padding: '10px 20px',
                              backgroundColor: uploadingCV ? '#ccc' : '#28a745',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: uploadingCV ? 'not-allowed' : 'pointer'
                            }}
                          >
                            {uploadingCV ? 'Ajout en cours...' : 'Ajouter le CV'}
                          </button>
                          <button
                            type="button"
                            onClick={() => setShowUploadForm(false)}
                            style={{
                              padding: '10px 20px',
                              backgroundColor: '#6c757d',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: 'pointer'
                            }}
                          >
                            Annuler
                          </button>
                        </div>
                      </form>
                    </div>
                  )}

                  {cvLibrary.length > 0 ? (
                    <div style={{ display: 'grid', gap: '15px' }}>
                      {cvLibrary.map((cv) => (
                        <div key={cv._id} style={{
                          border: '1px solid #ddd',
                          borderRadius: '8px',
                          padding: '20px',
                          backgroundColor: cv.isDefault ? '#f0f8ff' : '#fff'
                        }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
                            <div style={{ flex: 1 }}>
                              <h4 style={{ margin: '0 0 8px 0', color: '#2D8BBA' }}>
                                {cv.title}
                                {cv.isDefault && <span style={{ fontSize: '14px', marginLeft: '8px' }}>⭐ Par défaut</span>}
                              </h4>
                              <p style={{ margin: '0 0 8px 0', color: '#666' }}>{cv.description}</p>
                              <div style={{ fontSize: '14px', color: '#888' }}>
                                <span>Utilisé {cv.timesUsed} fois</span>
                                {cv.lastUsed && (
                                  <span> • Dernière utilisation: {new Date(cv.lastUsed).toLocaleDateString('fr-FR')}</span>
                                )}
                              </div>
                              <div style={{ fontSize: '14px', color: '#888', marginTop: '4px' }}>
                                {cv.originalName && (
                                  <span>Fichier: {cv.originalName}</span>
                                )}
                                {cv.tags && cv.tags.length > 0 && (
                                  <span> • Tags: {cv.tags.join(', ')}</span>
                                )}
                              </div>
                              <div style={{ fontSize: '14px', color: '#888', marginTop: '4px' }}>
                                {cv.suitableFor && cv.suitableFor.length > 0 && (
                                  <span>Adapté pour: {cv.suitableFor.join(', ')}</span>
                                )}
                                <span style={{
                                  marginLeft: cv.suitableFor && cv.suitableFor.length > 0 ? '8px' : '0',
                                  padding: '2px 6px',
                                  borderRadius: '4px',
                                  fontSize: '12px',
                                  backgroundColor: cv.isActive ? '#d1edff' : '#f8d7da',
                                  color: cv.isActive ? '#0c5460' : '#721c24'
                                }}>
                                  {cv.isActive ? 'Actif' : 'Inactif'}
                                </span>
                              </div>
                              <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
                                Créé le {new Date(cv.createdAt).toLocaleDateString('fr-FR')}
                              </div>
                            </div>
                            <div style={{ display: 'flex', gap: '8px', marginLeft: '16px' }}>
                              <a
                                href={cv.fileUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{
                                  padding: '6px 12px',
                                  backgroundColor: '#2D8BBA',
                                  color: 'white',
                                  textDecoration: 'none',
                                  borderRadius: '4px',
                                  fontSize: '14px'
                                }}
                              >
                                Voir
                              </a>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
                      <FontAwesomeIcon icon={faFileAlt} size="3x" style={{ marginBottom: '16px', opacity: 0.3 }} />
                      <h4>Aucun CV dans votre bibliothèque</h4>
                      <p>Commencez par postuler à une offre pour créer votre premier CV.</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Applications History */}
        {activeTab === "applications" && (
          <div className={styles.formContainer}>
            <div className={styles.cardWithForm}>
              <div className={styles.cardHeader}>
                <h2 className={styles.cardTitle}>
                  <FontAwesomeIcon icon={faList} className="me-2" />
                  Mes Candidatures
                </h2>
                <button
                  className={styles.backButton}
                  onClick={() => setActiveTab("dashboard")}
                >
                  Retour
                </button>
              </div>

              {applicationsLoading ? (
                <div style={{ textAlign: 'center', padding: '40px' }}>
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Chargement...</span>
                  </div>
                </div>
              ) : (
                <div>
                  {applications.length > 0 ? (
                    <div style={{ display: 'grid', gap: '15px' }}>
                      {applications.map((app) => (
                        <div key={app._id} style={{
                          border: '1px solid #ddd',
                          borderRadius: '8px',
                          padding: '20px',
                          backgroundColor: '#fff'
                        }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
                            <div style={{ flex: 1 }}>
                              <h4 style={{ margin: '0 0 8px 0', color: '#2D8BBA' }}>
                                {app.postId?.descriptionDuPoste || 'Poste non spécifié'}
                              </h4>
                              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>
                                <span>{app.postId?.agence || 'Agence'} • {app.postId?.ville || 'Ville'}</span>
                                <span> • {app.postId?.contract || 'Type de contrat'}</span>
                              </div>
                              <div style={{ fontSize: '14px', color: '#888' }}>
                                <span>Candidature envoyée le {new Date(app.createdAt).toLocaleDateString('fr-FR')}</span>
                                <span> • Type: {app.cvType === 'upload' ? 'CV téléchargé' : app.cvType === 'form' ? 'Données formulaire' : 'CV bibliothèque'}</span>
                                {app.source && <span> • Source: {app.source === 'direct' ? 'Candidature directe' : app.source === 'library' ? 'Bibliothèque CV' : app.source}</span>}
                              </div>
                              {app.cvFile && (
                                <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
                                  Fichier: {app.cvFile.originalName}
                                </div>
                              )}
                              {app.cvLibraryId && (
                                <div style={{ fontSize: '12px', color: '#2D8BBA', marginTop: '4px' }}>
                                  📁 CV utilisé depuis la bibliothèque
                                </div>
                              )}
                            </div>
                            <div style={{
                              padding: '4px 12px',
                              borderRadius: '12px',
                              fontSize: '12px',
                              fontWeight: 'bold',
                              backgroundColor: app.status === 'En attente' ? '#fff3cd' :
                                             app.status === 'Accepté' ? '#d1edff' :
                                             app.status === 'Refusé' ? '#f8d7da' : '#e2e3e5',
                              color: app.status === 'En attente' ? '#856404' :
                                     app.status === 'Accepté' ? '#0c5460' :
                                     app.status === 'Refusé' ? '#721c24' : '#383d41'
                            }}>
                              {app.status || 'En attente'}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
                      <FontAwesomeIcon icon={faList} size="3x" style={{ marginBottom: '16px', opacity: 0.3 }} />
                      <h4>Aucune candidature trouvée</h4>
                      <p>Vous n'avez pas encore postulé à des offres d'emploi.</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Job Match */}
        {activeTab === "job-match" && (
          <div className={styles.formContainer}>
            <div className={styles.cardWithForm}>
              <div className={styles.cardHeader}>
                <h2 className={styles.cardTitle}>
                  <FontAwesomeIcon icon={faSearch} className="me-2" />
                  Emplois correspondant à mon profil
                </h2>
                <button
                  className={styles.backButton}
                  onClick={() => setActiveTab("dashboard")}
                >
                  Retour
                </button>
              </div>

              {jobMatchLoading ? (
                <div style={{ textAlign: 'center', padding: '40px' }}>
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Chargement...</span>
                  </div>
                </div>
              ) : (
                <div>
                  {matchedJobs.length > 0 ? (
                    <div style={{ display: 'grid', gap: '15px' }}>
                      {matchedJobs.map((job) => (
                        <div key={job._id} style={{
                          border: '1px solid #ddd',
                          borderRadius: '8px',
                          padding: '20px',
                          backgroundColor: '#fff'
                        }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
                            <div style={{ flex: 1 }}>
                              <h4 style={{ margin: '0 0 8px 0', color: '#2D8BBA' }}>
                                {job.descriptionDuPoste}
                              </h4>
                              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>
                                <span>{job.agence} • {job.ville}</span>
                                <span> • {job.contract}</span>
                              </div>
                              <div style={{ fontSize: '14px', color: '#888' }}>
                                <span>Publié le {new Date(job.createdAt).toLocaleDateString('fr-FR')}</span>
                                {job.assignedRecruiter && (
                                  <span> • Recruteur: {job.assignedRecruiter.name}</span>
                                )}
                              </div>
                            </div>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                              <div style={{
                                padding: '8px 12px',
                                borderRadius: '12px',
                                fontSize: '14px',
                                fontWeight: 'bold',
                                backgroundColor: job.matchingScore >= 70 ? '#d1edff' : job.matchingScore >= 50 ? '#fff3cd' : '#f8d7da',
                                color: job.matchingScore >= 70 ? '#0c5460' : job.matchingScore >= 50 ? '#856404' : '#721c24'
                              }}>
                                {job.matchingScore}% match
                              </div>
                              <Link href={`/${job.uuid}`} style={{
                                padding: '8px 16px',
                                backgroundColor: '#2D8BBA',
                                color: 'white',
                                textDecoration: 'none',
                                borderRadius: '4px',
                                fontSize: '14px'
                              }}>
                                Voir l'offre
                              </Link>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
                      <FontAwesomeIcon icon={faSearch} size="3x" style={{ marginBottom: '16px', opacity: 0.3 }} />
                      <h4>Aucun emploi correspondant trouvé</h4>
                      <p>Complétez votre profil pour obtenir de meilleures recommandations.</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Job Alerts */}
        {activeTab === "job-alerts" && (
          <div className={styles.formContainer}>
            <div className={styles.cardWithForm}>
              <div className={styles.cardHeader}>
                <h2 className={styles.cardTitle}>
                  <FontAwesomeIcon icon={faBell} className="me-2" />
                  Mes Alertes Emploi
                </h2>
                <button
                  className={styles.backButton}
                  onClick={() => setActiveTab("dashboard")}
                >
                  Retour
                </button>
              </div>

              <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
                <FontAwesomeIcon icon={faBell} size="3x" style={{ marginBottom: '16px', opacity: 0.3 }} />
                <h4>Alertes Emploi</h4>
                <p>Cette fonctionnalité sera bientôt disponible.</p>
                <p style={{ fontSize: '14px' }}>Vous pourrez configurer des alertes pour recevoir les dernières offres par email.</p>
              </div>
            </div>
          </div>
        )}

        {/* Account Settings */}
        {activeTab === "account-settings" && (
          <div className={styles.formContainer}>
            <div className={styles.cardWithForm}>
              <div className={styles.cardHeader}>
                <h2 className={styles.cardTitle}>
                  <FontAwesomeIcon icon={faCog} className="me-2" />
                  Paramètres du compte
                </h2>
                <button
                  className={styles.backButton}
                  onClick={() => setActiveTab("dashboard")}
                >
                  Retour
                </button>
              </div>

              <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
                <FontAwesomeIcon icon={faCog} size="3x" style={{ marginBottom: '16px', opacity: 0.3 }} />
                <h4>Paramètres du compte</h4>
                <p>Cette fonctionnalité sera bientôt disponible.</p>
                <p style={{ fontSize: '14px' }}>Vous pourrez modifier votre mot de passe et gérer vos préférences de compte.</p>
              </div>
            </div>
          </div>
        )}

        {activeTab === "personalInfo" && (
          <div className={styles.formContainer}>
            <div className={styles.cardWithForm}>
              <div className={styles.cardHeader}>
                <h2 className={styles.cardTitle}>
                  <FontAwesomeIcon icon={faEdit} className="me-2" />
                  Modifier mes informations personnelles
                </h2>
                <button 
                  className={styles.backButton}
                  onClick={() => setActiveTab("dashboard")}
                >
                  Retour
                </button>
              </div>
              <form onSubmit={(e) => {
                e.preventDefault();
                if (user) {
                  const token = localStorage.getItem("token");
                  const decoded = jwtDecode(token);
                  const userId = decoded.id;
                  const config = {
                    headers: {
                      Authorization: `Bearer ${token}`,
                    },
                  };
                  axios.put(
                    `http://82.165.144.72:5000/api/users/${userId}`,
                    user,
                    config
                  )
                  .then(() => {
                    toast.success("Profil mis à jour avec succès!");
                    setActiveTab("dashboard");
                  })
                  .catch(error => {
                    console.error("Error updating user:", error);
                    toast.error("Échec de la mise à jour du profil");
                  });
                }
              }}>
                <div className={styles.formGroup}>
                  <label htmlFor="firstName" className={styles.formLabel}>
                    <FontAwesomeIcon icon={faUser} className="me-2" />
                    Prénom:
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    className={styles.formInput}
                    value={user?.firstName || ""}
                    onChange={(e) => setUser({...user, firstName: e.target.value})}
                    placeholder="Entrez votre prénom"
                  />
                </div>
                <div className={styles.formGroup}>
                  <label htmlFor="lastName" className={styles.formLabel}>
                    <FontAwesomeIcon icon={faUser} className="me-2" />
                    Nom de famille:
                  </label>
                  <input
                    type="text"
                    id="lastName"
                    name="lastName"
                    className={styles.formInput}
                    value={user?.lastName || ""}
                    onChange={(e) => setUser({...user, lastName: e.target.value})}
                    placeholder="Entrez votre nom de famille"
                  />
                </div>
                <div className={styles.formGroup}>
                  <label htmlFor="telephone" className={styles.formLabel}>
                    <FontAwesomeIcon icon={faPhone} className="me-2" />
                    Téléphone:
                  </label>
                  <input
                    type="text"
                    id="telephone"
                    name="telephone"
                    className={styles.formInput}
                    value={user?.telephone || ""}
                    onChange={(e) => setUser({...user, telephone: e.target.value})}
                    placeholder="Entrez votre numéro de téléphone"
                  />
                </div>
                <div className={styles.formGroup}>
                  <label htmlFor="email" className={styles.formLabel}>
                    <FontAwesomeIcon icon={faEnvelope} className="me-2" />
                    E-mail:
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    className={styles.formInput}
                    value={user?.email || ""}
                    onChange={(e) => setUser({...user, email: e.target.value})}
                    placeholder="Entrez votre adresse e-mail"
                  />
                </div>
                <button type="submit" className={styles.submitButton}>
                  Mettre à jour le profil
                </button>
              </form>
            </div>
          </div>
        )}

        {/* Account Settings */}
        {activeTab === "account-settings" && (
          <div className={styles.formContainer}>
            <div className={styles.cardWithForm}>
              <div className={styles.cardHeader}>
                <h2 className={styles.cardTitle}>
                  <FontAwesomeIcon icon={faCog} className="me-2" />
                  Paramètres du compte
                </h2>
                <button
                  className={styles.backButton}
                  onClick={() => setActiveTab("dashboard")}
                >
                  Retour
                </button>
              </div>

              <div style={{ display: 'grid', gap: '20px' }}>
                {/* Change Password Section */}
                <div style={{
                  border: '1px solid #ddd',
                  borderRadius: '8px',
                  padding: '20px',
                  backgroundColor: '#fff'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                    <h3 style={{ margin: 0, color: '#2D8BBA' }}>
                      <FontAwesomeIcon icon={faKey} className="me-2" />
                      Changer le mot de passe
                    </h3>
                    <button
                      onClick={() => setShowPasswordForm(!showPasswordForm)}
                      style={{
                        padding: '8px 16px',
                        backgroundColor: '#2D8BBA',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '14px'
                      }}
                    >
                      {showPasswordForm ? 'Annuler' : 'Modifier'}
                    </button>
                  </div>

                  {showPasswordForm && (
                    <form onSubmit={changePassword}>
                      <div style={{ marginBottom: '15px' }}>
                        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                          Mot de passe actuel *
                        </label>
                        <div style={{ position: 'relative' }}>
                          <input
                            type={showCurrentPassword ? 'text' : 'password'}
                            value={passwordData.currentPassword}
                            onChange={(e) => setPasswordData({...passwordData, currentPassword: e.target.value})}
                            required
                            style={{
                              width: '100%',
                              padding: '10px 40px 10px 10px',
                              border: '1px solid #ddd',
                              borderRadius: '4px',
                              fontSize: '14px'
                            }}
                            placeholder="Entrez votre mot de passe actuel"
                          />
                          <button
                            type="button"
                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                            style={{
                              position: 'absolute',
                              right: '10px',
                              top: '50%',
                              transform: 'translateY(-50%)',
                              background: 'none',
                              border: 'none',
                              cursor: 'pointer',
                              color: '#666'
                            }}
                          >
                            <FontAwesomeIcon icon={showCurrentPassword ? faEyeSlash : faEye} />
                          </button>
                        </div>
                      </div>

                      <div style={{ marginBottom: '15px' }}>
                        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                          Nouveau mot de passe *
                        </label>
                        <div style={{ position: 'relative' }}>
                          <input
                            type={showNewPassword ? 'text' : 'password'}
                            value={passwordData.newPassword}
                            onChange={(e) => setPasswordData({...passwordData, newPassword: e.target.value})}
                            required
                            style={{
                              width: '100%',
                              padding: '10px 40px 10px 10px',
                              border: '1px solid #ddd',
                              borderRadius: '4px',
                              fontSize: '14px'
                            }}
                            placeholder="Entrez votre nouveau mot de passe (min. 6 caractères)"
                          />
                          <button
                            type="button"
                            onClick={() => setShowNewPassword(!showNewPassword)}
                            style={{
                              position: 'absolute',
                              right: '10px',
                              top: '50%',
                              transform: 'translateY(-50%)',
                              background: 'none',
                              border: 'none',
                              cursor: 'pointer',
                              color: '#666'
                            }}
                          >
                            <FontAwesomeIcon icon={showNewPassword ? faEyeSlash : faEye} />
                          </button>
                        </div>
                      </div>

                      <div style={{ marginBottom: '20px' }}>
                        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                          Confirmer le nouveau mot de passe *
                        </label>
                        <div style={{ position: 'relative' }}>
                          <input
                            type={showConfirmPassword ? 'text' : 'password'}
                            value={passwordData.confirmPassword}
                            onChange={(e) => setPasswordData({...passwordData, confirmPassword: e.target.value})}
                            required
                            style={{
                              width: '100%',
                              padding: '10px 40px 10px 10px',
                              border: '1px solid #ddd',
                              borderRadius: '4px',
                              fontSize: '14px'
                            }}
                            placeholder="Confirmez votre nouveau mot de passe"
                          />
                          <button
                            type="button"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            style={{
                              position: 'absolute',
                              right: '10px',
                              top: '50%',
                              transform: 'translateY(-50%)',
                              background: 'none',
                              border: 'none',
                              cursor: 'pointer',
                              color: '#666'
                            }}
                          >
                            <FontAwesomeIcon icon={showConfirmPassword ? faEyeSlash : faEye} />
                          </button>
                        </div>
                      </div>

                      <div style={{ display: 'flex', gap: '10px' }}>
                        <button
                          type="submit"
                          disabled={changingPassword}
                          style={{
                            padding: '10px 20px',
                            backgroundColor: changingPassword ? '#ccc' : '#28a745',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: changingPassword ? 'not-allowed' : 'pointer',
                            fontSize: '14px'
                          }}
                        >
                          {changingPassword ? 'Modification en cours...' : 'Changer le mot de passe'}
                        </button>
                        <button
                          type="button"
                          onClick={() => {
                            setShowPasswordForm(false);
                            setPasswordData({
                              currentPassword: '',
                              newPassword: '',
                              confirmPassword: ''
                            });
                          }}
                          style={{
                            padding: '10px 20px',
                            backgroundColor: '#6c757d',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                            fontSize: '14px'
                          }}
                        >
                          Annuler
                        </button>
                      </div>
                    </form>
                  )}
                </div>

                {/* Delete Account Section */}
                <div style={{
                  border: '1px solid #dc3545',
                  borderRadius: '8px',
                  padding: '20px',
                  backgroundColor: '#fff'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                    <div>
                      <h3 style={{ margin: '0 0 8px 0', color: '#dc3545' }}>
                        <FontAwesomeIcon icon={faTrash} className="me-2" />
                        Supprimer le compte
                      </h3>
                      <p style={{ margin: 0, color: '#666', fontSize: '14px' }}>
                        Cette action est irréversible. Toutes vos données seront définitivement supprimées.
                      </p>
                    </div>
                    <button
                      onClick={() => setShowDeleteModal(true)}
                      style={{
                        padding: '8px 16px',
                        backgroundColor: '#dc3545',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '14px'
                      }}
                    >
                      Supprimer
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Delete Account Modal */}
        {showDeleteModal && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000
          }}>
            <div style={{
              backgroundColor: 'white',
              borderRadius: '8px',
              padding: '30px',
              maxWidth: '600px',
              width: '90%',
              maxHeight: '80vh',
              overflow: 'auto'
            }}>
              <h3 style={{ color: '#dc3545', marginBottom: '20px' }}>
                <FontAwesomeIcon icon={faTrash} className="me-2" />
                Suppression de compte - Information RGPD
              </h3>

              <div style={{ marginBottom: '25px', lineHeight: '1.6' }}>
                <h4 style={{ color: '#333', marginBottom: '15px' }}>Vos droits selon le RGPD :</h4>
                <ul style={{ paddingLeft: '20px', marginBottom: '20px' }}>
                  <li style={{ marginBottom: '8px' }}>
                    <strong>Droit à l'effacement :</strong> Vous avez le droit de demander la suppression de vos données personnelles.
                  </li>
                  <li style={{ marginBottom: '8px' }}>
                    <strong>Portabilité des données :</strong> Vous pouvez demander une copie de vos données avant suppression.
                  </li>
                  <li style={{ marginBottom: '8px' }}>
                    <strong>Délai de traitement :</strong> Votre demande sera traitée dans un délai maximum de 30 jours.
                  </li>
                  <li style={{ marginBottom: '8px' }}>
                    <strong>Données conservées :</strong> Certaines données peuvent être conservées pour des obligations légales (comptabilité, etc.).
                  </li>
                </ul>

                <div style={{
                  backgroundColor: '#f8f9fa',
                  border: '1px solid #dee2e6',
                  borderRadius: '4px',
                  padding: '15px',
                  marginBottom: '20px'
                }}>
                  <h5 style={{ color: '#495057', marginBottom: '10px' }}>Procédure de suppression :</h5>
                  <p style={{ margin: '0 0 10px 0', fontSize: '14px' }}>
                    Pour supprimer définitivement votre compte et toutes vos données personnelles,
                    veuillez nous envoyer un email à l'adresse suivante :
                  </p>
                  <p style={{
                    margin: 0,
                    fontSize: '16px',
                    fontWeight: 'bold',
                    color: '#2D8BBA',
                    textAlign: 'center',
                    padding: '10px',
                    backgroundColor: '#e3f2fd',
                    borderRadius: '4px'
                  }}>
                    <EMAIL>
                  </p>
                </div>

                <p style={{ fontSize: '14px', color: '#666', marginBottom: '0' }}>
                  <strong>Important :</strong> Cette action est irréversible. Une fois votre compte supprimé,
                  vous ne pourrez plus accéder à vos données, candidatures, ou CV sauvegardés.
                </p>
              </div>

              <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                <button
                  onClick={() => setShowDeleteModal(false)}
                  style={{
                    padding: '10px 20px',
                    backgroundColor: '#6c757d',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  Fermer
                </button>
                <a
                  href="mailto:<EMAIL>?subject=Demande de suppression de compte&body=Bonjour,%0D%0A%0D%0AJe souhaite supprimer définitivement mon compte et toutes mes données personnelles conformément au RGPD.%0D%0A%0D%0AEmail du compte : [VOTRE_EMAIL]%0D%0A%0D%0AMerci de confirmer la suppression.%0D%0A%0D%0ACordialement"
                  style={{
                    padding: '10px 20px',
                    backgroundColor: '#dc3545',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '4px',
                    fontSize: '14px',
                    display: 'inline-block'
                  }}
                >
                  Envoyer l'email de suppression
                </a>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Profile;
