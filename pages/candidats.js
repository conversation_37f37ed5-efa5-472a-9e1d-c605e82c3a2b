import Layout from "@/components/layout/Layout"
import About from "@/components/sections/home5/About"
import AboutTwo from "@/components/sections/home5/AboutTwo"
import Slider from "@/components/sections/home5/Slider"
import Testimonial from "@/components/sections/home5/Testimonial"
import AboutThree from "@/components/sections/home5/AboutThree"
import Estimate from "@/components/sections/home5/Estimate"
import Testimonial2 from "@/components/sections/home5/Testimonial2"
import Request from "@/components/sections/home5/Request"
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Section1 from "@/components/sections/home5/Section1";
import Section2 from "@/components/sections/home5/Section2";
import Section3 from "@/components/sections/home5/Section3";
import Section4 from "@/components/sections/home5/Section4";
import Section5 from "@/components/sections/home5/Section5";
import Section6 from "@/components/sections/home5/Section6";
import Section7 from "@/components/sections/home5/Section7";
import Section8 from "@/components/sections/home5/Section8";
export default function Home5() {
    return (
        <>
            <Layout headerStyle={2} footerStyle={3}>
                <Section1 id="emploi" />
                
                <Section3 id="cv" />
               
                {/* <Section4 id="alerte" /> */}
                <Section5 id="avantage-alerte" />
                
                <Section7 id="actions" />
                {/* <Section8 id="actions" /> */}
                {/* <AboutTwo />
                
                <Request />
                
                <Testimonial />
                <About />
                
                
                <Estimate/>
                <Testimonial2/>
                <AboutThree/>
  */}
                <ToastContainer/>
            </Layout>
        </>
    )
}