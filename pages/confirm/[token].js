import { useRouter } from 'next/router';
import { useEffect } from 'react';

export default function ConfirmEmail() {
  const router = useRouter();
  const { token } = router.query;

  useEffect(() => {
    if (token) {
      // Redirect to the backend confirmation endpoint
      window.location.href = `http://82.165.144.72:5000/api/users/confirm/${token}`;
    }
  }, [token]);

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '100vh',
      flexDirection: 'column'
    }}>
      <div style={{ textAlign: 'center' }}>
        <h2>Confirmation en cours...</h2>
        <p>Veuillez patienter pendant que nous confirmons votre email.</p>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Chargement...</span>
        </div>
      </div>
    </div>
  );
}
