import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowLeft,
  faUser,
  faEnvelope,
  faPhone,
  faMapMarkerAlt,
  faSave,
  faTimes,
  faEdit,
  faBriefcase,
  faGraduationCap,
  faCheckCircle,
  faExclamationCircle,
  faExclamationTriangle,
  faPlus,
  faMinus,
  faChartBar,
  faCog,
  faEuroSign,
  faCalendarAlt,
  faUserTie,
  faStar,
  faCheck,
  faClock
} from '@fortawesome/free-solid-svg-icons';
import Layout from '@/components/layout/Layout';
import styles from '@/styles/Profile.module.css';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import axios from 'axios';
import { jwtDecode } from 'jwt-decode';

export default function PersonalInfo() {
  const router = useRouter();
  const [editMode, setEditMode] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profileStatus, setProfileStatus] = useState(null);
  const [showQuickSetup, setShowQuickSetup] = useState(false);

  // Professional profile state - Updated to match comprehensive API
  const [professionalProfile, setProfessionalProfile] = useState({
    skills: [],
    workExperience: [],
    education: [],
    preferredRoles: [],
    preferredLocations: {
      cities: [],
      regions: [],
      remoteWork: false,
      willingToRelocate: false
    },
    contractTypes: [],
    salaryExpectations: {
      min: '',
      max: '',
      currency: 'EUR',
      period: 'year'
    },
    availability: {
      startDate: '',
      noticePeriod: '',
      partTime: false
    },
    currentStatus: '',
    experienceLevel: '',
    industries: [],
    languages: [],
    educationLevel: '',
    notifications: {
      emailNotifications: true,
      jobAlerts: true,
      applicationUpdates: true,
      matchingJobs: true,
      frequency: 'daily'
    }
  });

  // Form states
  const [newSkill, setNewSkill] = useState({ name: '', level: 'Intermédiaire', category: 'Technique' });
  const [newRole, setNewRole] = useState('');
  const [newCity, setNewCity] = useState('');
  const [newRegion, setNewRegion] = useState('');
  const [newIndustry, setNewIndustry] = useState('');
  const [newLanguage, setNewLanguage] = useState({ language: '', level: 'Intermédiaire' });
  const [newContractType, setNewContractType] = useState('');

  // Constants for dropdowns
  const contractTypes = ['CDI', 'CDD', 'Stage', 'Alternance', 'Freelance', 'Interim'];
  const skillLevels = ['Débutant', 'Intermédiaire', 'Avancé', 'Expert'];
  const skillCategories = ['Technique', 'Management', 'Langue', 'Soft Skills', 'Autre'];
  const currentStatusOptions = ['Employé', 'Demandeur d\'emploi', 'Étudiant', 'Freelance', 'Autre'];
  const experienceLevels = ['Débutant', 'Junior (1-3 ans)', 'Confirmé (3-7 ans)', 'Senior (7+ ans)', 'Expert (10+ ans)'];
  const educationLevels = ['Bac', 'Bac+2', 'Bac+3', 'Bac+5', 'Doctorat', 'Autre'];
  const languageLevels = ['Débutant', 'Intermédiaire', 'Courant', 'Natif'];
  const noticePeriods = ['Immédiatement', '1 semaine', '2 semaines', '1 mois', '2 mois', '3 mois'];
  const notificationFrequencies = ['immediate', 'daily', 'weekly', 'monthly'];
  const salaryPeriods = ['hour', 'day', 'month', 'year'];
  const availabilityOptions = ['Immédiatement', '1 semaine', '2 semaines', '1 mois', '2 mois', '3 mois'];
  const industryOptions = ['Technologie', 'Finance', 'Santé', 'Éducation', 'Retail', 'Manufacturing', 'Consulting', 'Media', 'Transport', 'Autre'];
  const languageOptions = ['Français', 'Anglais', 'Espagnol', 'Allemand', 'Italien', 'Portugais', 'Chinois', 'Japonais', 'Arabe', 'Russe', 'Autre'];

  // Validation function for profile data
  const validateProfileData = (data) => {
    const errors = [];
    
    if (!data.skills || data.skills.length === 0) {
      errors.push("Au moins une compétence est requise");
    }
    
    if (!data.preferredRoles || data.preferredRoles.length === 0) {
      errors.push("Au moins un rôle préféré est requis");
    }
    
    if (!data.preferredLocations?.cities || data.preferredLocations.cities.length === 0) {
      errors.push("Au moins une localisation préférée est requise");
    }
    
    if (!data.contractTypes || data.contractTypes.length === 0) {
      errors.push("Au moins un type de contrat est requis");
    }
    
    return errors;
  };

  // Fetch user data and profile status
  const fetchUserData = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        toast.error("Vous devez être connecté pour accéder à cette page");
        router.push('/signin');
        return;
      }

      const decoded = jwtDecode(token);
      const userId = decoded.id;

      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      // Fetch user basic data
      const userResponse = await axios.get(
        `http://82.165.144.72:5000/api/users/${userId}`,
        config
      );
      setUser(userResponse.data);

      // Fetch comprehensive profile (includes status and professional profile)
      await fetchProfessionalProfile();

    } catch (err) {
      console.error("Failed to fetch user data:", err);
      if (err.response?.status === 401) {
        toast.error("Session expirée, veuillez vous reconnecter");
        router.push('/signin');
      } else {
        toast.error("Erreur lors du chargement des informations");
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch profile status for job matching
  const fetchProfileStatus = async () => {
    try {
      const token = localStorage.getItem("token");
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      const response = await axios.get(
        'http://82.165.144.72:5000/api/job-match/profile',
        config
      );
      
      // Extract status information from the comprehensive profile response
      if (response.data) {
        const data = response.data;
        setProfileStatus({
          canUseJobMatching: data.status?.canReceiveMatches || false,
          completionPercentage: data.status?.completionPercentage || 0,
          missingRequirements: data.recommendations?.map(rec => rec.message) || [],
          profileCompletion: data.profile?.profileCompletion || {},
          insights: data.insights || {},
          recommendations: data.recommendations || []
        });
      }
    } catch (err) {
      console.error("Failed to fetch profile status:", err);
      // Try the old endpoint as fallback
      try {
        const fallbackResponse = await axios.get(
          'http://82.165.144.72:5000/api/job-match/profile-status',
          config
        );
        setProfileStatus(fallbackResponse.data);
      } catch (fallbackErr) {
        console.error("Failed to fetch profile status from fallback:", fallbackErr);
      }
    }
  };

  // Fetch professional profile
  const fetchProfessionalProfile = async () => {
    try {
      const token = localStorage.getItem("token");
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      const response = await axios.get(
        'http://82.165.144.72:5000/api/job-match/profile',
        config
      );
      
      console.log('Fetched professional profile:', response.data);
      
      if (response.data && response.data.profile) {
        const profileData = response.data.profile;
        const jobPrefs = profileData.jobPreferences || {};
        const professionalProf = profileData.professionalProfile || {};
        
        // Update profile status from the same response
        setProfileStatus({
          canUseJobMatching: response.data.status?.canReceiveMatches || false,
          completionPercentage: response.data.status?.completionPercentage || 0,
          missingRequirements: response.data.recommendations?.map(rec => rec.message) || [],
          profileCompletion: profileData.profileCompletion || {},
          insights: response.data.insights || {},
          recommendations: response.data.recommendations || []
        });
        
        setProfessionalProfile({
          // Skills from professionalProfile
          skills: professionalProf.skills || [],
          workExperience: professionalProf.workExperience || [],
          education: professionalProf.education || [],
          
          // Job preferences
          preferredRoles: jobPrefs.preferredRoles || [],
          preferredLocations: {
            cities: jobPrefs.preferredLocations?.cities || [],
            regions: jobPrefs.preferredLocations?.regions || [],
            remoteWork: jobPrefs.preferredLocations?.remoteWork || false,
            willingToRelocate: jobPrefs.preferredLocations?.willingToRelocate || false
          },
          contractTypes: jobPrefs.contractTypes || [],
          salaryExpectations: {
            min: jobPrefs.salaryExpectations?.min ? jobPrefs.salaryExpectations.min.toString() : '',
            max: jobPrefs.salaryExpectations?.max ? jobPrefs.salaryExpectations.max.toString() : '',
            currency: jobPrefs.salaryExpectations?.currency || 'EUR',
            period: jobPrefs.salaryExpectations?.period || 'year'
          },
          availability: {
            startDate: jobPrefs.availability?.startDate || '',
            noticePeriod: jobPrefs.availability?.noticePeriod || '',
            partTime: jobPrefs.availability?.partTime || false
          },
          
          // Professional profile data
          currentStatus: professionalProf.currentStatus || '',
          experienceLevel: professionalProf.experienceLevel || '',
          industries: professionalProf.industries || [],
          languages: professionalProf.languages || [],
          educationLevel: professionalProf.educationLevel || '',
          
          // Default notifications if not provided
          notifications: {
            emailNotifications: true,
            jobAlerts: true,
            applicationUpdates: true,
            matchingJobs: true,
            frequency: 'daily'
          }
        });
      }
    } catch (err) {
      console.error("Failed to fetch professional profile:", err);
      if (err.response?.status === 404) {
        console.log("Professional profile not found - this is normal for new users");
        // Initialize with empty profile for new users
        setProfessionalProfile({
          skills: [],
          workExperience: [],
          education: [],
          preferredRoles: [],
          preferredLocations: {
            cities: [],
            regions: [],
            remoteWork: false,
            willingToRelocate: false
          },
          contractTypes: [],
          salaryExpectations: {
            min: '',
            max: '',
            currency: 'EUR',
            period: 'year'
          },
          availability: {
            startDate: '',
            noticePeriod: '',
            partTime: false
          },
          currentStatus: '',
          experienceLevel: '',
          industries: [],
          languages: [],
          educationLevel: '',
          notifications: {
            emailNotifications: true,
            jobAlerts: true,
            applicationUpdates: true,
            matchingJobs: true,
            frequency: 'daily'
          }
        });
      }
      // Don't show error as this might be the first time setting up profile
    }
  };

  useEffect(() => {
    fetchUserData();
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setUser(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!user) return;

    setSaving(true);
    try {
      const token = localStorage.getItem("token");
      const decoded = jwtDecode(token);
      const userId = decoded.id;

      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
      };

      // Validate basic user info
      if (!user.firstName?.trim() || !user.lastName?.trim() || !user.email?.trim()) {
        toast.error("Les champs prénom, nom et email sont obligatoires");
        setSaving(false);
        return;
      }

      // Prepare comprehensive professional profile data
      const comprehensiveProfileData = {
        skills: professionalProfile.skills || [],
        workExperience: professionalProfile.workExperience || [],
        education: professionalProfile.education || [],
        preferredRoles: professionalProfile.preferredRoles || [],
        preferredLocations: {
          cities: professionalProfile.preferredLocations?.cities || [],
          regions: professionalProfile.preferredLocations?.regions || [],
          remoteWork: professionalProfile.preferredLocations?.remoteWork || false,
          willingToRelocate: professionalProfile.preferredLocations?.willingToRelocate || false
        },
        contractTypes: professionalProfile.contractTypes || [],
        salaryExpectations: {
          min: professionalProfile.salaryExpectations?.min ? parseInt(professionalProfile.salaryExpectations.min) : null,
          max: professionalProfile.salaryExpectations?.max ? parseInt(professionalProfile.salaryExpectations.max) : null,
          currency: professionalProfile.salaryExpectations?.currency || 'EUR',
          period: professionalProfile.salaryExpectations?.period || 'year'
        },
        availability: {
          startDate: professionalProfile.availability?.startDate || '',
          noticePeriod: professionalProfile.availability?.noticePeriod || '',
          partTime: professionalProfile.availability?.partTime || false
        },
        currentStatus: professionalProfile.currentStatus || '',
        experienceLevel: professionalProfile.experienceLevel || '',
        industries: professionalProfile.industries || [],
        languages: professionalProfile.languages || [],
        educationLevel: professionalProfile.educationLevel || '',
        notifications: {
          emailNotifications: professionalProfile.notifications?.emailNotifications !== false,
          jobAlerts: professionalProfile.notifications?.jobAlerts !== false,
          applicationUpdates: professionalProfile.notifications?.applicationUpdates !== false,
          matchingJobs: professionalProfile.notifications?.matchingJobs !== false,
          frequency: professionalProfile.notifications?.frequency || 'daily'
        }
      };

      // Validate professional profile data
      const validationErrors = validateProfileData(comprehensiveProfileData);
      if (validationErrors.length > 0) {
        toast.warning("Profil incomplet: " + validationErrors.join(", "));
        // Continue with update but warn user
      }

      console.log('Updating comprehensive profile with data:', comprehensiveProfileData);

      // Update basic user info first
      try {
        await axios.put(
          `http://82.165.144.72:5000/api/users/${userId}`,
          {
            firstName: user.firstName.trim(),
            lastName: user.lastName.trim(),
            email: user.email.trim(),
            telephone: user.telephone || ''
          },
          config
        );
        console.log('Basic user info updated successfully');
      } catch (userUpdateError) {
        console.error("Error updating basic user info:", userUpdateError);
        throw new Error("Erreur lors de la mise à jour des informations personnelles");
      }

      // Update comprehensive professional profile for job matching
      try {
        console.log('Sending comprehensive profile to API:', {
          endpoint: 'http://82.165.144.72:5000/api/job-match/profile',
          method: 'PUT',
          data: comprehensiveProfileData,
          headers: config.headers
        });
        
        const profileResponse = await axios.put(
          'http://82.165.144.72:5000/api/job-match/profile',
          comprehensiveProfileData,
          config
        );
        
        console.log('Comprehensive professional profile updated successfully:', profileResponse.data);
        console.log('Response profile data structure:', JSON.stringify(profileResponse.data, null, 2));
        
        // Check if the API response contains the expected data
        if (profileResponse.data && profileResponse.data.profile) {
          const returnedProfile = profileResponse.data.profile;
          console.log('Returned profile skills:', returnedProfile.professionalProfile?.skills || 'No skills returned');
          console.log('Returned profile jobPreferences:', returnedProfile.jobPreferences || 'No job preferences returned');
          
          // Log data mismatch warnings
          if (!returnedProfile.professionalProfile?.skills || returnedProfile.professionalProfile.skills.length === 0) {
            console.warn('WARNING: Backend did not store/return skills data properly');
          }
          if (!returnedProfile.jobPreferences?.preferredRoles || returnedProfile.jobPreferences.preferredRoles.length === 0) {
            console.warn('WARNING: Backend did not store/return preferred roles properly');
          }
        }
        
        // Show profile completion info if available
        if (profileResponse.data.profile?.profileCompletion) {
          const completion = profileResponse.data.profile.profileCompletion;
          toast.success(`Profil mis à jour avec succès! Complétude: ${completion.percentage}%`);
        } else {
          toast.success("Profil mis à jour avec succès!");
        }
        
      } catch (profileUpdateError) {
        console.error("Error updating professional profile:", profileUpdateError);
        // Log the specific error for debugging
        if (profileUpdateError.response) {
          console.error("Profile update error response:", profileUpdateError.response.data);
          console.error("Profile update error status:", profileUpdateError.response.status);
        }
        throw new Error("Erreur lors de la mise à jour du profil professionnel");
      }

      // Refresh profile data
      await fetchProfessionalProfile();
      
      setEditMode(false);
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error(error.message || "Échec de la mise à jour du profil");
    } finally {
      setSaving(false);
    }
  };

  // Quick setup for incomplete profiles
  const handleQuickSetup = async () => {
    setSaving(true);
    try {
      const token = localStorage.getItem("token");
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
      };

      // Quick setup with minimum required fields - properly structured for comprehensive API
      const quickProfile = {
        skills: professionalProfile.skills.length > 0 ? professionalProfile.skills : [
          { name: 'JavaScript', level: 'Intermédiaire', category: 'Technique' }
        ],
        workExperience: professionalProfile.workExperience.length > 0 ? professionalProfile.workExperience : [],
        education: professionalProfile.education.length > 0 ? professionalProfile.education : [],
        preferredRoles: professionalProfile.preferredRoles.length > 0 ? professionalProfile.preferredRoles : ['Développeur'],
        preferredLocations: {
          cities: professionalProfile.preferredLocations.cities.length > 0 ? 
            professionalProfile.preferredLocations.cities : ['Paris'],
          regions: professionalProfile.preferredLocations.regions.length > 0 ? 
            professionalProfile.preferredLocations.regions : [],
          remoteWork: professionalProfile.preferredLocations.remoteWork || false,
          willingToRelocate: professionalProfile.preferredLocations.willingToRelocate || false
        },
        contractTypes: professionalProfile.contractTypes.length > 0 ? 
          professionalProfile.contractTypes : ['CDI'],
        salaryExpectations: {
          min: professionalProfile.salaryExpectations.min ? parseInt(professionalProfile.salaryExpectations.min) : null,
          max: professionalProfile.salaryExpectations.max ? parseInt(professionalProfile.salaryExpectations.max) : null,
          currency: professionalProfile.salaryExpectations.currency || 'EUR',
          period: professionalProfile.salaryExpectations.period || 'year'
        },
        availability: {
          startDate: professionalProfile.availability.startDate || '',
          noticePeriod: professionalProfile.availability.noticePeriod || 'Immédiatement',
          partTime: professionalProfile.availability.partTime || false
        },
        currentStatus: professionalProfile.currentStatus || 'Demandeur d\'emploi',
        experienceLevel: professionalProfile.experienceLevel || 'Junior (1-3 ans)',
        industries: professionalProfile.industries.length > 0 ? professionalProfile.industries : ['Technologie'],
        languages: professionalProfile.languages.length > 0 ? professionalProfile.languages : [
          { language: 'Français', level: 'Natif' }
        ],
        educationLevel: professionalProfile.educationLevel || 'Bac+3',
        notifications: {
          emailNotifications: true,
          jobAlerts: true,
          applicationUpdates: true,
          matchingJobs: true,
          frequency: 'daily'
        }
      };

      console.log('Quick setup profile data:', quickProfile);

      const response = await axios.put(
        'http://82.165.144.72:5000/api/job-match/profile',
        quickProfile,
        config
      );

      console.log('Quick setup response:', response.data);

      toast.success("Configuration rapide terminée! Vous pouvez maintenant utiliser le matching d'emplois.");
      
      // Refresh all profile data
      await fetchProfessionalProfile();
      
      setShowQuickSetup(false);
    } catch (error) {
      console.error("Error in quick setup:", error);
      if (error.response) {
        console.error("Quick setup error response:", error.response.data);
      }
      toast.error("Erreur lors de la configuration rapide");
    } finally {
      setSaving(false);
    }
  };

  // Skills management
  const addSkill = () => {
    if (newSkill.name.trim() && !professionalProfile.skills.some(skill => skill.name === newSkill.name.trim())) {
      setProfessionalProfile(prev => ({
        ...prev,
        skills: [...prev.skills, {
          name: newSkill.name.trim(),
          level: newSkill.level,
          category: newSkill.category
        }]
      }));
      setNewSkill({ name: '', level: 'Intermédiaire', category: 'Technique' });
    }
  };

  const removeSkill = (skillToRemove) => {
    setProfessionalProfile(prev => ({
      ...prev,
      skills: prev.skills.filter(skill => skill.name !== skillToRemove.name)
    }));
  };

  // Role management
  const addRole = () => {
    if (newRole.trim() && !professionalProfile.preferredRoles.includes(newRole.trim())) {
      setProfessionalProfile(prev => ({
        ...prev,
        preferredRoles: [...prev.preferredRoles, newRole.trim()]
      }));
      setNewRole('');
    }
  };

  const removeRole = (roleToRemove) => {
    setProfessionalProfile(prev => ({
      ...prev,
      preferredRoles: prev.preferredRoles.filter(role => role !== roleToRemove)
    }));
  };

  // City management
  const addCity = () => {
    if (newCity.trim() && !professionalProfile.preferredLocations.cities.includes(newCity.trim())) {
      setProfessionalProfile(prev => ({
        ...prev,
        preferredLocations: {
          ...prev.preferredLocations,
          cities: [...prev.preferredLocations.cities, newCity.trim()]
        }
      }));
      setNewCity('');
    }
  };

  const removeCity = (cityToRemove) => {
    setProfessionalProfile(prev => ({
      ...prev,
      preferredLocations: {
        ...prev.preferredLocations,
        cities: prev.preferredLocations.cities.filter(city => city !== cityToRemove)
      }
    }));
  };

  // Region management
  const addRegion = () => {
    if (newRegion.trim() && !professionalProfile.preferredLocations.regions.includes(newRegion.trim())) {
      setProfessionalProfile(prev => ({
        ...prev,
        preferredLocations: {
          ...prev.preferredLocations,
          regions: [...prev.preferredLocations.regions, newRegion.trim()]
        }
      }));
      setNewRegion('');
    }
  };

  const removeRegion = (regionToRemove) => {
    setProfessionalProfile(prev => ({
      ...prev,
      preferredLocations: {
        ...prev.preferredLocations,
        regions: prev.preferredLocations.regions.filter(region => region !== regionToRemove)
      }
    }));
  };

  // Industry management
  const addIndustry = () => {
    if (newIndustry.trim() && !professionalProfile.industries.includes(newIndustry.trim())) {
      setProfessionalProfile(prev => ({
        ...prev,
        industries: [...prev.industries, newIndustry.trim()]
      }));
      setNewIndustry('');
    }
  };

  const removeIndustry = (industryToRemove) => {
    setProfessionalProfile(prev => ({
      ...prev,
      industries: prev.industries.filter(industry => industry !== industryToRemove)
    }));
  };

  // Language management
  const addLanguage = () => {
    if (newLanguage.language.trim() && !professionalProfile.languages.some(lang => lang.language === newLanguage.language.trim())) {
      setProfessionalProfile(prev => ({
        ...prev,
        languages: [...prev.languages, {
          language: newLanguage.language.trim(),
          level: newLanguage.level
        }]
      }));
      setNewLanguage({ language: '', level: 'Intermédiaire' });
    }
  };

  const removeLanguage = (languageToRemove) => {
    setProfessionalProfile(prev => ({
      ...prev,
      languages: prev.languages.filter(lang => lang.language !== languageToRemove.language)
    }));
  };

  // Contract type management
  const toggleContractType = (contractType) => {
    setProfessionalProfile(prev => ({
      ...prev,
      contractTypes: prev.contractTypes.includes(contractType)
        ? prev.contractTypes.filter(type => type !== contractType)
        : [...prev.contractTypes, contractType]
    }));
  };

  // Work Experience management
  const addWorkExperience = () => {
    setProfessionalProfile(prev => ({
      ...prev,
      workExperience: [...prev.workExperience, {
        jobTitle: '',
        company: '',
        startDate: '',
        endDate: '',
        current: false,
        description: '',
        location: '',
        industry: ''
      }]
    }));
  };

  const updateWorkExperience = (index, field, value) => {
    setProfessionalProfile(prev => ({
      ...prev,
      workExperience: prev.workExperience.map((exp, i) => 
        i === index ? { ...exp, [field]: value } : exp
      )
    }));
  };

  const removeWorkExperience = (index) => {
    setProfessionalProfile(prev => ({
      ...prev,
      workExperience: prev.workExperience.filter((_, i) => i !== index)
    }));
  };

  // Education management
  const addEducation = () => {
    setProfessionalProfile(prev => ({
      ...prev,
      education: [...prev.education, {
        degree: '',
        institution: '',
        startDate: '',
        endDate: '',
        current: false,
        description: '',
        location: '',
        gpa: ''
      }]
    }));
  };

  const updateEducation = (index, field, value) => {
    setProfessionalProfile(prev => ({
      ...prev,
      education: prev.education.map((edu, i) => 
        i === index ? { ...edu, [field]: value } : edu
      )
    }));
  };

  const removeEducation = (index) => {
    setProfessionalProfile(prev => ({
      ...prev,
      education: prev.education.filter((_, i) => i !== index)
    }));
  };

  if (loading) {
    return (
      <Layout headerStyle={2} footerStyle={3}>
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '60vh' }}>
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Chargement...</span>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout headerStyle={2} footerStyle={3}>
      <ToastContainer position="top-right" autoClose={3000} />
      <div className="container" style={{ marginTop: '150px', marginBottom: '120px', minHeight: '70vh' }}>
        <div style={{
          background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
          borderRadius: '20px',
          padding: '2px',
          marginBottom: '30px'
        }}>
          <div style={{
            background: '#fff',
            borderRadius: '18px',
            padding: '40px',
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '30px' }}>
              <div>
                <button
                  onClick={() => router.back()}
                  className="btn-link p-2 mb-4 d-flex align-items-center justify-content-center"
                  style={{
                    background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                    border: 'none',
                    borderRadius: '8px',
                    color: 'white',
                    padding: '12px 24px',
                    textDecoration: 'none',
                    fontSize: '16px',
                    fontWeight: '500',
                    minWidth: '48px',
                    minHeight: '48px'
                  }}
                >
                  <FontAwesomeIcon icon={faArrowLeft} className="me-md-2" />
                  <span className="d-none d-md-inline">Retour au profil</span>
                </button>
                <h1 style={{
                  fontSize: '32px',
                  fontWeight: '700',
                  background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  marginBottom: '8px'
                }}>
                  <FontAwesomeIcon icon={faEdit} className="me-3" style={{ color: '#2D8BBA' }} />
                  Mon profil professionnel
                </h1>
                <p style={{ color: '#6c757d', fontSize: '16px', marginBottom: '0' }}>
                  Complétez votre profil pour améliorer vos correspondances d'emplois
                </p>
              </div>
              
              {/* Edit Mode Toggle Button */}
              <div className="d-flex align-items-start">
                <button
                  type="button"
                  onClick={() => setEditMode(!editMode)}
                  className="mb-4 d-flex align-items-center justify-content-center"
                  style={{
                    background: editMode ? 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)' : 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                    border: 'none',
                    borderRadius: '8px',
                    color: 'white',
                    padding: '12px 18px',
                    fontSize: '16px',
                    fontWeight: '500',
                    minWidth: '48px',
                    minHeight: '48px'
                  }}
                >
                  <FontAwesomeIcon icon={editMode ? faTimes : faEdit} className="me-md-2" />
                  <span className="d-none d-md-inline">{editMode ? 'Annuler' : 'Modifier le profil'}</span>
                </button>
              </div>
            </div>

            {/* Profile Status Banner */}
            {profileStatus && (
              <div className="row mb-4">
                {/* Main Status Card */}
                <div className="col-md-12">
                  <div className={`alert ${profileStatus.canUseJobMatching ? 'alert-success' : 'alert-warning'} d-flex align-items-center mb-3`}>
                    <FontAwesomeIcon 
                      icon={profileStatus.canUseJobMatching ? faCheckCircle : faExclamationTriangle} 
                      className="me-3" 
                      style={{ fontSize: '24px' }} 
                    />
                    <div className="flex-grow-1">
                      <h6 className="alert-heading mb-2">
                        {profileStatus.canUseJobMatching ? 'Profil complet!' : 'Profil incomplet'}
                      </h6>
                      <p className="mb-2">
                        Complétude du profil: {profileStatus.completionPercentage}%
                      </p>
                      {profileStatus.insights && (
                        <div className="small">
                          <div className="row">
                            <div className="col-6">
                              <strong>Emplois disponibles:</strong> {profileStatus.insights.totalJobsAvailable || 0}
                            </div>
                            <div className="col-6">
                              <strong>
                                <span className="d-none d-md-inline">Score de correspondance moyen:</span>
                                <span className="d-md-none">Score:</span>
                              </strong> {profileStatus.insights.averageMatchingScore || 0}%
                            </div>
                          </div>
                        </div>
                      )}
                      {!profileStatus.canUseJobMatching && (
                        <button
                          onClick={() => setShowQuickSetup(true)}
                          className="btn btn-warning btn-sm mt-2"
                        >
                          <FontAwesomeIcon icon={faCog} className="me-2" />
                          Configuration rapide
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                
              </div>
            )}

            {/* Recommendations Section */}
            {profileStatus && profileStatus.recommendations && profileStatus.recommendations.length > 0 && (
              <div className="alert alert-info mb-4">
                <h6 className="alert-heading">
                  <FontAwesomeIcon icon={faExclamationCircle} className="me-2" />
                  Recommandations pour améliorer votre profil
                </h6>
                <ul className="mb-0">
                  {profileStatus.recommendations.slice(0, 5).map((recommendation, index) => (
                    <li key={index} className="small">
                      <span className={`badge me-2 ${
                        recommendation.priority === 'high' ? 'bg-danger' : 
                        recommendation.priority === 'medium' ? 'bg-warning' : 'bg-secondary'
                      }`}>
                        {recommendation.priority === 'high' ? 'Priorité haute' : 
                         recommendation.priority === 'medium' ? 'Priorité moyenne' : 'Priorité basse'}
                      </span>
                      {recommendation.message}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Quick Setup Modal */}
            {showQuickSetup && (
              <div className="modal show" style={{ display: 'block', backgroundColor: 'rgba(0,0,0,0.5)' }}>
                <div className="modal-dialog modal-dialog-centered">
                  <div className="modal-content">
                    <div className="modal-header">
                      <h5 className="modal-title">Configuration rapide du profil</h5>
                      <button type="button" className="btn-close" onClick={() => setShowQuickSetup(false)}></button>
                    </div>
                    <div className="modal-body">
                      <p>Cette configuration rapide ajoutera les informations minimum requises pour utiliser le système de correspondance d'emplois.</p>
                      <p><strong>Champs qui seront configurés :</strong></p>
                      <ul>
                        <li>Compétence par défaut</li>
                        <li>Rôle préféré par défaut</li>
                        <li>Localisation par défaut</li>
                        <li>Type de contrat préféré</li>
                        <li>Disponibilité</li>
                      </ul>
                      <p className="text-muted">Vous pourrez modifier ces informations par la suite.</p>
                    </div>
                    <div className="modal-footer">
                      <button type="button" className="btn btn-secondary" onClick={() => setShowQuickSetup(false)}>
                        Annuler
                      </button>
                      <button type="button" className="btn btn-primary" onClick={handleQuickSetup} disabled={saving}>
                        {saving ? 'Configuration...' : 'Configurer rapidement'}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <form onSubmit={handleSubmit}>
              {/* Personal Information Section */}
              <div className="mb-5">
                <h3 className="mb-4" style={{ color: '#2D8BBA', borderBottom: '2px solid #2D8BBA', paddingBottom: '8px' }}>
                  <FontAwesomeIcon icon={faUser} className="me-2" />
                  Informations personnelles
                </h3>
                
                {!editMode ? (
                  // Read-only view
                  <div className="row">
                    <div className="col-md-6 mb-3">
                      <div className="card border-0 bg-light">
                        <div className="card-body p-3">
                          <label className="form-label mb-1 fw-bold">
                            <FontAwesomeIcon icon={faUser} className="me-2 text-primary" />
                            Prénom
                          </label>
                          <p className="mb-0 text-dark">{user?.firstName || "Non renseigné"}</p>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-6 mb-3">
                      <div className="card border-0 bg-light">
                        <div className="card-body p-3">
                          <label className="form-label mb-1 fw-bold">
                            <FontAwesomeIcon icon={faUser} className="me-2 text-primary" />
                            Nom de famille
                          </label>
                          <p className="mb-0 text-dark">{user?.lastName || "Non renseigné"}</p>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-6 mb-3">
                      <div className="card border-0 bg-light">
                        <div className="card-body p-3">
                          <label className="form-label mb-1 fw-bold">
                            <FontAwesomeIcon icon={faPhone} className="me-2 text-primary" />
                            Téléphone
                          </label>
                          <p className="mb-0 text-dark">{user?.telephone || "Non renseigné"}</p>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-6 mb-3">
                      <div className="card border-0 bg-light">
                        <div className="card-body p-3">
                          <label className="form-label mb-1 fw-bold">
                            <FontAwesomeIcon icon={faEnvelope} className="me-2 text-primary" />
                            E-mail
                          </label>
                          <p className="mb-0 text-dark">{user?.email || "Non renseigné"}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  // Edit mode
                  <div className="row">
                    <div className="col-md-6 mb-3">
                      <label htmlFor="firstName" className="form-label">
                        <FontAwesomeIcon icon={faUser} className="me-2" />
                        Prénom *
                      </label>
                      <input
                        type="text"
                        id="firstName"
                        name="firstName"
                        className="form-control"
                        value={user?.firstName || ""}
                        onChange={(e) => setUser({...user, firstName: e.target.value})}
                        placeholder="Entrez votre prénom"
                        required
                      />
                    </div>
                    <div className="col-md-6 mb-3">
                      <label htmlFor="lastName" className="form-label">
                        <FontAwesomeIcon icon={faUser} className="me-2" />
                        Nom de famille *
                      </label>
                      <input
                        type="text"
                        id="lastName"
                        name="lastName"
                        className="form-control"
                        value={user?.lastName || ""}
                        onChange={(e) => setUser({...user, lastName: e.target.value})}
                        placeholder="Entrez votre nom de famille"
                        required
                      />
                    </div>
                    <div className="col-md-6 mb-3">
                      <label htmlFor="telephone" className="form-label">
                        <FontAwesomeIcon icon={faPhone} className="me-2" />
                        Téléphone
                      </label>
                      <input
                        type="text"
                        id="telephone"
                        name="telephone"
                        className="form-control"
                        value={user?.telephone || ""}
                        onChange={(e) => setUser({...user, telephone: e.target.value})}
                        placeholder="Entrez votre numéro de téléphone"
                      />
                    </div>
                    <div className="col-md-6 mb-3">
                      <label htmlFor="email" className="form-label">
                        <FontAwesomeIcon icon={faEnvelope} className="me-2" />
                        E-mail *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        className="form-control"
                        value={user?.email || ""}
                        onChange={(e) => setUser({...user, email: e.target.value})}
                        placeholder="Entrez votre adresse e-mail"
                        required
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Skills Section */}
              <div className="mb-5">
                <h3 className="mb-4" style={{ 
                  color: '#2D8BBA', 
                  borderBottom: '3px solid #2D8BBA', 
                  paddingBottom: '12px',
                  fontSize: '24px',
                  fontWeight: '700'
                }}>
                  <FontAwesomeIcon icon={faBriefcase} className="me-3" style={{ color: '#2D8BBA' }} />
                  Compétences
                </h3>
                
                {!editMode ? (
                  // Read-only view
                  <div>
                    {professionalProfile.skills.length > 0 ? (
                      <div className="d-flex flex-wrap gap-3">
                        {professionalProfile.skills.map((skill, index) => (
                          <div key={index} className="position-relative">
                            <div style={{
                              background: `linear-gradient(135deg, ${
                                skill.level === 'Expert' ? '#dc2626 0%, #991b1b 100%' :
                                skill.level === 'Avancé' ? '#ea580c 0%, #c2410c 100%' :
                                skill.level === 'Intermédiaire' ? '#2563eb 0%, #1d4ed8 100%' :
                                '#10b981 0%, #059669 100%'
                              })`,
                              borderRadius: '20px',
                              padding: '14px 20px',
                              color: 'white',
                              fontSize: '14px',
                              fontWeight: '600',
                              boxShadow: `0 6px 20px ${
                                skill.level === 'Expert' ? 'rgba(220, 38, 38, 0.4)' :
                                skill.level === 'Avancé' ? 'rgba(234, 88, 12, 0.4)' :
                                skill.level === 'Intermédiaire' ? 'rgba(37, 99, 235, 0.4)' :
                                'rgba(16, 185, 129, 0.4)'
                              }`,
                              border: '2px solid rgba(255,255,255,0.2)',
                              transform: 'translateY(0)',
                              transition: 'all 0.3s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.target.style.transform = 'translateY(-3px)';
                              e.target.style.boxShadow = `0 8px 25px ${
                                skill.level === 'Expert' ? 'rgba(220, 38, 38, 0.5)' :
                                skill.level === 'Avancé' ? 'rgba(234, 88, 12, 0.5)' :
                                skill.level === 'Intermédiaire' ? 'rgba(37, 99, 235, 0.5)' :
                                'rgba(16, 185, 129, 0.5)'
                              }`;
                            }}
                            onMouseLeave={(e) => {
                              e.target.style.transform = 'translateY(0)';
                              e.target.style.boxShadow = `0 6px 20px ${
                                skill.level === 'Expert' ? 'rgba(220, 38, 38, 0.4)' :
                                skill.level === 'Avancé' ? 'rgba(234, 88, 12, 0.4)' :
                                skill.level === 'Intermédiaire' ? 'rgba(37, 99, 235, 0.4)' :
                                'rgba(16, 185, 129, 0.4)'
                              }`;
                            }}
                            >
                              <div className="d-flex align-items-center">
                                <FontAwesomeIcon icon={faStar} className="me-2" />
                                <div>
                                  <div className="fw-bold">{skill.name}</div>
                                  <small className="opacity-85">{skill.level} • {skill.category}</small>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="alert alert-light border-0 text-center py-4" style={{ backgroundColor: '#f8f9fa' }}>
                        <FontAwesomeIcon icon={faExclamationCircle} className="me-2 text-muted" />
                        <span className="text-muted">Aucune compétence renseignée</span>
                      </div>
                    )}
                  </div>
                ) : (
                  // Edit mode
                  <div>
                    <div className="row mb-3">
                      <div className="col-md-4">
                        <input
                          type="text"
                          className="form-control"
                          value={newSkill.name}
                          onChange={(e) => setNewSkill(prev => ({ ...prev, name: e.target.value }))}
                          placeholder="Nom de la compétence"
                          onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSkill())}
                        />
                      </div>
                      <div className="col-md-3">
                        <select
                          className="form-select"
                          value={newSkill.level}
                          onChange={(e) => setNewSkill(prev => ({ ...prev, level: e.target.value }))}
                        >
                          {skillLevels.map(level => (
                            <option key={level} value={level}>{level}</option>
                          ))}
                        </select>
                      </div>
                      <div className="col-md-3">
                        <select
                          className="form-select"
                          value={newSkill.category}
                          onChange={(e) => setNewSkill(prev => ({ ...prev, category: e.target.value }))}
                        >
                          {skillCategories.map(category => (
                            <option key={category} value={category}>{category}</option>
                          ))}
                        </select>
                      </div>
                      <div className="col-md-2">
                        <button type="button" className="btn btn-outline-primary w-100" onClick={addSkill}>
                          <FontAwesomeIcon icon={faPlus} />
                        </button>
                      </div>
                    </div>
                    <div className="d-flex flex-wrap gap-2">
                      {professionalProfile.skills.map((skill, index) => (
                        <span key={index} className="badge bg-primary d-flex align-items-center" style={{ fontSize: '14px', padding: '8px 12px' }}>
                          <strong>{skill.name}</strong>
                          <span className="ms-2 opacity-75">({skill.level} - {skill.category})</span>
                          <button
                            type="button"
                            className="btn-close btn-close-white ms-2"
                            style={{ fontSize: '10px' }}
                            onClick={() => removeSkill(skill)}
                          ></button>
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Work Experience Section */}
              <div className="mb-5">
                <h3 className="mb-4" style={{ 
                  color: '#2D8BBA', 
                  borderBottom: '3px solid #2D8BBA', 
                  paddingBottom: '12px',
                  fontSize: '24px',
                  fontWeight: '700'
                }}>
                  <FontAwesomeIcon icon={faUserTie} className="me-3" style={{ color: '#2D8BBA' }} />
                  Expérience professionnelle
                </h3>
                
                {!editMode ? (
                  // Read-only view
                  <div>
                    {professionalProfile.workExperience.length > 0 ? (
                      <div className="row">
                        {professionalProfile.workExperience.map((exp, index) => (
                          <div key={index} className="col-12 mb-4">
                            <div className="card border-0 shadow-lg" style={{
                              background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                              borderRadius: '16px',
                              overflow: 'hidden',
                              transition: 'all 0.3s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.transform = 'translateY(-2px)';
                              e.currentTarget.style.boxShadow = '0 12px 30px rgba(45, 139, 186, 0.15)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.transform = 'translateY(0)';
                              e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.1)';
                            }}>
                              <div className="card-body p-4">
                                <div className="d-flex align-items-start">
                                  <div className="me-4">
                                    <div style={{
                                      background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                                      borderRadius: '50%',
                                      width: '60px',
                                      height: '60px',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      boxShadow: '0 6px 20px rgba(45, 139, 186, 0.3)'
                                    }}>
                                      <FontAwesomeIcon icon={faBriefcase} className="text-white" style={{ fontSize: '20px' }} />
                                    </div>
                                  </div>
                                  <div className="flex-grow-1">
                                    <div className="row">
                                      <div className="col-md-8">
                                        <h5 className="mb-2 fw-bold" style={{ color: '#2D8BBA', fontSize: '20px' }}>
                                          {exp.jobTitle || "Poste non spécifié"}
                                        </h5>
                                        <h6 className="mb-3" style={{ color: '#64748b', fontSize: '16px', fontWeight: '600' }}>
                                          {exp.company || "Entreprise non spécifiée"}
                                        </h6>
                                        <p className="mb-0" style={{ color: '#475569', lineHeight: '1.6' }}>
                                          {exp.description || "Aucune description"}
                                        </p>
                                      </div>
                                      <div className="col-md-4">
                                        <div className="text-end">
                                          <span className={`badge px-3 py-2 mb-3 ${exp.current ? 'bg-success' : 'bg-secondary'}`} style={{
                                            fontSize: '12px',
                                            fontWeight: '600',
                                            borderRadius: '12px'
                                          }}>
                                            {exp.current ? '📍 Poste actuel' : '✅ Terminé'}
                                          </span>
                                          <div className="small mb-2" style={{ color: '#64748b' }}>
                                            <FontAwesomeIcon icon={faCalendarAlt} className="me-2" style={{ color: '#2D8BBA' }} />
                                            <strong>
                                              {exp.startDate ? new Date(exp.startDate).toLocaleDateString('fr-FR') : 'Date de début non spécifiée'}
                                              {!exp.current && exp.endDate && ` - ${new Date(exp.endDate).toLocaleDateString('fr-FR')}`}
                                            </strong>
                                          </div>
                                          {exp.location && (
                                            <div className="small mb-2" style={{ color: '#64748b' }}>
                                              <FontAwesomeIcon icon={faMapMarkerAlt} className="me-2" style={{ color: '#2D8BBA' }} />
                                              <strong>{exp.location}</strong>
                                            </div>
                                          )}
                                          {exp.industry && (
                                            <div className="small" style={{ color: '#64748b' }}>
                                              <FontAwesomeIcon icon={faBriefcase} className="me-2" style={{ color: '#2D8BBA' }} />
                                              <strong>{exp.industry}</strong>
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="alert alert-light border-0 text-center py-4" style={{ backgroundColor: '#f8f9fa' }}>
                        <FontAwesomeIcon icon={faExclamationCircle} className="me-2 text-muted" />
                        <span className="text-muted">Aucune expérience professionnelle renseignée</span>
                      </div>
                    )}
                  </div>
                ) : (
                  // Edit mode
                  <div>
                    {professionalProfile.workExperience.map((exp, index) => (
                      <div key={index} className="border rounded p-3 mb-3">
                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <h6>Expérience {index + 1}</h6>
                          <button 
                            type="button" 
                            className="btn btn-outline-danger btn-sm"
                            onClick={() => removeWorkExperience(index)}
                          >
                            <FontAwesomeIcon icon={faMinus} />
                          </button>
                        </div>
                        <div className="row">
                          <div className="col-md-6 mb-2">
                            <input
                              type="text"
                              className="form-control"
                              placeholder="Titre du poste"
                              value={exp.jobTitle || ''}
                              onChange={(e) => updateWorkExperience(index, 'jobTitle', e.target.value)}
                            />
                          </div>
                          <div className="col-md-6 mb-2">
                            <input
                              type="text"
                              className="form-control"
                              placeholder="Entreprise"
                              value={exp.company || ''}
                              onChange={(e) => updateWorkExperience(index, 'company', e.target.value)}
                            />
                          </div>
                          <div className="col-md-4 mb-2">
                            <input
                              type="date"
                              className="form-control"
                              placeholder="Date de début"
                              value={exp.startDate || ''}
                              onChange={(e) => updateWorkExperience(index, 'startDate', e.target.value)}
                            />
                          </div>
                          <div className="col-md-4 mb-2">
                            <input
                              type="date"
                              className="form-control"
                              placeholder="Date de fin"
                              value={exp.endDate || ''}
                              onChange={(e) => updateWorkExperience(index, 'endDate', e.target.value)}
                              disabled={exp.current}
                            />
                          </div>
                          <div className="col-md-4 mb-2">
                            <div className="form-check">
                              <input
                                className="form-check-input"
                                type="checkbox"
                                checked={exp.current || false}
                                onChange={(e) => updateWorkExperience(index, 'current', e.target.checked)}
                              />
                              <label className="form-check-label">
                                Poste actuel
                              </label>
                            </div>
                          </div>
                          <div className="col-md-6 mb-2">
                            <input
                              type="text"
                              className="form-control"
                              placeholder="Localisation"
                              value={exp.location || ''}
                              onChange={(e) => updateWorkExperience(index, 'location', e.target.value)}
                            />
                          </div>
                          <div className="col-md-6 mb-2">
                            <input
                              type="text"
                              className="form-control"
                              placeholder="Secteur d'activité"
                              value={exp.industry || ''}
                              onChange={(e) => updateWorkExperience(index, 'industry', e.target.value)}
                            />
                          </div>
                          <div className="col-12 mb-2">
                            <textarea
                              className="form-control"
                              rows="3"
                              placeholder="Description de vos responsabilités et réalisations"
                              value={exp.description || ''}
                              onChange={(e) => updateWorkExperience(index, 'description', e.target.value)}
                            ></textarea>
                          </div>
                        </div>
                      </div>
                    ))}
                    <button type="button" className="btn btn-outline-primary" onClick={addWorkExperience}>
                      <FontAwesomeIcon icon={faPlus} className="me-2" />
                      Ajouter une expérience
                    </button>
                  </div>
                )}
              </div>

              {/* Education Section */}
              <div className="mb-5">
                <h3 className="mb-4" style={{ 
                  color: '#2D8BBA', 
                  borderBottom: '3px solid #2D8BBA', 
                  paddingBottom: '12px',
                  fontSize: '24px',
                  fontWeight: '700'
                }}>
                  <FontAwesomeIcon icon={faGraduationCap} className="me-3" style={{ color: '#2D8BBA' }} />
                  Formation
                </h3>
                
                {!editMode ? (
                  // Read-only view
                  <div>
                    {professionalProfile.education.length > 0 ? (
                      <div className="row">
                        {professionalProfile.education.map((edu, index) => (
                          <div key={index} className="col-12 mb-4">
                            <div 
                              className="card border-0 shadow-sm h-100" 
                              style={{
                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                borderRadius: '16px',
                                overflow: 'hidden',
                                transition: 'all 0.3s ease',
                                position: 'relative'
                              }}
                              onMouseEnter={(e) => {
                                e.target.style.transform = 'translateY(-5px)';
                                e.target.style.boxShadow = '0 20px 40px rgba(102, 126, 234, 0.3)';
                              }}
                              onMouseLeave={(e) => {
                                e.target.style.transform = 'translateY(0)';
                                e.target.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.1)';
                              }}
                            >
                              <div className="card-body p-4" style={{ background: 'rgba(255, 255, 255, 0.95)', margin: '2px', borderRadius: '14px' }}>
                                <div className="d-flex align-items-start">
                                  <div className="me-4" style={{ flexShrink: 0 }}>
                                    <div 
                                      className="d-flex align-items-center justify-content-center" 
                                      style={{ 
                                        width: '60px', 
                                        height: '60px',
                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                        borderRadius: '20px',
                                        boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)'
                                      }}
                                    >
                                      <FontAwesomeIcon icon={faGraduationCap} style={{ color: 'white', fontSize: '24px' }} />
                                    </div>
                                  </div>
                                  <div className="flex-grow-1">
                                    <div className="row">
                                      <div className="col-md-8">
                                        <h5 className="mb-2" style={{ 
                                          color: '#2c3e50', 
                                          fontWeight: '700',
                                          fontSize: '20px'
                                        }}>
                                          {edu.degree || "Diplôme non spécifié"}
                                        </h5>
                                        <h6 className="mb-3" style={{ 
                                          color: '#667eea', 
                                          fontWeight: '600',
                                          fontSize: '16px'
                                        }}>
                                          <FontAwesomeIcon icon={faBriefcase} className="me-2" />
                                          {edu.institution || "Établissement non spécifié"}
                                        </h6>
                                        {edu.description && (
                                          <p className="mb-3" style={{ 
                                            color: '#555', 
                                            fontSize: '14px',
                                            lineHeight: '1.6'
                                          }}>
                                            {edu.description}
                                          </p>
                                        )}
                                        {edu.gpa && (
                                          <div className="mb-2">
                                            <span 
                                              className="badge px-3 py-2"
                                              style={{
                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                                color: 'white',
                                                fontSize: '12px',
                                                fontWeight: '600',
                                                borderRadius: '20px'
                                              }}
                                            >
                                              <FontAwesomeIcon icon={faStar} className="me-1" />
                                              Mention/GPA: {edu.gpa}
                                            </span>
                                          </div>
                                        )}
                                      </div>
                                      <div className="col-md-4">
                                        <div className="text-end">
                                          <span 
                                            className="badge px-3 py-2 mb-3 d-block"
                                            style={{
                                              background: edu.current ? 
                                                'linear-gradient(135deg, #10b981 0%, #059669 100%)' : 
                                                'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
                                              color: 'white',
                                              fontSize: '12px',
                                              fontWeight: '600',
                                              borderRadius: '20px',
                                              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
                                            }}
                                          >
                                            {edu.current ? '🎓 En cours' : '✅ Terminé'}
                                          </span>
                                          <div 
                                            className="p-3 mb-2"
                                            style={{
                                              background: 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)',
                                              borderRadius: '12px',
                                              border: '1px solid #d1d5db'
                                            }}
                                          >
                                            <p className="small mb-1" style={{ color: '#374151', fontWeight: '600' }}>
                                              <FontAwesomeIcon icon={faCalendarAlt} className="me-2" style={{ color: '#667eea' }} />
                                              Période
                                            </p>
                                            <p className="small mb-0" style={{ color: '#6b7280' }}>
                                              {edu.startDate ? new Date(edu.startDate).toLocaleDateString('fr-FR') : 'Date non spécifiée'}
                                              {!edu.current && edu.endDate && ` - ${new Date(edu.endDate).toLocaleDateString('fr-FR')}`}
                                            </p>
                                          </div>
                                          {edu.location && (
                                            <div 
                                              className="p-3"
                                              style={{
                                                background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
                                                borderRadius: '12px',
                                                border: '1px solid #f59e0b'
                                              }}
                                            >
                                              <p className="small mb-1" style={{ color: '#92400e', fontWeight: '600' }}>
                                                <FontAwesomeIcon icon={faMapMarkerAlt} className="me-2" style={{ color: '#f59e0b' }} />
                                                Localisation
                                              </p>
                                              <p className="small mb-0" style={{ color: '#a16207' }}>
                                                {edu.location}
                                              </p>
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="alert alert-light border-0 text-center py-4" style={{ backgroundColor: '#f8f4ff' }}>
                        <FontAwesomeIcon icon={faExclamationCircle} className="me-2 text-muted" />
                        <span className="text-muted">Aucune formation renseignée</span>
                      </div>
                    )}
                  </div>
                ) : (
                  // Edit mode
                  <div>
                    {professionalProfile.education.map((edu, index) => (
                      <div 
                        key={index} 
                        className="border-0 rounded-4 p-4 mb-4 shadow-sm"
                        style={{
                          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
                          border: '2px solid #e2e8f0'
                        }}
                      >
                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <h6 className="mb-0" style={{ 
                            color: '#2D8BBA', 
                            fontWeight: '700',
                            fontSize: '18px'
                          }}>
                            <FontAwesomeIcon icon={faGraduationCap} className="me-2" />
                            Formation {index + 1}
                          </h6>
                          <button 
                            type="button" 
                            className="btn btn-outline-danger btn-sm rounded-pill px-3"
                            onClick={() => removeEducation(index)}
                            style={{
                              borderWidth: '2px',
                              fontWeight: '600'
                            }}
                          >
                            <FontAwesomeIcon icon={faMinus} className="me-1" />
                            Supprimer
                          </button>
                        </div>
                        
                        <div className="row g-3">
                          <div className="col-md-6">
                            <label className="form-label fw-bold text-secondary mb-2">
                              <FontAwesomeIcon icon={faGraduationCap} className="me-2 text-primary" />
                              Diplôme/Formation *
                            </label>
                            <input
                              type="text"
                              className="form-control border-2 rounded-3"
                              placeholder="Ex: Master en Informatique"
                              value={edu.degree || ''}
                              onChange={(e) => updateEducation(index, 'degree', e.target.value)}
                              style={{
                                borderColor: '#d1d5db',
                                padding: '12px 16px',
                                fontSize: '15px'
                              }}
                            />
                          </div>
                          <div className="col-md-6">
                            <label className="form-label fw-bold text-secondary mb-2">
                              <FontAwesomeIcon icon={faBriefcase} className="me-2 text-primary" />
                              Établissement *
                            </label>
                            <input
                              type="text"
                              className="form-control border-2 rounded-3"
                              placeholder="Ex: Université de Paris"
                              value={edu.institution || ''}
                              onChange={(e) => updateEducation(index, 'institution', e.target.value)}
                              style={{
                                borderColor: '#d1d5db',
                                padding: '12px 16px',
                                fontSize: '15px'
                              }}
                            />
                          </div>
                          
                          <div className="col-md-4">
                            <label className="form-label fw-bold text-secondary mb-2">
                              <FontAwesomeIcon icon={faCalendarAlt} className="me-2 text-success" />
                              Date de début
                            </label>
                            <input
                              type="date"
                              className="form-control border-2 rounded-3"
                              value={edu.startDate || ''}
                              onChange={(e) => updateEducation(index, 'startDate', e.target.value)}
                              style={{
                                borderColor: '#d1d5db',
                                padding: '12px 16px',
                                fontSize: '15px'
                              }}
                            />
                          </div>
                          <div className="col-md-4">
                            <label className="form-label fw-bold text-secondary mb-2">
                              <FontAwesomeIcon icon={faCalendarAlt} className="me-2 text-danger" />
                              Date de fin
                            </label>
                            <input
                              type="date"
                              className="form-control border-2 rounded-3"
                              value={edu.endDate || ''}
                              onChange={(e) => updateEducation(index, 'endDate', e.target.value)}
                              disabled={edu.current}
                              style={{
                                borderColor: edu.current ? '#e5e7eb' : '#d1d5db',
                                backgroundColor: edu.current ? '#f9fafb' : 'white',
                                padding: '12px 16px',
                                fontSize: '15px'
                              }}
                            />
                          </div>
                          <div className="col-md-4">
                            <label className="form-label fw-bold text-secondary mb-2">
                              Statut
                            </label>
                            <div className="form-check mt-3">
                              <input
                                className="form-check-input"
                                type="checkbox"
                                checked={edu.current || false}
                                onChange={(e) => updateEducation(index, 'current', e.target.checked)}
                                style={{ transform: 'scale(1.2)' }}
                              />
                              <label className="form-check-label fw-bold text-success ms-2">
                                Formation en cours
                              </label>
                            </div>
                          </div>
                          
                          <div className="col-md-6">
                            <label className="form-label fw-bold text-secondary mb-2">
                              <FontAwesomeIcon icon={faMapMarkerAlt} className="me-2 text-warning" />
                              Localisation
                            </label>
                            <input
                              type="text"
                              className="form-control border-2 rounded-3"
                              placeholder="Ex: Paris, France"
                              value={edu.location || ''}
                              onChange={(e) => updateEducation(index, 'location', e.target.value)}
                              style={{
                                borderColor: '#d1d5db',
                                padding: '12px 16px',
                                fontSize: '15px'
                              }}
                            />
                          </div>
                          <div className="col-md-6">
                            <label className="form-label fw-bold text-secondary mb-2">
                              <FontAwesomeIcon icon={faStar} className="me-2 text-warning" />
                              Mention/GPA (optionnel)
                            </label>
                            <input
                              type="text"
                              className="form-control border-2 rounded-3"
                              placeholder="Ex: Mention Bien, 16/20"
                              value={edu.gpa || ''}
                              onChange={(e) => updateEducation(index, 'gpa', e.target.value)}
                              style={{
                                borderColor: '#d1d5db',
                                padding: '12px 16px',
                                fontSize: '15px'
                              }}
                            />
                          </div>
                          
                          <div className="col-12">
                            <label className="form-label fw-bold text-secondary mb-2">
                              <FontAwesomeIcon icon={faEdit} className="me-2 text-info" />
                              Description de la formation
                            </label>
                            <textarea
                              className="form-control border-2 rounded-3"
                              rows="3"
                              placeholder="Décrivez le contenu de votre formation, les compétences acquises, projets réalisés..."
                              value={edu.description || ''}
                              onChange={(e) => updateEducation(index, 'description', e.target.value)}
                              style={{
                                borderColor: '#d1d5db',
                                padding: '12px 16px',
                                fontSize: '15px',
                                resize: 'vertical'
                              }}
                            ></textarea>
                          </div>
                        </div>
                      </div>
                    ))}
                    
                    <div className="text-center">
                      <button 
                        type="button" 
                        className="btn btn-outline-primary btn-lg rounded-pill px-4 py-3"
                        onClick={addEducation}
                        style={{
                          borderWidth: '2px',
                          fontWeight: '600',
                          background: 'linear-gradient(135deg, rgba(45, 139, 186, 0.1) 0%, rgba(45, 139, 186, 0.05) 100%)',
                          borderColor: '#2D8BBA',
                          color: '#2D8BBA',
                          transition: 'all 0.3s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.background = 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)';
                          e.target.style.color = 'white';
                          e.target.style.transform = 'translateY(-2px)';
                          e.target.style.boxShadow = '0 8px 25px rgba(45, 139, 186, 0.3)';
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.background = 'linear-gradient(135deg, rgba(45, 139, 186, 0.1) 0%, rgba(45, 139, 186, 0.05) 100%)';
                          e.target.style.color = '#2D8BBA';
                          e.target.style.transform = 'translateY(0)';
                          e.target.style.boxShadow = 'none';
                        }}
                      >
                        <FontAwesomeIcon icon={faPlus} className="me-2" />
                        Ajouter une formation
                      </button>
                    </div>
                  </div>
                )}
              </div>              {/* Professional Information Section */}
              <div className="mb-5">
                <h3 className="mb-4" style={{ 
                  color: '#2D8BBA', 
                  borderBottom: '3px solid #2D8BBA', 
                  paddingBottom: '12px',
                  fontSize: '24px',
                  fontWeight: '700'
                }}>
                  <FontAwesomeIcon icon={faStar} className="me-3" style={{ color: '#2D8BBA' }} />
                  Informations professionnelles
                </h3>
                
                {!editMode ? (
                  // Read-only view
                  <div>
                    {professionalProfile.currentStatus || professionalProfile.experienceLevel || professionalProfile.educationLevel ? (
                      <div className="row g-4">
                        {/* Current Status Card */}
                        <div className="col-md-4">
                          <div className="card border-0 shadow-lg h-100" style={{
                            background: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
                            borderRadius: '16px',
                            overflow: 'hidden',
                            transition: 'all 0.3s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-3px)';
                            e.currentTarget.style.boxShadow = '0 15px 35px rgba(59, 130, 246, 0.3)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.2)';
                          }}>
                            <div className="card-body p-4 text-white text-center">
                              <div className="mb-3">
                                <FontAwesomeIcon icon={faUserTie} style={{ 
                                  fontSize: '32px',
                                  opacity: '0.9'
                                }} />
                              </div>
                              <div className="mb-2">
                                <small style={{ 
                                  opacity: '0.8',
                                  fontSize: '12px',
                                  textTransform: 'uppercase',
                                  letterSpacing: '1px',
                                  fontWeight: '600'
                                }}>
                                  Statut actuel
                                </small>
                              </div>
                              <div style={{ 
                                fontSize: '18px', 
                                fontWeight: '700',
                                lineHeight: '1.2'
                              }}>
                                {professionalProfile.currentStatus || "Non renseigné"}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Experience Level Card */}
                        <div className="col-md-4">
                          <div className="card border-0 shadow-lg h-100" style={{
                            background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',
                            borderRadius: '16px',
                            overflow: 'hidden',
                            transition: 'all 0.3s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-3px)';
                            e.currentTarget.style.boxShadow = '0 15px 35px rgba(6, 182, 212, 0.3)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 8px 25px rgba(6, 182, 212, 0.2)';
                          }}>
                            <div className="card-body p-4 text-white text-center">
                              <div className="mb-3">
                                <FontAwesomeIcon icon={faChartBar} style={{ 
                                  fontSize: '32px',
                                  opacity: '0.9'
                                }} />
                              </div>
                              <div className="mb-2">
                                <small style={{ 
                                  opacity: '0.8',
                                  fontSize: '12px',
                                  textTransform: 'uppercase',
                                  letterSpacing: '1px',
                                  fontWeight: '600'
                                }}>
                                  Niveau d'expérience
                                </small>
                              </div>
                              <div style={{ 
                                fontSize: '18px', 
                                fontWeight: '700',
                                lineHeight: '1.2'
                              }}>
                                {professionalProfile.experienceLevel || "Non renseigné"}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Education Level Card */}
                        <div className="col-md-4">
                          <div className="card border-0 shadow-lg h-100" style={{
                            background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                            borderRadius: '16px',
                            overflow: 'hidden',
                            transition: 'all 0.3s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-3px)';
                            e.currentTarget.style.boxShadow = '0 15px 35px rgba(139, 92, 246, 0.3)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 8px 25px rgba(139, 92, 246, 0.2)';
                          }}>
                            <div className="card-body p-4 text-white text-center">
                              <div className="mb-3">
                                <FontAwesomeIcon icon={faGraduationCap} style={{ 
                                  fontSize: '32px',
                                  opacity: '0.9'
                                }} />
                              </div>
                              <div className="mb-2">
                                <small style={{ 
                                  opacity: '0.8',
                                  fontSize: '12px',
                                  textTransform: 'uppercase',
                                  letterSpacing: '1px',
                                  fontWeight: '600'
                                }}>
                                  Niveau d'éducation
                                </small>
                              </div>
                              <div style={{ 
                                fontSize: '18px', 
                                fontWeight: '700',
                                lineHeight: '1.2'
                              }}>
                                {professionalProfile.educationLevel || "Non renseigné"}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="alert alert-light border-0 text-center py-4" style={{ backgroundColor: '#f0f9ff' }}>
                        <FontAwesomeIcon icon={faExclamationCircle} className="me-2 text-muted" />
                        <span className="text-muted">Informations professionnelles non renseignées</span>
                      </div>
                    )}
                  </div>
                ) : (
                  // Edit mode
                  <div className="card border-0 shadow-lg" style={{
                    background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                    borderRadius: '16px',
                    overflow: 'hidden'
                  }}>
                    <div className="card-body p-4">
                      <div className="row g-4">
                        {/* Current Status */}
                        <div className="col-md-4">
                          <div className="form-group">
                            <label className="form-label fw-bold" style={{ color: '#2563eb', fontSize: '14px' }}>
                              <FontAwesomeIcon icon={faUserTie} className="me-2" style={{ color: '#2563eb' }} />
                              Statut actuel
                            </label>
                            <select
                              className="form-select"
                              style={{
                                borderRadius: '12px',
                                border: '2px solid #e5e7eb',
                                padding: '12px 16px',
                                fontSize: '16px',
                                transition: 'all 0.3s ease',
                                background: 'white',
                                boxShadow: '0 2px 8px rgba(37, 99, 235, 0.1)'
                              }}
                              value={professionalProfile.currentStatus}
                              onChange={(e) => setProfessionalProfile(prev => ({
                                ...prev,
                                currentStatus: e.target.value
                              }))}
                              onFocus={(e) => {
                                e.target.style.borderColor = '#2563eb';
                                e.target.style.boxShadow = '0 0 0 3px rgba(37, 99, 235, 0.1)';
                              }}
                              onBlur={(e) => {
                                e.target.style.borderColor = '#e5e7eb';
                                e.target.style.boxShadow = '0 2px 8px rgba(37, 99, 235, 0.1)';
                              }}
                            >
                              <option value="">Sélectionnez votre statut</option>
                              {currentStatusOptions.map((status) => (
                                <option key={status} value={status}>{status}</option>
                              ))}
                            </select>
                            <small className="text-muted mt-1">Votre situation professionnelle actuelle</small>
                          </div>
                        </div>

                        {/* Experience Level */}
                        <div className="col-md-4">
                          <div className="form-group">
                            <label className="form-label fw-bold" style={{ color: '#0891b2', fontSize: '14px' }}>
                              <FontAwesomeIcon icon={faChartBar} className="me-2" style={{ color: '#0891b2' }} />
                              Niveau d'expérience
                            </label>
                            <select
                              className="form-select"
                              style={{
                                borderRadius: '12px',
                                border: '2px solid #e5e7eb',
                                padding: '12px 16px',
                                fontSize: '16px',
                                transition: 'all 0.3s ease',
                                background: 'white',
                                boxShadow: '0 2px 8px rgba(8, 145, 178, 0.1)'
                              }}
                              value={professionalProfile.experienceLevel}
                              onChange={(e) => setProfessionalProfile(prev => ({
                                ...prev,
                                experienceLevel: e.target.value
                              }))}
                              onFocus={(e) => {
                                e.target.style.borderColor = '#0891b2';
                                e.target.style.boxShadow = '0 0 0 3px rgba(8, 145, 178, 0.1)';
                              }}
                              onBlur={(e) => {
                                e.target.style.borderColor = '#e5e7eb';
                                e.target.style.boxShadow = '0 2px 8px rgba(8, 145, 178, 0.1)';
                              }}
                            >
                              <option value="">Sélectionnez votre niveau</option>
                              {experienceLevels.map((level) => (
                                <option key={level} value={level}>{level}</option>
                              ))}
                            </select>
                            <small className="text-muted mt-1">Années d'expérience professionnelle</small>
                          </div>
                        </div>

                        {/* Education Level */}
                        <div className="col-md-4">
                          <div className="form-group">
                            <label className="form-label fw-bold" style={{ color: '#7c3aed', fontSize: '14px' }}>
                              <FontAwesomeIcon icon={faGraduationCap} className="me-2" style={{ color: '#7c3aed' }} />
                              Niveau d'éducation
                            </label>
                            <select
                              className="form-select"
                              style={{
                                borderRadius: '12px',
                                border: '2px solid #e5e7eb',
                                padding: '12px 16px',
                                fontSize: '16px',
                                transition: 'all 0.3s ease',
                                background: 'white',
                                boxShadow: '0 2px 8px rgba(124, 58, 237, 0.1)'
                              }}
                              value={professionalProfile.educationLevel}
                              onChange={(e) => setProfessionalProfile(prev => ({
                                ...prev,
                                educationLevel: e.target.value
                              }))}
                              onFocus={(e) => {
                                e.target.style.borderColor = '#7c3aed';
                                e.target.style.boxShadow = '0 0 0 3px rgba(124, 58, 237, 0.1)';
                              }}
                              onBlur={(e) => {
                                e.target.style.borderColor = '#e5e7eb';
                                e.target.style.boxShadow = '0 2px 8px rgba(124, 58, 237, 0.1)';
                              }}
                            >
                              <option value="">Sélectionnez votre niveau d'éducation</option>
                              {educationLevels.map((level) => (
                                <option key={level} value={level}>{level}</option>
                              ))}
                            </select>
                            <small className="text-muted mt-1">Plus haut diplôme obtenu</small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Job Preferences Section */}
              <div className="mb-5">
                <h3 className="mb-4" style={{ 
                  color: '#2D8BBA', 
                  borderBottom: '3px solid #2D8BBA', 
                  paddingBottom: '12px',
                  fontSize: '24px',
                  fontWeight: '700'
                }}>
                  <FontAwesomeIcon icon={faStar} className="me-3" style={{ color: '#2D8BBA' }} />
                  Préférences professionnelles
                </h3>

                {!editMode ? (
                  // Read-only view
                  <div>
                    {/* Preferred Roles Display */}
                    <div className="mb-5">
                      <div className="d-flex align-items-center mb-3">
                        <div className="bg-gradient-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style={{ width: '40px', height: '40px', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
                          <FontAwesomeIcon icon={faUserTie} className="text-white" />
                        </div>
                        <h5 className="mb-0 fw-bold" style={{ color: '#667eea' }}>Rôles préférés</h5>
                      </div>
                      {professionalProfile.preferredRoles.length > 0 ? (
                        <div className="d-flex flex-wrap gap-3">
                          {professionalProfile.preferredRoles.map((role, index) => (
                            <div key={index} className="position-relative">
                              <div className="badge-container" style={{
                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                borderRadius: '25px',
                                padding: '12px 20px',
                                color: 'white',
                                fontSize: '14px',
                                fontWeight: '600',
                                boxShadow: '0 4px 15px rgba(102, 126, 234, 0.3)',
                                border: '2px solid rgba(255,255,255,0.2)'
                              }}>
                                <FontAwesomeIcon icon={faUserTie} className="me-2" />
                                {role}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="alert alert-light border-0 text-center py-4" style={{ backgroundColor: '#f8f9fa' }}>
                          <FontAwesomeIcon icon={faExclamationCircle} className="me-2 text-muted" />
                          <span className="text-muted">Aucun rôle préféré renseigné</span>
                        </div>
                      )}
                    </div>

                    {/* Preferred Locations Display */}
                    <div className="mb-5">
                      <div className="d-flex align-items-center mb-3">
                        <div className="bg-gradient-success rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style={{ width: '40px', height: '40px', background: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)' }}>
                          <FontAwesomeIcon icon={faMapMarkerAlt} className="text-white" />
                        </div>
                        <h5 className="mb-0 fw-bold" style={{ color: '#11998e' }}>Localisations préférées</h5>
                      </div>
                      
                      <div className="row mb-4">
                        <div className="col-lg-6 mb-3">
                          <div className="card border-0 shadow-sm h-100" style={{ backgroundColor: '#f0f9ff' }}>
                            <div className="card-body p-4">
                              <div className="d-flex align-items-center mb-3">
                                <FontAwesomeIcon icon={faMapMarkerAlt} className="me-2" style={{ color: '#0284c7' }} />
                                <h6 className="mb-0 fw-bold" style={{ color: '#0284c7' }}>Villes</h6>
                              </div>
                              {professionalProfile.preferredLocations.cities.length > 0 ? (
                                <div className="d-flex flex-wrap gap-2">
                                  {professionalProfile.preferredLocations.cities.map((city, index) => (
                                    <span key={index} style={{ 
                                      background: 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%)',
                                      color: 'white',
                                      padding: '8px 16px',
                                      borderRadius: '20px',
                                      fontSize: '13px',
                                      fontWeight: '500',
                                      boxShadow: '0 2px 8px rgba(14, 165, 233, 0.3)'
                                    }}>
                                      <FontAwesomeIcon icon={faMapMarkerAlt} className="me-1" />
                                      {city}
                                    </span>
                                  ))}
                                </div>
                              ) : (
                                <p className="text-muted mb-0 small">Aucune ville renseignée</p>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="col-lg-6 mb-3">
                          <div className="card border-0 shadow-sm h-100" style={{ backgroundColor: '#f0fdf4' }}>
                            <div className="card-body p-4">
                              <div className="d-flex align-items-center mb-3">
                                <FontAwesomeIcon icon={faMapMarkerAlt} className="me-2" style={{ color: '#16a34a' }} />
                                <h6 className="mb-0 fw-bold" style={{ color: '#16a34a' }}>Régions</h6>
                              </div>
                              {professionalProfile.preferredLocations.regions.length > 0 ? (
                                <div className="d-flex flex-wrap gap-2">
                                  {professionalProfile.preferredLocations.regions.map((region, index) => (
                                    <span key={index} style={{ 
                                      background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
                                      color: 'white',
                                      padding: '8px 16px',
                                      borderRadius: '20px',
                                      fontSize: '13px',
                                      fontWeight: '500',
                                      boxShadow: '0 2px 8px rgba(34, 197, 94, 0.3)'
                                    }}>
                                      <FontAwesomeIcon icon={faMapMarkerAlt} className="me-1" />
                                      {region}
                                    </span>
                                  ))}
                                </div>
                              ) : (
                                <p className="text-muted mb-0 small">Aucune région renseignée</p>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="row">
                        <div className="col-lg-6 mb-3">
                          <div className="card border-0 shadow-sm" style={{ 
                            background: professionalProfile.preferredLocations.remoteWork ? 
                              'linear-gradient(135deg, #10b981 0%, #059669 100%)' : 
                              'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)'
                          }}>
                            <div className="card-body p-4 text-white">
                              <div className="d-flex align-items-center justify-content-between">
                                <div>
                                  <h6 className="mb-1 text-white fw-bold">Télétravail</h6>
                                  <p className="mb-0 small opacity-90">
                                    {professionalProfile.preferredLocations.remoteWork ? 'Accepté' : 'Non souhaité'}
                                  </p>
                                </div>
                                <div className="text-end">
                                  <FontAwesomeIcon 
                                    icon={professionalProfile.preferredLocations.remoteWork ? faCheckCircle : faTimes} 
                                    style={{ fontSize: '24px' }} 
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="col-lg-6 mb-3">
                          <div className="card border-0 shadow-sm" style={{ 
                            background: professionalProfile.preferredLocations.willingToRelocate ? 
                              'linear-gradient(135deg, #10b981 0%, #059669 100%)' : 
                              'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)'
                          }}>
                            <div className="card-body p-4 text-white">
                              <div className="d-flex align-items-center justify-content-between">
                                <div>
                                  <h6 className="mb-1 text-white fw-bold">Relocalisation</h6>
                                  <p className="mb-0 small opacity-90">
                                    {professionalProfile.preferredLocations.willingToRelocate ? 'Acceptée' : 'Non souhaitée'}
                                  </p>
                                </div>
                                <div className="text-end">
                                  <FontAwesomeIcon 
                                    icon={professionalProfile.preferredLocations.willingToRelocate ? faCheckCircle : faTimes} 
                                    style={{ fontSize: '24px' }} 
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Contract Types Display */}
                    <div className="mb-5">
                      <div className="d-flex align-items-center mb-3">
                        <div className="bg-gradient-warning rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style={{ width: '40px', height: '40px', background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)' }}>
                          <FontAwesomeIcon icon={faBriefcase} className="text-white" />
                        </div>
                        <h5 className="mb-0 fw-bold" style={{ color: '#d97706' }}>Types de contrat souhaités</h5>
                      </div>
                      {professionalProfile.contractTypes.length > 0 ? (
                        <div className="d-flex flex-wrap gap-3">
                          {professionalProfile.contractTypes.map((type, index) => (
                            <div key={index} className="position-relative">
                              <div style={{
                                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                                borderRadius: '25px',
                                padding: '12px 20px',
                                color: 'white',
                                fontSize: '14px',
                                fontWeight: '600',
                                boxShadow: '0 4px 15px rgba(245, 158, 11, 0.3)',
                                border: '2px solid rgba(255,255,255,0.2)'
                              }}>
                                <FontAwesomeIcon icon={faBriefcase} className="me-2" />
                                {type}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="alert alert-light border-0 text-center py-4" style={{ backgroundColor: '#fff7ed' }}>
                          <FontAwesomeIcon icon={faExclamationCircle} className="me-2 text-muted" />
                          <span className="text-muted">Aucun type de contrat renseigné</span>
                        </div>
                      )}
                    </div>

                    {/* Salary Expectations Display */}
                    <div className="mb-5">
                      <div className="d-flex align-items-center mb-4">
                        <div className="bg-gradient-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style={{ width: '50px', height: '50px', background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)', boxShadow: '0 8px 25px rgba(16, 185, 129, 0.3)' }}>
                          <FontAwesomeIcon icon={faEuroSign} className="text-white" style={{ fontSize: '20px' }} />
                        </div>
                        <h5 className="mb-0 fw-bold" style={{ color: '#10b981', fontSize: '20px' }}>Attentes salariales</h5>
                      </div>
                      
                      {professionalProfile.salaryExpectations.min || professionalProfile.salaryExpectations.max ? (
                        <div 
                          className="p-4 rounded-4"
                          style={{
                            background: 'linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%)',
                            border: '2px solid rgba(16, 185, 129, 0.2)',
                            boxShadow: '0 4px 15px rgba(16, 185, 129, 0.1)'
                          }}
                        >
                          <div className="row g-4">
                            <div className="col-md-4">
                              <div 
                                className="text-center p-3 rounded-3"
                                style={{
                                  background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                                  color: 'white',
                                  boxShadow: '0 4px 15px rgba(16, 185, 129, 0.3)'
                                }}
                              >
                                <div style={{ fontSize: '14px', opacity: '0.9', fontWeight: '500' }}>Minimum</div>
                                <div style={{ fontSize: '24px', fontWeight: '700', marginTop: '8px' }}>
                                  {professionalProfile.salaryExpectations.min ? 
                                    `${professionalProfile.salaryExpectations.min}` : 
                                    'Non spécifié'}
                                </div>
                                <div style={{ fontSize: '12px', opacity: '0.8', marginTop: '4px' }}>
                                  {professionalProfile.salaryExpectations.currency}
                                </div>
                              </div>
                            </div>
                            <div className="col-md-4">
                              <div 
                                className="text-center p-3 rounded-3"
                                style={{
                                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                                  color: 'white',
                                  boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)'
                                }}
                              >
                                <div style={{ fontSize: '14px', opacity: '0.9', fontWeight: '500' }}>Maximum</div>
                                <div style={{ fontSize: '24px', fontWeight: '700', marginTop: '8px' }}>
                                  {professionalProfile.salaryExpectations.max ? 
                                    `${professionalProfile.salaryExpectations.max}` : 
                                    'Non spécifié'}
                                </div>
                                <div style={{ fontSize: '12px', opacity: '0.8', marginTop: '4px' }}>
                                  {professionalProfile.salaryExpectations.currency}
                                </div>
                              </div>
                            </div>
                            <div className="col-md-4">
                              <div 
                                className="text-center p-3 rounded-3"
                                style={{
                                  background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                                  color: 'white',
                                  boxShadow: '0 4px 15px rgba(139, 92, 246, 0.3)'
                                }}
                              >
                                <div style={{ fontSize: '14px', opacity: '0.9', fontWeight: '500' }}>Période</div>
                                <div style={{ fontSize: '18px', fontWeight: '700', marginTop: '8px' }}>
                                  {professionalProfile.salaryExpectations.period === 'hour' ? '💰 Par heure' : 
                                   professionalProfile.salaryExpectations.period === 'day' ? '📅 Par jour' :
                                   professionalProfile.salaryExpectations.period === 'month' ? '📋 Par mois' : '📊 Par an'}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="alert alert-light border-0 text-center py-4" style={{ backgroundColor: '#f0fdf4' }}>
                          <FontAwesomeIcon icon={faExclamationCircle} className="me-2 text-muted" />
                          <span className="text-muted">Attentes salariales non renseignées</span>
                        </div>
                      )}
                    </div>

                    {/* Availability Display */}
                    <div className="mb-4">
                      <h5 className="mb-4" style={{ 
                        color: '#7c3aed', 
                        fontSize: '20px',
                        fontWeight: '700',
                        borderBottom: '2px solid #7c3aed',
                        paddingBottom: '8px'
                      }}>
                        <FontAwesomeIcon icon={faCalendarAlt} className="me-3" style={{ color: '#7c3aed' }} />
                        Disponibilité
                      </h5>
                      
                      {professionalProfile.availability.startDate || professionalProfile.availability.noticePeriod || professionalProfile.availability.partTime !== undefined ? (
                        <div className="row g-4">
                          {/* Start Date Card */}
                          <div className="col-md-4">
                            <div className="card border-0 shadow-lg h-100" style={{
                              background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                              borderRadius: '16px',
                              overflow: 'hidden',
                              transition: 'all 0.3s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.transform = 'translateY(-3px)';
                              e.currentTarget.style.boxShadow = '0 15px 35px rgba(124, 58, 237, 0.3)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.transform = 'translateY(0)';
                              e.currentTarget.style.boxShadow = '0 8px 25px rgba(124, 58, 237, 0.2)';
                            }}>
                              <div className="card-body p-4 text-white text-center">
                                <div className="mb-3">
                                  <FontAwesomeIcon icon={faCalendarAlt} style={{ 
                                    fontSize: '32px',
                                    opacity: '0.9'
                                  }} />
                                </div>
                                <div className="mb-2">
                                  <small style={{ 
                                    opacity: '0.8',
                                    fontSize: '12px',
                                    textTransform: 'uppercase',
                                    letterSpacing: '1px',
                                    fontWeight: '600'
                                  }}>
                                    Date de début
                                  </small>
                                </div>
                                <div style={{ 
                                  fontSize: '18px', 
                                  fontWeight: '700',
                                  lineHeight: '1.2'
                                }}>
                                  {professionalProfile.availability.startDate ? 
                                    new Date(professionalProfile.availability.startDate).toLocaleDateString('fr-FR') : 
                                    'Non spécifiée'}
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Notice Period Card */}
                          <div className="col-md-4">
                            <div className="card border-0 shadow-lg h-100" style={{
                              background: 'linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%)',
                              borderRadius: '16px',
                              overflow: 'hidden',
                              transition: 'all 0.3s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.transform = 'translateY(-3px)';
                              e.currentTarget.style.boxShadow = '0 15px 35px rgba(168, 85, 247, 0.3)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.transform = 'translateY(0)';
                              e.currentTarget.style.boxShadow = '0 8px 25px rgba(168, 85, 247, 0.2)';
                            }}>
                              <div className="card-body p-4 text-white text-center">
                                <div className="mb-3">
                                  <FontAwesomeIcon icon={faClock} style={{ 
                                    fontSize: '32px',
                                    opacity: '0.9'
                                  }} />
                                </div>
                                <div className="mb-2">
                                  <small style={{ 
                                    opacity: '0.8',
                                    fontSize: '12px',
                                    textTransform: 'uppercase',
                                    letterSpacing: '1px',
                                    fontWeight: '600'
                                  }}>
                                    Délai de préavis
                                  </small>
                                </div>
                                <div style={{ 
                                  fontSize: '18px', 
                                  fontWeight: '700',
                                  lineHeight: '1.2'
                                }}>
                                  {professionalProfile.availability.noticePeriod || 'Non spécifié'}
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Part Time Card */}
                          <div className="col-md-4">
                            <div className="card border-0 shadow-lg h-100" style={{
                              background: professionalProfile.availability.partTime 
                                ? 'linear-gradient(135deg, #c084fc 0%, #a855f7 100%)'
                                : 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
                              borderRadius: '16px',
                              overflow: 'hidden',
                              transition: 'all 0.3s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.transform = 'translateY(-3px)';
                              e.currentTarget.style.boxShadow = professionalProfile.availability.partTime 
                                ? '0 15px 35px rgba(192, 132, 252, 0.3)'
                                : '0 15px 35px rgba(107, 114, 128, 0.3)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.transform = 'translateY(0)';
                              e.currentTarget.style.boxShadow = professionalProfile.availability.partTime 
                                ? '0 8px 25px rgba(192, 132, 252, 0.2)'
                                : '0 8px 25px rgba(107, 114, 128, 0.2)';
                            }}>
                              <div className="card-body p-4 text-white text-center">
                                <div className="mb-3">
                                  <FontAwesomeIcon icon={professionalProfile.availability.partTime ? faCheck : faTimes} style={{ 
                                    fontSize: '32px',
                                    opacity: '0.9'
                                  }} />
                                </div>
                                <div className="mb-2">
                                  <small style={{ 
                                    opacity: '0.8',
                                    fontSize: '12px',
                                    textTransform: 'uppercase',
                                    letterSpacing: '1px',
                                    fontWeight: '600'
                                  }}>
                                    Temps partiel
                                  </small>
                                </div>
                                <div style={{ 
                                  fontSize: '18px', 
                                  fontWeight: '700',
                                  lineHeight: '1.2'
                                }}>
                                  {professionalProfile.availability.partTime ? 'Accepté' : 'Non souhaité'}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="alert alert-light border-0 text-center py-4" style={{ backgroundColor: '#faf5ff' }}>
                          <FontAwesomeIcon icon={faExclamationCircle} className="me-2 text-muted" />
                          <span className="text-muted">Disponibilité non renseignée</span>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  // Edit mode
                  <div>
                    {/* Preferred Roles */}
                    <div className="mb-4">
                      <label className="form-label">Rôles préférés</label>
                      <div className="row mb-2">
                        <div className="col-md-8">
                          <input
                            type="text"
                            className="form-control"
                            value={newRole}
                            onChange={(e) => setNewRole(e.target.value)}
                            placeholder="Ajouter un rôle (ex: Développeur Web, Chef de projet, etc.)"
                            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addRole())}
                          />
                        </div>
                        <div className="col-md-4">
                          <button type="button" className="btn btn-outline-primary" onClick={addRole}>
                            <FontAwesomeIcon icon={faPlus} className="me-2" />
                            Ajouter
                          </button>
                        </div>
                      </div>
                      <div className="d-flex flex-wrap gap-2">
                        {professionalProfile.preferredRoles.map((role, index) => (
                          <span key={index} className="badge bg-secondary d-flex align-items-center" style={{ fontSize: '14px', padding: '8px 12px' }}>
                            {role}
                            <button
                              type="button"
                              className="btn-close btn-close-white ms-2"
                              style={{ fontSize: '10px' }}
                              onClick={() => removeRole(role)}
                            ></button>
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Preferred Cities */}
                    <div className="mb-4">
                      <label className="form-label">Villes préférées</label>
                      <div className="row mb-2">
                        <div className="col-md-8">
                          <input
                            type="text"
                            className="form-control"
                            value={newCity}
                            onChange={(e) => setNewCity(e.target.value)}
                            placeholder="Ajouter une ville (ex: Paris, Lyon, etc.)"
                            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCity())}
                          />
                        </div>
                        <div className="col-md-4">
                          <button type="button" className="btn btn-outline-primary" onClick={addCity}>
                            <FontAwesomeIcon icon={faPlus} className="me-2" />
                            Ajouter
                          </button>
                        </div>
                      </div>
                      <div className="d-flex flex-wrap gap-2">
                        {professionalProfile.preferredLocations.cities.map((city, index) => (
                          <span key={index} className="badge bg-info d-flex align-items-center" style={{ fontSize: '14px', padding: '8px 12px' }}>
                            {city}
                            <button
                              type="button"
                              className="btn-close btn-close-white ms-2"
                              style={{ fontSize: '10px' }}
                              onClick={() => removeCity(city)}
                            ></button>
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Preferred Regions */}
                    <div className="mb-4">
                      <label className="form-label">Régions préférées</label>
                      <div className="row mb-2">
                        <div className="col-md-8">
                          <input
                            type="text"
                            className="form-control"
                            value={newRegion}
                            onChange={(e) => setNewRegion(e.target.value)}
                            placeholder="Ajouter une région (ex: Île-de-France, PACA, etc.)"
                            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addRegion())}
                          />
                        </div>
                        <div className="col-md-4">
                          <button type="button" className="btn btn-outline-primary" onClick={addRegion}>
                            <FontAwesomeIcon icon={faPlus} className="me-2" />
                            Ajouter
                          </button>
                        </div>
                      </div>
                      <div className="d-flex flex-wrap gap-2">
                        {professionalProfile.preferredLocations.regions.map((region, index) => (
                          <span key={index} className="badge bg-success d-flex align-items-center" style={{ fontSize: '14px', padding: '8px 12px' }}>
                            {region}
                            <button
                              type="button"
                              className="btn-close btn-close-white ms-2"
                              style={{ fontSize: '10px' }}
                              onClick={() => removeRegion(region)}
                            ></button>
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Remote Work & Relocation */}
                    <div className="mb-4">
                      <div className="row">
                        <div className="col-md-6">
                          <div className="form-check">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={professionalProfile.preferredLocations.remoteWork}
                              onChange={(e) => setProfessionalProfile(prev => ({
                                ...prev,
                                preferredLocations: {
                                  ...prev.preferredLocations,
                                  remoteWork: e.target.checked
                                }
                              }))}
                            />
                            <label className="form-check-label">
                              Ouvert au télétravail
                            </label>
                          </div>
                        </div>
                        <div className="col-md-6">
                          <div className="form-check">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={professionalProfile.preferredLocations.willingToRelocate}
                              onChange={(e) => setProfessionalProfile(prev => ({
                                ...prev,
                                preferredLocations: {
                                  ...prev.preferredLocations,
                                  willingToRelocate: e.target.checked
                                }
                              }))}
                            />
                            <label className="form-check-label">
                              Ouvert à la relocalisation
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Contract Types */}
                    <div className="mb-4">
                      <label className="form-label">Types de contrat souhaités</label>
                      <div className="d-flex flex-wrap gap-3">
                        {contractTypes.map((type) => (
                          <div key={type} className="form-check">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              id={`contract-${type}`}
                              checked={professionalProfile.contractTypes.includes(type)}
                              onChange={() => toggleContractType(type)}
                            />
                            <label className="form-check-label" htmlFor={`contract-${type}`}>
                              {type}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Salary Expectations */}
                    <div className="mb-4">
                      <label className="form-label">
                        <FontAwesomeIcon icon={faEuroSign} className="me-2" />
                        Attentes salariales
                      </label>
                      <div className="row">
                        <div className="col-md-4">
                          <input
                            type="number"
                            className="form-control"
                            placeholder="Salaire minimum"
                            value={professionalProfile.salaryExpectations.min}
                            onChange={(e) => setProfessionalProfile(prev => ({
                              ...prev,
                              salaryExpectations: { ...prev.salaryExpectations, min: e.target.value }
                            }))}
                          />
                        </div>
                        <div className="col-md-4">
                          <input
                            type="number"
                            className="form-control"
                            placeholder="Salaire maximum"
                            value={professionalProfile.salaryExpectations.max}
                            onChange={(e) => setProfessionalProfile(prev => ({
                              ...prev,
                              salaryExpectations: { ...prev.salaryExpectations, max: e.target.value }
                            }))}
                          />
                        </div>
                        <div className="col-md-4">
                          <select
                            className="form-select"
                            value={professionalProfile.salaryExpectations.period}
                            onChange={(e) => setProfessionalProfile(prev => ({
                              ...prev,
                              salaryExpectations: { ...prev.salaryExpectations, period: e.target.value }
                            }))}
                          >
                            {salaryPeriods.map((period) => (
                              <option key={period} value={period}>
                                {period === 'hour' ? 'Par heure' : 
                                 period === 'day' ? 'Par jour' :
                                 period === 'month' ? 'Par mois' : 'Par an'}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>

                    {/* Availability */}
                    <div className="mb-5">
                      <h5 className="mb-4" style={{ 
                        color: '#7c3aed', 
                        fontSize: '20px',
                        fontWeight: '700',
                        borderBottom: '2px solid #7c3aed',
                        paddingBottom: '8px'
                      }}>
                        <FontAwesomeIcon icon={faCalendarAlt} className="me-3" style={{ color: '#7c3aed' }} />
                        Disponibilité
                      </h5>
                      
                      <div className="card border-0 shadow-lg" style={{
                        background: 'linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%)',
                        borderRadius: '16px',
                        overflow: 'hidden'
                      }}>
                        <div className="card-body p-4">
                          <div className="row g-4">
                            {/* Start Date */}
                            <div className="col-md-4">
                              <div className="form-group">
                                <label className="form-label fw-bold" style={{ color: '#7c3aed', fontSize: '14px' }}>
                                  <FontAwesomeIcon icon={faCalendarAlt} className="me-2" style={{ color: '#7c3aed' }} />
                                  Date de début souhaitée
                                </label>
                                <input
                                  type="date"
                                  className="form-control"
                                  style={{
                                    borderRadius: '12px',
                                    border: '2px solid #e5e7eb',
                                    padding: '12px 16px',
                                    fontSize: '16px',
                                    transition: 'all 0.3s ease',
                                    background: 'white',
                                    boxShadow: '0 2px 8px rgba(124, 58, 237, 0.1)'
                                  }}
                                  value={professionalProfile.availability.startDate}
                                  onChange={(e) => setProfessionalProfile(prev => ({
                                    ...prev,
                                    availability: { ...prev.availability, startDate: e.target.value }
                                  }))}
                                  onFocus={(e) => {
                                    e.target.style.borderColor = '#7c3aed';
                                    e.target.style.boxShadow = '0 0 0 3px rgba(124, 58, 237, 0.1)';
                                  }}
                                  onBlur={(e) => {
                                    e.target.style.borderColor = '#e5e7eb';
                                    e.target.style.boxShadow = '0 2px 8px rgba(124, 58, 237, 0.1)';
                                  }}
                                />
                                <small className="text-muted mt-1">Indiquez quand vous pouvez commencer</small>
                              </div>
                            </div>

                            {/* Notice Period */}
                            <div className="col-md-4">
                              <div className="form-group">
                                <label className="form-label fw-bold" style={{ color: '#7c3aed', fontSize: '14px' }}>
                                  <FontAwesomeIcon icon={faClock} className="me-2" style={{ color: '#7c3aed' }} />
                                  Délai de préavis
                                </label>
                                <select
                                  className="form-select"
                                  style={{
                                    borderRadius: '12px',
                                    border: '2px solid #e5e7eb',
                                    padding: '12px 16px',
                                    fontSize: '16px',
                                    transition: 'all 0.3s ease',
                                    background: 'white',
                                    boxShadow: '0 2px 8px rgba(124, 58, 237, 0.1)'
                                  }}
                                  value={professionalProfile.availability.noticePeriod}
                                  onChange={(e) => setProfessionalProfile(prev => ({
                                    ...prev,
                                    availability: { ...prev.availability, noticePeriod: e.target.value }
                                  }))}
                                  onFocus={(e) => {
                                    e.target.style.borderColor = '#7c3aed';
                                    e.target.style.boxShadow = '0 0 0 3px rgba(124, 58, 237, 0.1)';
                                  }}
                                  onBlur={(e) => {
                                    e.target.style.borderColor = '#e5e7eb';
                                    e.target.style.boxShadow = '0 2px 8px rgba(124, 58, 237, 0.1)';
                                  }}
                                >
                                  <option value="">Sélectionnez un délai</option>
                                  {availabilityOptions.map((period) => (
                                    <option key={period} value={period}>{period}</option>
                                  ))}
                                </select>
                                <small className="text-muted mt-1">Temps nécessaire pour quitter votre poste actuel</small>
                              </div>
                            </div>

                            {/* Part Time Preference */}
                            <div className="col-md-4">
                              <div className="form-group">
                                <label className="form-label fw-bold" style={{ color: '#7c3aed', fontSize: '14px' }}>
                                  <FontAwesomeIcon icon={faClock} className="me-2" style={{ color: '#7c3aed' }} />
                                  Type de contrat
                                </label>
                                <div className="mt-3">
                                  <div className="form-check" style={{
                                    background: 'white',
                                    borderRadius: '12px',
                                    padding: '16px 20px',
                                    border: '2px solid #e5e7eb',
                                    transition: 'all 0.3s ease',
                                    boxShadow: '0 2px 8px rgba(124, 58, 237, 0.1)'
                                  }}>
                                    <input
                                      className="form-check-input"
                                      type="checkbox"
                                      style={{
                                        width: '20px',
                                        height: '20px',
                                        marginTop: '2px'
                                      }}
                                      checked={professionalProfile.availability.partTime}
                                      onChange={(e) => setProfessionalProfile(prev => ({
                                        ...prev,
                                        availability: { ...prev.availability, partTime: e.target.checked }
                                      }))}
                                    />
                                    <label className="form-check-label ms-3" style={{ 
                                      fontSize: '16px',
                                      fontWeight: '600',
                                      color: '#374151'
                                    }}>
                                      <FontAwesomeIcon 
                                        icon={professionalProfile.availability.partTime ? faCheck : faTimes} 
                                        className="me-2" 
                                        style={{ 
                                          color: professionalProfile.availability.partTime ? '#10b981' : '#ef4444'
                                        }} 
                                      />
                                      Ouvert au temps partiel
                                    </label>
                                  </div>
                                  <small className="text-muted mt-1">Cochez si vous acceptez les postes à temps partiel</small>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Industries Section */}
              <div className="mb-5">
                <h3 className="mb-4" style={{ 
                  color: '#2D8BBA', 
                  borderBottom: '3px solid #2D8BBA', 
                  paddingBottom: '12px',
                  fontSize: '24px',
                  fontWeight: '700'
                }}>
                  <FontAwesomeIcon icon={faBriefcase} className="me-3" style={{ color: '#2D8BBA' }} />
                  Secteurs d'intérêt
                </h3>
                
                {!editMode ? (
                  // Read-only view
                  <div>
                    {professionalProfile.industries.length > 0 ? (
                      <div className="d-flex flex-wrap gap-3">
                        {professionalProfile.industries.map((industry, index) => (
                          <div key={index} className="position-relative">
                            <div style={{
                              background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                              borderRadius: '20px',
                              padding: '12px 20px',
                              color: 'white',
                              fontSize: '14px',
                              fontWeight: '600',
                              boxShadow: '0 4px 15px rgba(245, 158, 11, 0.3)',
                              border: '2px solid rgba(255,255,255,0.2)',
                              transition: 'all 0.3s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.target.style.transform = 'translateY(-2px)';
                              e.target.style.boxShadow = '0 6px 20px rgba(245, 158, 11, 0.4)';
                            }}
                            onMouseLeave={(e) => {
                              e.target.style.transform = 'translateY(0)';
                              e.target.style.boxShadow = '0 4px 15px rgba(245, 158, 11, 0.3)';
                            }}>
                              <FontAwesomeIcon icon={faBriefcase} className="me-2" />
                              {industry}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="alert alert-light border-0 text-center py-4" style={{ backgroundColor: '#fff7ed' }}>
                        <FontAwesomeIcon icon={faExclamationCircle} className="me-2 text-muted" />
                        <span className="text-muted">Aucun secteur d'intérêt renseigné</span>
                      </div>
                    )}
                  </div>
                ) : (
                  // Edit mode
                  <div>
                    <div className="row mb-3">
                      <div className="col-md-8">
                        <select
                          className="form-select"
                          value={newIndustry}
                          onChange={(e) => setNewIndustry(e.target.value)}
                        >
                          <option value="">Sélectionnez un secteur</option>
                          {industryOptions.map((industry) => (
                            <option key={industry} value={industry}>{industry}</option>
                          ))}
                        </select>
                      </div>
                      <div className="col-md-4">
                        <button type="button" className="btn btn-outline-primary" onClick={addIndustry}>
                          <FontAwesomeIcon icon={faPlus} className="me-2" />
                          Ajouter
                        </button>
                      </div>
                    </div>
                    <div className="d-flex flex-wrap gap-2">
                      {professionalProfile.industries.map((industry, index) => (
                        <span key={index} className="badge bg-warning d-flex align-items-center" style={{ fontSize: '14px', padding: '8px 12px' }}>
                          {industry}
                          <button
                            type="button"
                            className="btn-close btn-close-white ms-2"
                            style={{ fontSize: '10px' }}
                            onClick={() => removeIndustry(industry)}
                          ></button>
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Languages Section */}
              <div className="mb-5">
                <h3 className="mb-4" style={{ 
                  color: '#2D8BBA', 
                  borderBottom: '3px solid #2D8BBA', 
                  paddingBottom: '12px',
                  fontSize: '24px',
                  fontWeight: '700'
                }}>
                  <FontAwesomeIcon icon={faGraduationCap} className="me-3" style={{ color: '#2D8BBA' }} />
                  Langues
                </h3>
                
                {!editMode ? (
                  // Read-only view
                  <div>
                    {professionalProfile.languages.length > 0 ? (
                      <div className="d-flex flex-wrap gap-3">
                        {professionalProfile.languages.map((language, index) => (
                          <div key={index} className="position-relative">
                            <div style={{
                              background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                              borderRadius: '20px',
                              padding: '14px 20px',
                              color: 'white',
                              fontSize: '14px',
                              fontWeight: '600',
                              boxShadow: '0 6px 20px rgba(139, 92, 246, 0.3)',
                              border: '2px solid rgba(255,255,255,0.2)',
                              transition: 'all 0.3s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.target.style.transform = 'translateY(-3px)';
                              e.target.style.boxShadow = '0 8px 25px rgba(139, 92, 246, 0.4)';
                            }}
                            onMouseLeave={(e) => {
                              e.target.style.transform = 'translateY(0)';
                              e.target.style.boxShadow = '0 6px 20px rgba(139, 92, 246, 0.3)';
                            }}>
                              <div className="d-flex align-items-center">
                                <FontAwesomeIcon icon={faGraduationCap} className="me-2" />
                                <div>
                                  <div className="fw-bold">{language.language}</div>
                                  <small className="opacity-85">Niveau: {language.level}</small>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="alert alert-light border-0 text-center py-4" style={{ backgroundColor: '#faf5ff' }}>
                        <FontAwesomeIcon icon={faExclamationCircle} className="me-2 text-muted" />
                        <span className="text-muted">Aucune langue renseignée</span>
                      </div>
                    )}
                  </div>
                ) : (
                  // Edit mode
                  <div>
                    <div className="row mb-3">
                      <div className="col-md-5">
                        <select
                          className="form-select"
                          value={newLanguage.language}
                          onChange={(e) => setNewLanguage(prev => ({ ...prev, language: e.target.value }))}
                        >
                          <option value="">Sélectionnez une langue</option>
                          {languageOptions.map((language) => (
                            <option key={language} value={language}>{language}</option>
                          ))}
                        </select>
                      </div>
                      <div className="col-md-4">
                        <select
                          className="form-select"
                          value={newLanguage.level}
                          onChange={(e) => setNewLanguage(prev => ({ ...prev, level: e.target.value }))}
                        >
                          {languageLevels.map((level) => (
                            <option key={level} value={level}>{level}</option>
                          ))}
                        </select>
                      </div>
                      <div className="col-md-3">
                        <button type="button" className="btn btn-outline-primary w-100" onClick={addLanguage}>
                          <FontAwesomeIcon icon={faPlus} />
                        </button>
                      </div>
                    </div>
                    <div className="d-flex flex-wrap gap-2">
                      {professionalProfile.languages.map((language, index) => (
                        <span key={index} className="badge bg-dark d-flex align-items-center" style={{ fontSize: '14px', padding: '8px 12px' }}>
                          <strong>{language.language}</strong>
                          <span className="ms-2 opacity-75">({language.level})</span>
                          <button
                            type="button"
                            className="btn-close btn-close-white ms-2"
                            style={{ fontSize: '10px' }}
                            onClick={() => removeLanguage(language)}
                          ></button>
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Notification Preferences */}
              {editMode && (
                <div className="mb-5">
                  <h3 className="mb-4" style={{ 
                    color: '#2D8BBA', 
                    borderBottom: '3px solid #2D8BBA', 
                    paddingBottom: '12px',
                    fontSize: '24px',
                    fontWeight: '700'
                  }}>
                    <FontAwesomeIcon icon={faCog} className="me-3" style={{ color: '#2D8BBA' }} />
                    Préférences de notifications
                  </h3>
                  
                  <div 
                    className="p-4 rounded-4 shadow-sm"
                    style={{
                      background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
                      border: '2px solid #e2e8f0'
                    }}
                  >
                    <div className="row">
                      <div className="col-md-6">
                        <h6 className="mb-3" style={{ color: '#4a5568', fontWeight: '600' }}>
                          <FontAwesomeIcon icon={faEnvelope} className="me-2 text-primary" />
                          Types de notifications
                        </h6>
                        
                        <div className="form-check mb-3">
                          <input
                            className="form-check-input"
                            type="checkbox"
                            checked={professionalProfile.notifications.emailNotifications}
                            onChange={(e) => setProfessionalProfile(prev => ({
                              ...prev,
                              notifications: { ...prev.notifications, emailNotifications: e.target.checked }
                            }))}
                            style={{ transform: 'scale(1.2)' }}
                          />
                          <label className="form-check-label fw-bold text-dark ms-2">
                            <FontAwesomeIcon icon={faEnvelope} className="me-2 text-info" />
                            Notifications par email
                          </label>
                        </div>
                        
                        <div className="form-check mb-3">
                          <input
                            className="form-check-input"
                            type="checkbox"
                            checked={professionalProfile.notifications.jobAlerts}
                            onChange={(e) => setProfessionalProfile(prev => ({
                              ...prev,
                              notifications: { ...prev.notifications, jobAlerts: e.target.checked }
                            }))}
                            style={{ transform: 'scale(1.2)' }}
                          />
                          <label className="form-check-label fw-bold text-dark ms-2">
                            <FontAwesomeIcon icon={faBriefcase} className="me-2 text-warning" />
                            Alertes d'emploi
                          </label>
                        </div>
                        
                        <div className="form-check mb-3">
                          <input
                            className="form-check-input"
                            type="checkbox"
                            checked={professionalProfile.notifications.applicationUpdates}
                            onChange={(e) => setProfessionalProfile(prev => ({
                              ...prev,
                              notifications: { ...prev.notifications, applicationUpdates: e.target.checked }
                            }))}
                            style={{ transform: 'scale(1.2)' }}
                          />
                          <label className="form-check-label fw-bold text-dark ms-2">
                            <FontAwesomeIcon icon={faCheckCircle} className="me-2 text-success" />
                            Mises à jour des candidatures
                          </label>
                        </div>
                        
                        <div className="form-check mb-2">
                          <input
                            className="form-check-input"
                            type="checkbox"
                            checked={professionalProfile.notifications.matchingJobs}
                            onChange={(e) => setProfessionalProfile(prev => ({
                              ...prev,
                              notifications: { ...prev.notifications, matchingJobs: e.target.checked }
                            }))}
                            style={{ transform: 'scale(1.2)' }}
                          />
                          <label className="form-check-label fw-bold text-dark ms-2">
                            <FontAwesomeIcon icon={faStar} className="me-2 text-primary" />
                            Emplois correspondants
                          </label>
                        </div>
                      </div>
                      
                      <div className="col-md-6">
                        <h6 className="mb-3" style={{ color: '#4a5568', fontWeight: '600' }}>
                          <FontAwesomeIcon icon={faCalendarAlt} className="me-2 text-primary" />
                          Fréquence des notifications
                        </h6>
                        
                        <div 
                          className="p-3 rounded-3"
                          style={{
                            background: 'linear-gradient(135deg, #fff 0%, #f7fafc 100%)',
                            border: '2px solid #e2e8f0'
                          }}
                        >
                          <label className="form-label fw-bold text-secondary mb-2">
                            <FontAwesomeIcon icon={faCog} className="me-2 text-info" />
                            Choisissez votre fréquence
                          </label>
                          <select
                            className="form-select border-2 rounded-3"
                            value={professionalProfile.notifications.frequency}
                            onChange={(e) => setProfessionalProfile(prev => ({
                              ...prev,
                              notifications: { ...prev.notifications, frequency: e.target.value }
                            }))}
                            style={{
                              borderColor: '#d1d5db',
                              padding: '12px 16px',
                              fontSize: '15px',
                              fontWeight: '500'
                            }}
                          >
                            {notificationFrequencies.map((freq) => (
                              <option key={freq} value={freq}>
                                {freq === 'immediate' ? '⚡ Immédiat' :
                                 freq === 'daily' ? '📅 Quotidien' :
                                 freq === 'weekly' ? '📋 Hebdomadaire' : '📝 Mensuel'}
                              </option>
                            ))}
                          </select>
                          
                          <div className="mt-3 p-2 rounded-2" style={{ backgroundColor: '#e0f2fe' }}>
                            <small className="text-info">
                              <FontAwesomeIcon icon={faExclamationTriangle} className="me-1" />
                              Les notifications importantes seront toujours envoyées immédiatement.
                            </small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Submit button - only show in edit mode */}
              {editMode && (
                <button
                  type="submit"
                  disabled={saving}
                  style={{
                    background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                    border: 'none',
                    borderRadius: '8px',
                    color: 'white',
                    padding: '12px 24px',
                    fontSize: '16px',
                    fontWeight: '600',
                    cursor: 'pointer',
                    width: '100%',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-2px)';
                    e.target.style.boxShadow = '0 8px 20px rgba(45, 139, 186, 0.3)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0)';
                    e.target.style.boxShadow = 'none';
                  }}
                >
                  {saving ? (
                    <>
                      <div className="spinner-border spinner-border-sm me-2" role="status">
                        <span className="visually-hidden">Sauvegarde...</span>
                      </div>
                      Sauvegarde en cours...
                    </>
                  ) : (
                    <>
                      <FontAwesomeIcon icon={faSave} className="me-2" />
                      Mettre à jour le profil
                    </>
                  )}
                </button>
              )}
            </form>
          </div>
        </div>
      </div>
    </Layout>
  );
}
