'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowLeft,
  faFileAlt,
  faUpload,
  faTrash,
  faEye,
  faDownload,
  faPlus,
  faTimes,
  faCheck,
  faSpinner
} from '@fortawesome/free-solid-svg-icons';
import Layout from '@/components/layout/Layout';
import styles from '@/styles/Profile.module.css';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import axios from 'axios';
import { jwtDecode } from 'jwt-decode';

export default function CVLibrary() {
  const router = useRouter();
  
  // State for CV library
  const [cvLibrary, setCvLibrary] = useState([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    tags: '',
    suitableFor: 'CDI,CDD',
    isDefault: false
  });
  const [selectedFile, setSelectedFile] = useState(null);
  const fileInputRef = useRef(null);

  // Fetch CV Library
  const fetchCVLibrary = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        toast.error("Vous devez être connecté pour accéder à cette page");
        router.push('/signin');
        return;
      }

      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      const response = await axios.get("http://*************:5000/api/cv-library?active=true", config);
      setCvLibrary(response.data.cvs || []);
    } catch (err) {
      console.error("Failed to fetch CV library:", err);
      if (err.response?.status === 401) {
        toast.error("Session expirée, veuillez vous reconnecter");
        router.push('/signin');
      } else {
        toast.error("Erreur lors du chargement de la bibliothèque CV");
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCVLibrary();
  }, []);

  // Handle file selection
  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Upload CV to library
  const uploadCVToLibrary = async (e) => {
    e.preventDefault();

    if (!selectedFile) {
      toast.error('Veuillez sélectionner un fichier');
      return;
    }

    if (!formData.title) {
      toast.error('Veuillez entrer un titre pour le CV');
      return;
    }

    setUploading(true);

    try {
      const token = localStorage.getItem("token");
      if (!token) {
        toast.error("Vous devez être connecté");
        return;
      }

      const uploadFormData = new FormData();
      uploadFormData.append('cv', selectedFile);
      uploadFormData.append('title', formData.title);
      uploadFormData.append('description', formData.description);
      uploadFormData.append('tags', formData.tags);
      uploadFormData.append('suitableFor', formData.suitableFor);
      uploadFormData.append('isDefault', formData.isDefault);

      const response = await fetch("http://*************:5000/api/cv-library/upload", {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: uploadFormData,
      });

      if (response.ok) {
        const result = await response.json();
        toast.success("CV ajouté à votre bibliothèque avec succès !");
        setShowUploadForm(false);

        // Reset form
        setFormData({
          title: '',
          description: '',
          tags: '',
          suitableFor: 'CDI,CDD',
          isDefault: false
        });
        setSelectedFile(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }

        // Refresh the CV library
        fetchCVLibrary();
      } else {
        const error = await response.json();
        toast.error(error.message || "Erreur lors de l'ajout du CV");
      }
    } catch (err) {
      console.error("Failed to upload CV:", err);
      toast.error("Erreur lors de l'ajout du CV");
    } finally {
      setUploading(false);
    }
  };

  // Set CV as default
  const setAsDefault = async (cvId) => {
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        toast.error("Vous devez être connecté");
        return;
      }

      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      await axios.put(`http://*************:5000/api/cv-library/${cvId}/set-default`, {}, config);

      // Update local state
      setCvLibrary(prevCVs =>
        prevCVs.map(cv => ({
          ...cv,
          isDefault: cv._id === cvId
        }))
      );

      toast.success('CV défini comme CV par défaut');
    } catch (error) {
      console.error('Error setting default CV:', error);
      toast.error('Erreur lors de la mise à jour du CV par défaut');
    }
  };

  // Delete CV
  const deleteCV = async (cvId) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer ce CV ?')) {
      return;
    }

    try {
      const token = localStorage.getItem("token");
      if (!token) {
        toast.error("Vous devez être connecté");
        return;
      }

      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      await axios.delete(`http://*************:5000/api/cv-library/${cvId}`, config);

      // Refresh the CV library after deletion
      fetchCVLibrary();

      toast.success('CV supprimé avec succès');
    } catch (error) {
      console.error('Error deleting CV:', error);
      toast.error('Erreur lors de la suppression du CV');
    }
  };

  // Render loading state
  if (loading) {
    return (
      <Layout headerStyle={2} footerStyle={3}>
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '50vh' }}>
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Chargement...</span>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout headerStyle={2} footerStyle={3}>
      <style jsx>{`
        @media (max-width: 767.98px) {
          .cv-table {
            display: none !important;
          }
          .cv-mobile-cards {
            display: block !important;
          }
        }
        @media (min-width: 768px) {
          .cv-table {
            display: block !important;
          }
          .cv-mobile-cards {
            display: none !important;
          }
        }
      `}</style>
      <ToastContainer position="top-right" autoClose={3000} />
      <div className="container" style={{ marginTop: '150px', marginBottom: '120px', minHeight: '70vh' }}>
        <div style={{
          background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
          borderRadius: '24px',
          padding: '3px',
          marginBottom: '30px',
          boxShadow: '0 25px 50px rgba(45, 139, 186, 0.15)'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
            borderRadius: '21px',
            padding: '50px',
            boxShadow: '0 30px 60px rgba(0,0,0,0.08)'
          }}>
            {/* Mobile buttons row */}
            <div className="d-md-none d-flex justify-content-between align-items-center mb-3">
              <button
                onClick={() => router.back()}
                className="btn-link p-2 d-flex align-items-center justify-content-center"
                style={{
                  background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                  border: 'none',
                  borderRadius: '12px',
                  color: 'white',
                  padding: '12px',
                  textDecoration: 'none',
                  fontSize: '16px',
                  fontWeight: '600',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 8px 25px rgba(45, 139, 186, 0.3)',
                  minWidth: '48px',
                  minHeight: '48px'
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = 'translateY(-2px)';
                  e.target.style.boxShadow = '0 12px 35px rgba(45, 139, 186, 0.4)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 8px 25px rgba(45, 139, 186, 0.3)';
                }}
              >
                <FontAwesomeIcon icon={faArrowLeft} />
              </button>

              {!showUploadForm && (
                <button
                  onClick={() => setShowUploadForm(true)}
                  className="btn-link p-2 d-flex align-items-center justify-content-center"
                  style={{
                    background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                    border: 'none',
                    borderRadius: '12px',
                    color: 'white',
                    padding: '12px',
                    textDecoration: 'none',
                    fontSize: '16px',
                    fontWeight: '600',
                    transition: 'all 0.3s ease',
                    boxShadow: '0 8px 20px rgba(45, 139, 186, 0.3)',
                    minWidth: '48px',
                    minHeight: '48px'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-2px)';
                    e.target.style.boxShadow = '0 12px 25px rgba(45, 139, 186, 0.4)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0)';
                    e.target.style.boxShadow = '0 8px 20px rgba(45, 139, 186, 0.3)';
                  }}
                >
                  <FontAwesomeIcon icon={faPlus} />
                </button>
              )}
            </div>

            {/* Desktop layout */}
            <div className="d-none d-md-flex justify-content-between align-items-start mb-4">
              <div>
                <button
                  onClick={() => router.back()}
                  className="btn-link p-2 mb-3 d-flex align-items-center"
                  style={{
                    background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                    border: 'none',
                    borderRadius: '12px',
                    color: 'white',
                    padding: '14px 28px',
                    textDecoration: 'none',
                    fontSize: '16px',
                    fontWeight: '600',
                    transition: 'all 0.3s ease',
                    boxShadow: '0 8px 25px rgba(45, 139, 186, 0.3)'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-2px)';
                    e.target.style.boxShadow = '0 12px 35px rgba(45, 139, 186, 0.4)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0)';
                    e.target.style.boxShadow = '0 8px 25px rgba(45, 139, 186, 0.3)';
                  }}
                >
                  <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
                  Retour au profil
                </button>
                <h1 style={{
                  fontSize: '36px',
                  fontWeight: '800',
                  background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  marginBottom: '12px',
                  letterSpacing: '-0.5px'
                }}>
                  <FontAwesomeIcon icon={faFileAlt} className="me-3" style={{ color: '#2D8BBA' }} />
                  Ma Bibliothèque de CVs
                </h1>
                <p style={{ 
                  color: '#64748b', 
                  fontSize: '18px', 
                  marginBottom: '0',
                  fontWeight: '500'
                }}>
                  Gérez et organisez vos CVs pour vos candidatures
                </p>
              </div>
              {!showUploadForm && (
                <button
                  onClick={() => setShowUploadForm(true)}
                  style={{
                    background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                    border: 'none',
                    borderRadius: '12px',
                    color: 'white',
                    padding: '12px 24px',
                    fontSize: '16px',
                    fontWeight: '600',
                    boxShadow: '0 8px 20px rgba(45, 139, 186, 0.3)',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-2px)';
                    e.target.style.boxShadow = '0 12px 25px rgba(45, 139, 186, 0.4)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0)';
                    e.target.style.boxShadow = '0 8px 20px rgba(45, 139, 186, 0.3)';
                  }}
                >
                  <FontAwesomeIcon icon={faPlus} className="me-2" />
                  Ajouter un CV
                </button>
              )}
            </div>

            {showUploadForm ? (
              <div style={{
                background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                borderRadius: '20px',
                padding: '40px',
                marginBottom: '30px',
                boxShadow: '0 15px 35px rgba(45, 139, 186, 0.1)',
                border: '2px solid rgba(45, 139, 186, 0.1)'
              }}>
            <div className="card-body">
              <div className="d-flex justify-content-between align-items-center mb-4">
                <h5 style={{
                  fontSize: '24px',
                  fontWeight: '700',
                  color: '#2D8BBA',
                  margin: '0'
                }}>Ajouter un nouveau CV</h5>
                <button 
                  type="button" 
                  style={{
                    background: 'none',
                    border: 'none',
                    fontSize: '24px',
                    color: '#6c757d',
                    cursor: 'pointer',
                    padding: '8px',
                    borderRadius: '8px',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.background = '#f8f9fa';
                    e.target.style.color = '#dc3545';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = 'none';
                    e.target.style.color = '#6c757d';
                  }}
                  onClick={() => {
                    setShowUploadForm(false);
                    setFormData({
                      title: '',
                      description: '',
                      tags: '',
                      suitableFor: 'CDI,CDD',
                      isDefault: false
                    });
                    setSelectedFile(null);
                    if (fileInputRef.current) {
                      fileInputRef.current.value = '';
                    }
                  }}
                >
                  <FontAwesomeIcon icon={faTimes} />
                </button>
              </div>
              
              <form onSubmit={uploadCVToLibrary}>
                <div className="mb-4">
                  <label htmlFor="title" style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontWeight: '600',
                    color: '#374151',
                    fontSize: '16px'
                  }}>Titre du CV *</label>
                  <input 
                    type="text" 
                    style={{
                      width: '100%',
                      padding: '14px 18px',
                      border: '2px solid #e2e8f0',
                      borderRadius: '12px',
                      fontSize: '16px',
                      transition: 'all 0.3s ease',
                      outline: 'none'
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = '#2D8BBA';
                      e.target.style.boxShadow = '0 0 0 4px rgba(45, 139, 186, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = '#e2e8f0';
                      e.target.style.boxShadow = 'none';
                    }}
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    required 
                  />
                </div>
                
                <div className="mb-4">
                  <label htmlFor="cvFile" style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontWeight: '600',
                    color: '#374151',
                    fontSize: '16px'
                  }}>Fichier CV (PDF, DOC, DOCX) *</label>
                  <input 
                    type="file" 
                    style={{
                      width: '100%',
                      padding: '14px 18px',
                      border: '2px solid #e2e8f0',
                      borderRadius: '12px',
                      fontSize: '16px',
                      transition: 'all 0.3s ease',
                      outline: 'none'
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = '#2D8BBA';
                      e.target.style.boxShadow = '0 0 0 4px rgba(45, 139, 186, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = '#e2e8f0';
                      e.target.style.boxShadow = 'none';
                    }}
                    id="cvFile"
                    name="cvFile"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    accept=".pdf,.doc,.docx"
                    required
                  />
                  {selectedFile && (
                    <div style={{
                      marginTop: '12px',
                      padding: '12px',
                      background: 'rgba(45, 139, 186, 0.1)',
                      borderRadius: '8px',
                      fontSize: '14px',
                      color: '#2D8BBA'
                    }}>
                      <FontAwesomeIcon icon={faFileAlt} className="me-2" />
                      {selectedFile.name} ({(selectedFile.size / 1024).toFixed(2)} KB)
                    </div>
                  )}
                </div>
                
                <div className="mb-4">
                  <label htmlFor="description" style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontWeight: '600',
                    color: '#374151',
                    fontSize: '16px'
                  }}>Description</label>
                  <textarea 
                    style={{
                      width: '100%',
                      padding: '14px 18px',
                      border: '2px solid #e2e8f0',
                      borderRadius: '12px',
                      fontSize: '16px',
                      transition: 'all 0.3s ease',
                      outline: 'none',
                      resize: 'vertical',
                      minHeight: '100px'
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = '#2D8BBA';
                      e.target.style.boxShadow = '0 0 0 4px rgba(45, 139, 186, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = '#e2e8f0';
                      e.target.style.boxShadow = 'none';
                    }}
                    id="description"
                    name="description"
                    rows="3"
                    value={formData.description}
                    onChange={handleInputChange}
                  ></textarea>
                </div>
                
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label htmlFor="tags" className="form-label">Tags</label>
                    <input 
                      type="text" 
                      className="form-control" 
                      id="tags"
                      name="tags"
                      value={formData.tags}
                      onChange={handleInputChange}
                      placeholder="javascript,react,nodejs"
                    />
                    <div className="form-text">Séparez les tags par des virgules</div>
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label htmlFor="suitableFor" className="form-label">Type de Contract</label>
                    <input 
                      type="text" 
                      className="form-control" 
                      id="suitableFor"
                      name="suitableFor"
                      value={formData.suitableFor}
                      onChange={handleInputChange}
                      placeholder="CDI,CDD,Stage"
                    />
                    <div className="form-text">Types de contrats pour ce CV</div>
                  </div>
                </div>
                
                <div className="form-check mb-4">
                  <input 
                    className="form-check-input" 
                    type="checkbox" 
                    id="isDefault"
                    name="isDefault"
                    checked={formData.isDefault}
                    onChange={handleInputChange}
                  />
                  <label className="form-check-label" htmlFor="isDefault">
                    Définir comme CV par défaut
                  </label>
                </div>
                
                <div className="d-flex gap-2">
                  <button
                    type="submit"
                    disabled={uploading || !selectedFile || !formData.title}
                    style={{
                      background: uploading || !selectedFile || !formData.title
                        ? '#6c757d'
                        : 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                      border: 'none',
                      borderRadius: '8px',
                      color: 'white',
                      padding: '10px 20px',
                      fontSize: '14px',
                      fontWeight: '500',
                      cursor: uploading || !selectedFile || !formData.title ? 'not-allowed' : 'pointer',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      if (!uploading && selectedFile && formData.title) {
                        e.target.style.transform = 'translateY(-1px)';
                        e.target.style.boxShadow = '0 4px 12px rgba(45, 139, 186, 0.3)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.transform = 'translateY(0)';
                      e.target.style.boxShadow = 'none';
                    }}
                  >
                    {uploading ? (
                      <>
                        <FontAwesomeIcon icon={faSpinner} className="me-2" spin />
                        Téléchargement...
                      </>
                    ) : (
                      <>
                        <FontAwesomeIcon icon={faUpload} className="me-2" />
                        Télécharger le CV
                      </>
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowUploadForm(false);
                      setFormData({
                        title: '',
                        description: '',
                        tags: '',
                        suitableFor: 'CDI,CDD',
                        isDefault: false
                      });
                      setSelectedFile(null);
                      if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                      }
                    }}
                    style={{
                      background: '#fff',
                      border: '2px solid #6c757d',
                      borderRadius: '8px',
                      color: '#6c757d',
                      padding: '10px 20px',
                      fontSize: '14px',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.background = '#6c757d';
                      e.target.style.color = 'white';
                      e.target.style.transform = 'translateY(-1px)';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.background = '#fff';
                      e.target.style.color = '#6c757d';
                      e.target.style.transform = 'translateY(0)';
                    }}
                  >
                    <FontAwesomeIcon icon={faTimes} className="me-2" />
                    Annuler
                  </button>
                </div>
              </form>
            </div>
          </div>
        ) : cvLibrary.length > 0 ? (
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '40px',
            boxShadow: '0 15px 35px rgba(0, 0, 0, 0.08)',
            border: '1px solid #e5e7eb'
          }}>
            <div>
              <div className="d-flex justify-content-between align-items-center mb-5">
                <h5 style={{
                  fontSize: '24px',
                  fontWeight: '700',
                  color: '#2D8BBA',
                  margin: '0'
                }}>Mes CVs</h5>
                <span style={{
                  background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                  color: 'white',
                  padding: '8px 16px',
                  borderRadius: '20px',
                  fontSize: '14px',
                  fontWeight: '600'
                }}>
                  {cvLibrary.length} CV{cvLibrary.length > 1 ? 's' : ''}
                </span>
              </div>
              
              {/* Desktop Table */}
              <div className="cv-table" style={{ overflowX: 'auto' }}>
                <table style={{
                  width: '100%',
                  borderCollapse: 'separate',
                  borderSpacing: '0',
                  borderRadius: '12px',
                  overflow: 'hidden',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}>
                  <thead>
                    <tr style={{ background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)' }}>
                      <th style={{ 
                        width: '30%', 
                        padding: '20px 24px',
                        fontSize: '14px',
                        fontWeight: '700',
                        color: '#374151',
                        textAlign: 'left',
                        borderBottom: '2px solid #e2e8f0'
                      }}>Titre</th>
                      <th style={{ 
                        padding: '20px 24px',
                        fontSize: '14px',
                        fontWeight: '700',
                        color: '#374151',
                        textAlign: 'left',
                        borderBottom: '2px solid #e2e8f0'
                      }}>Description</th>
                      <th style={{ 
                        padding: '20px 24px',
                        fontSize: '14px',
                        fontWeight: '700',
                        color: '#374151',
                        textAlign: 'left',
                        borderBottom: '2px solid #e2e8f0'
                      }}>Dernière utilisation</th>
                      <th style={{ 
                        padding: '20px 24px',
                        fontSize: '14px',
                        fontWeight: '700',
                        color: '#374151',
                        textAlign: 'left',
                        borderBottom: '2px solid #e2e8f0'
                      }}>Statut</th>
                      <th style={{ 
                        padding: '20px 24px',
                        fontSize: '14px',
                        fontWeight: '700',
                        color: '#374151',
                        textAlign: 'right',
                        borderBottom: '2px solid #e2e8f0'
                      }}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {cvLibrary.map((cv, index) => (
                      <tr key={cv._id} style={{
                        background: cv.isDefault 
                          ? 'linear-gradient(135deg, rgba(45, 139, 186, 0.05) 0%, rgba(26, 95, 122, 0.05) 100%)'
                          : index % 2 === 0 ? '#ffffff' : '#f9fafb',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.background = 'linear-gradient(135deg, rgba(45, 139, 186, 0.08) 0%, rgba(26, 95, 122, 0.08) 100%)';
                        e.currentTarget.style.transform = 'translateY(-1px)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.background = cv.isDefault 
                          ? 'linear-gradient(135deg, rgba(45, 139, 186, 0.05) 0%, rgba(26, 95, 122, 0.05) 100%)'
                          : index % 2 === 0 ? '#ffffff' : '#f9fafb';
                        e.currentTarget.style.transform = 'translateY(0)';
                      }}>
                        <td style={{ padding: '20px 24px', borderBottom: '1px solid #e2e8f0' }}>
                          <div className="d-flex align-items-center">
                            <div style={{
                              background: cv.isDefault 
                                ? 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)'
                                : '#6c757d',
                              borderRadius: '12px',
                              width: '48px',
                              height: '48px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              marginRight: '16px'
                            }}>
                              <FontAwesomeIcon 
                                icon={faFileAlt} 
                                style={{ color: 'white', fontSize: '18px' }}
                              />
                            </div>
                            <div>
                              <div style={{
                                fontWeight: '600',
                                fontSize: '16px',
                                color: '#374151',
                                marginBottom: '4px'
                              }}>{cv.title}</div>
                              <div style={{
                                color: '#6b7280',
                                fontSize: '14px'
                              }}>
                                {cv.originalName || 'Aucun fichier'}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td style={{ padding: '20px 24px', borderBottom: '1px solid #e2e8f0' }}>
                          <div style={{
                            color: '#6b7280',
                            fontSize: '14px',
                            maxWidth: '300px',
                            lineHeight: '1.5'
                          }}>
                            {cv.description || 'Aucune description'}
                          </div>
                        </td>
                        <td style={{ padding: '20px 24px', borderBottom: '1px solid #e2e8f0' }}>
                          <div style={{
                            fontSize: '14px',
                            color: '#374151',
                            marginBottom: '4px'
                          }}>
                            {cv.lastUsed 
                              ? new Date(cv.lastUsed).toLocaleDateString('fr-FR') 
                              : 'Jamais utilisé'}
                          </div>
                          <div style={{
                            color: '#6b7280',
                            fontSize: '12px'
                          }}>
                            {cv.timesUsed || 0} utilisation{cv.timesUsed !== 1 ? 's' : ''}
                          </div>
                        </td>
                        <td style={{ padding: '20px 24px', borderBottom: '1px solid #e2e8f0' }}>
                          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                            {cv.isDefault ? (
                              <span style={{
                                background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                                color: 'white',
                                padding: '4px 12px',
                                borderRadius: '12px',
                                fontSize: '12px',
                                fontWeight: '600',
                                textAlign: 'center'
                              }}>Par défaut</span>
                            ) : (
                              <span style={{
                                background: '#6c757d',
                                color: 'white',
                                padding: '4px 12px',
                                borderRadius: '12px',
                                fontSize: '12px',
                                fontWeight: '600',
                                textAlign: 'center'
                              }}>Secondaire</span>
                            )}
                            {cv.isActive ? (
                              <span style={{
                                background: '#10b981',
                                color: 'white',
                                padding: '4px 12px',
                                borderRadius: '12px',
                                fontSize: '12px',
                                fontWeight: '600',
                                textAlign: 'center'
                              }}>Actif</span>
                            ) : (
                              <span style={{
                                background: '#ef4444',
                                color: 'white',
                                padding: '4px 12px',
                                borderRadius: '12px',
                                fontSize: '12px',
                                fontWeight: '600',
                                textAlign: 'center'
                              }}>Inactif</span>
                            )}
                          </div>
                        </td>
                        <td style={{ padding: '20px 24px', borderBottom: '1px solid #e2e8f0', textAlign: 'right' }}>
                          <div style={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
                            <a
                              href={cv.fileUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              title="Voir le CV"
                              style={{
                                background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                                border: 'none',
                                borderRadius: '10px',
                                color: 'white',
                                padding: '10px 12px',
                                fontSize: '14px',
                                textDecoration: 'none',
                                display: 'inline-flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                transition: 'all 0.3s ease'
                              }}
                              onMouseEnter={(e) => {
                                e.target.style.transform = 'translateY(-2px)';
                                e.target.style.boxShadow = '0 8px 20px rgba(45, 139, 186, 0.3)';
                              }}
                              onMouseLeave={(e) => {
                                e.target.style.transform = 'translateY(0)';
                                e.target.style.boxShadow = 'none';
                              }}
                            >
                              <FontAwesomeIcon icon={faEye} />
                            </a>
                            <a
                              href={cv.fileUrl}
                              download
                              title="Télécharger"
                              style={{
                                background: '#6c757d',
                                border: 'none',
                                borderRadius: '10px',
                                color: 'white',
                                padding: '10px 12px',
                                fontSize: '14px',
                                textDecoration: 'none',
                                display: 'inline-flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                transition: 'all 0.3s ease'
                              }}
                              onMouseEnter={(e) => {
                                e.target.style.background = '#5a6268';
                                e.target.style.transform = 'translateY(-2px)';
                                e.target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
                              }}
                              onMouseLeave={(e) => {
                                e.target.style.background = '#6c757d';
                                e.target.style.transform = 'translateY(0)';
                                e.target.style.boxShadow = 'none';
                              }}
                            >
                              <FontAwesomeIcon icon={faDownload} />
                            </a>
                            {!cv.isDefault && (
                              <button 
                                style={{
                                  background: 'white',
                                  border: '2px solid #10b981',
                                  borderRadius: '10px',
                                  color: '#10b981',
                                  padding: '10px 12px',
                                  fontSize: '14px',
                                  cursor: 'pointer',
                                  transition: 'all 0.3s ease'
                                }}
                                onMouseEnter={(e) => {
                                  e.target.style.background = '#10b981';
                                  e.target.style.color = 'white';
                                  e.target.style.transform = 'translateY(-2px)';
                                  e.target.style.boxShadow = '0 4px 12px rgba(16, 185, 129, 0.3)';
                                }}
                                onMouseLeave={(e) => {
                                  e.target.style.background = 'white';
                                  e.target.style.color = '#10b981';
                                  e.target.style.transform = 'translateY(0)';
                                  e.target.style.boxShadow = 'none';
                                }}
                                onClick={() => setAsDefault(cv._id)}
                                title="Définir comme par défaut"
                              >
                                <FontAwesomeIcon icon={faCheck} />
                              </button>
                            )}
                            <button 
                              style={{
                                background: '#ef4444',
                                border: '2px solid #dc2626',
                                borderRadius: '10px',
                                color: 'white',
                                padding: '10px 12px',
                                fontSize: '14px',
                                cursor: 'pointer',
                                transition: 'all 0.3s ease'
                              }}
                              onMouseEnter={(e) => {
                                e.target.style.background = '#dc2626';
                                e.target.style.transform = 'translateY(-2px)';
                                e.target.style.boxShadow = '0 4px 12px rgba(220, 38, 38, 0.3)';
                              }}
                              onMouseLeave={(e) => {
                                e.target.style.background = '#ef4444';
                                e.target.style.transform = 'translateY(0)';
                                e.target.style.boxShadow = 'none';
                              }}
                              onClick={() => deleteCV(cv._id)}
                              title="Supprimer"
                            >
                              <FontAwesomeIcon icon={faTrash} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Mobile Cards */}
              <div className="cv-mobile-cards">
                {cvLibrary.map((cv, index) => (
                  <div key={cv._id} style={{
                    background: cv.isDefault
                      ? 'linear-gradient(135deg, rgba(45, 139, 186, 0.05) 0%, rgba(26, 95, 122, 0.05) 100%)'
                      : 'white',
                    borderRadius: '16px',
                    padding: '20px',
                    marginBottom: '16px',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
                    border: cv.isDefault ? '2px solid rgba(45, 139, 186, 0.2)' : '1px solid #e5e7eb'
                  }}>
                    {/* Header with title and status */}
                    <div style={{ marginBottom: '16px' }}>
                      <div className="d-flex align-items-center mb-2">
                        <div className="d-flex align-items-center">
                          <div style={{
                            background: cv.isDefault
                              ? 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)'
                              : '#6c757d',
                            borderRadius: '12px',
                            width: '40px',
                            height: '40px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            marginRight: '12px'
                          }}>
                            <FontAwesomeIcon
                              icon={faFileAlt}
                              style={{ color: 'white', fontSize: '16px' }}
                            />
                          </div>
                          <div>
                            <h6 style={{
                              fontSize: '16px',
                              fontWeight: '700',
                              color: '#374151',
                              margin: '0',
                              lineHeight: '1.2'
                            }}>
                              {cv.title}
                            </h6>
                            <div style={{
                              color: '#6b7280',
                              fontSize: '12px',
                              marginTop: '2px'
                            }}>
                              {cv.originalName || 'Aucun fichier'}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Description */}
                    {cv.description && (
                      <div style={{
                        color: '#6b7280',
                        fontSize: '14px',
                        marginBottom: '12px',
                        lineHeight: '1.4'
                      }}>
                        {cv.description}
                      </div>
                    )}

                    {/* Usage info */}
                    <div style={{
                      fontSize: '12px',
                      color: '#6b7280',
                      marginBottom: '16px'
                    }}>
                      <div>
                        <strong>Dernière utilisation:</strong> {cv.lastUsed
                          ? new Date(cv.lastUsed).toLocaleDateString('fr-FR')
                          : 'Jamais utilisé'}
                      </div>
                      <div>
                        <strong>Utilisations:</strong> {cv.timesUsed || 0} fois
                      </div>
                    </div>

                    {/* Actions */}
                    <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                      <a
                        href={cv.fileUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{
                          background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                          border: 'none',
                          borderRadius: '8px',
                          color: 'white',
                          padding: '8px 12px',
                          fontSize: '12px',
                          textDecoration: 'none',
                          display: 'inline-flex',
                          alignItems: 'center',
                          fontWeight: '500'
                        }}
                      >
                        <FontAwesomeIcon icon={faEye} className="me-1" />
                        Voir
                      </a>
                      <a
                        href={cv.fileUrl}
                        download
                        style={{
                          background: '#6c757d',
                          border: 'none',
                          borderRadius: '8px',
                          color: 'white',
                          padding: '8px 12px',
                          fontSize: '12px',
                          textDecoration: 'none',
                          display: 'inline-flex',
                          alignItems: 'center',
                          fontWeight: '500'
                        }}
                      >
                        <FontAwesomeIcon icon={faDownload} className="me-1" />
                        Télécharger
                      </a>
                      {!cv.isDefault && (
                        <button
                          style={{
                            background: 'white',
                            border: '2px solid #10b981',
                            borderRadius: '8px',
                            color: '#10b981',
                            padding: '8px 12px',
                            fontSize: '12px',
                            cursor: 'pointer',
                            fontWeight: '500'
                          }}
                          onClick={() => setAsDefault(cv._id)}
                        >
                          <FontAwesomeIcon icon={faCheck} className="me-1" />
                          Par défaut
                        </button>
                      )}
                      <button
                        style={{
                          background: '#ef4444',
                          border: 'none',
                          borderRadius: '8px',
                          color: 'white',
                          padding: '8px 12px',
                          fontSize: '12px',
                          cursor: 'pointer',
                          fontWeight: '500'
                        }}
                        onClick={() => deleteCV(cv._id)}
                      >
                        <FontAwesomeIcon icon={faTrash} className="me-1" />
                        Supprimer
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '60px 40px',
            textAlign: 'center',
            boxShadow: '0 15px 35px rgba(0, 0, 0, 0.08)',
            border: '1px solid #e5e7eb'
          }}>
            <div style={{
              background: 'linear-gradient(135deg, rgba(45, 139, 186, 0.1) 0%, rgba(26, 95, 122, 0.1) 100%)',
              borderRadius: '50%',
              width: '80px',
              height: '80px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px auto'
            }}>
              <FontAwesomeIcon icon={faFileAlt} style={{ fontSize: '32px', color: '#2D8BBA' }} />
            </div>
            <h5 style={{
              fontSize: '24px',
              fontWeight: '700',
              color: '#374151',
              marginBottom: '12px'
            }}>Aucun CV trouvé</h5>
            <p style={{
              color: '#6b7280',
              fontSize: '16px',
              marginBottom: '32px',
              maxWidth: '400px',
              margin: '0 auto 32px auto'
            }}>
              Vous n'avez pas encore ajouté de CV à votre bibliothèque.
            </p>
            <button 
              style={{
                background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                border: 'none',
                borderRadius: '12px',
                color: 'white',
                padding: '16px 32px',
                fontSize: '16px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                boxShadow: '0 8px 20px rgba(45, 139, 186, 0.3)'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 12px 25px rgba(45, 139, 186, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 8px 20px rgba(45, 139, 186, 0.3)';
              }}
              onClick={() => setShowUploadForm(true)}
            >
              <FontAwesomeIcon icon={faPlus} className="me-2" />
              Ajouter votre premier CV
            </button>
          </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
}
