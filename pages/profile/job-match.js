import { useEffect, useState } from "react";
import { useRouter } from 'next/router';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faArrowLeft,
  faSearch,
  faMapMarkerAlt,
  faBuilding,
  faPercentage,
  faSpinner,
  faEye,
  faHeart,
  faShare,
  faUser,
  faChartBar,
  faCog,
  faExclamationTriangle,
  faCheckCircle
} from "@fortawesome/free-solid-svg-icons";
import Layout from "@/components/layout/Layout";
import styles from "@/styles/Profile.module.css";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import axios from 'axios';

export default function JobMatch() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [matchedJobs, setMatchedJobs] = useState([]);
  const [profileStatus, setProfileStatus] = useState(null);
  const [statistics, setStatistics] = useState(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalJobs: 0
  });
  const [filters, setFilters] = useState({
    minScore: 30,
    page: 1,
    limit: 12
  });

  // Get auth headers
  const getAuthHeaders = () => {
    const token = localStorage.getItem("token");
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  };

  // API base configuration
  const apiConfig = {
    baseURL: 'http://82.165.144.72:5000/api/job-match'
  };

  // Check Profile Status
  const checkProfileStatus = async () => {
    try {
      const response = await axios.get(`${apiConfig.baseURL}/profile-status`, {
        headers: getAuthHeaders()
      });
      
      setProfileStatus(response.data);
      return response.data;
    } catch (error) {
      console.error("Failed to check profile status:", error);
      if (error.response?.status === 401) {
        toast.error("Session expirée, veuillez vous reconnecter");
        router.push('/signin');
      } else {
        toast.error("Erreur lors de la vérification du profil");
      }
      return null;
    }
  };

  // Fetch Matched Jobs
  const fetchMatchedJobs = async (page = 1, minScore = 30) => {
    try {
      const response = await axios.get(
        `${apiConfig.baseURL}/matched-jobs?minScore=${minScore}&page=${page}&limit=${filters.limit}`,
        { headers: getAuthHeaders() }
      );
      
      if (response.data) {
        setMatchedJobs(response.data.matchedJobs || []);
        setPagination(response.data.pagination || {
          currentPage: 1,
          totalPages: 1,
          totalJobs: 0
        });
      }
    } catch (error) {
      console.error("Failed to fetch matched jobs:", error);
      if (error.response?.status === 401) {
        toast.error("Session expirée, veuillez vous reconnecter");
        router.push('/signin');
      } else {
        toast.error("Erreur lors du chargement des emplois correspondants");
      }
    }
  };

  // Fetch Statistics
  const fetchStatistics = async () => {
    try {
      const response = await axios.get(`${apiConfig.baseURL}/stats`, {
        headers: getAuthHeaders()
      });
      
      if (response.data) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error("Failed to fetch statistics:", error);
    }
  };

  // Initialize data
  const initializeData = async () => {
    setLoading(true);
    
    const token = localStorage.getItem("token");
    if (!token) {
      toast.error("Vous devez être connecté pour accéder à cette page");
      router.push('/signin');
      return;
    }

    const profileData = await checkProfileStatus();
    
    if (profileData && profileData.canUseJobMatching) {
      await Promise.all([
        fetchMatchedJobs(filters.page, filters.minScore),
        fetchStatistics()
      ]);
    }
    
    setLoading(false);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    fetchMatchedJobs(newFilters.page || filters.page, newFilters.minScore || filters.minScore);
  };

  // Get match score color
  const getMatchScoreColor = (score) => {
    if (score >= 70) return '#28a745'; // Green
    if (score >= 40) return '#ffc107'; // Yellow
    if (score >= 20) return '#fd7e14'; // Orange
    return '#dc3545'; // Red
  };

  // Get match score label
  const getMatchScoreLabel = (score) => {
    if (score >= 70) return 'Excellente correspondance';
    if (score >= 40) return 'Bonne correspondance';
    if (score >= 20) return 'Correspondance faible';
    return 'Pas de correspondance';
  };

  useEffect(() => {
    initializeData();
  }, []);

  return (
    <Layout headerStyle={2} footerStyle={3}>
      <ToastContainer position="top-right" autoClose={3000} />
      <div className="container" style={{ marginTop: '150px', marginBottom: '120px', minHeight: '70vh' }}>
        {/* Enhanced Main Container */}
        <div style={{
          background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
          borderRadius: '24px',
          padding: '3px',
          marginBottom: '30px',
          boxShadow: '0 25px 50px rgba(45, 139, 186, 0.15)'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
            borderRadius: '21px',
            padding: '50px',
            boxShadow: '0 30px 60px rgba(0,0,0,0.08)'
          }}>
            {/* Enhanced Back Button */}
            <button
              onClick={() => router.back()}
              className="btn-link p-2 mb-4 d-flex align-items-center"
              style={{
                background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                border: 'none',
                borderRadius: '12px',
                color: 'white',
                padding: '14px 28px',
                textDecoration: 'none',
                fontSize: '16px',
                fontWeight: '600',
                transition: 'all 0.3s ease',
                boxShadow: '0 8px 25px rgba(45, 139, 186, 0.3)'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 12px 35px rgba(45, 139, 186, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 8px 25px rgba(45, 139, 186, 0.3)';
              }}
            >
              <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
              Retour au profil
            </button>

            {/* Enhanced Header Section */}
            <div className="d-flex justify-content-between align-items-center mb-5">
              <div>
                <h1 style={{
                  fontSize: '36px',
                  fontWeight: '800',
                  background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                  WebkitBackgroundClip: 'text',
                  color:'white',
                  WebkitTextFillColor: 'transparent',
                  marginBottom: '12px',
                  letterSpacing: '-0.5px'
                }}>
                  <FontAwesomeIcon icon={faSearch} className="me-3" style={{ color: '#2D8BBA' }} />
                  Emplois correspondant à mon profil
                </h1>
                <p style={{ 
                  color: '#64748b', 
                  fontSize: '18px', 
                  marginBottom: '0',
                  fontWeight: '500'
                }}>
                  Découvrez les offres qui correspondent le mieux à votre profil professionnel
                </p>
                {statistics && (
                  <div className="mt-4 d-flex gap-6 flex-wrap">
                    <div style={{
                      background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                      padding: '12px 20px',
                      borderRadius: '16px',
                      border: '2px solid rgba(45, 139, 186, 0.1)',
                      boxShadow: '0 4px 15px rgba(45, 139, 186, 0.1)'
                    }}>
                      <span style={{ color: '#2D8BBA', fontWeight: '700', fontSize: '16px' }}>
                        <FontAwesomeIcon icon={faChartBar} className="me-2" />
                        {statistics.totalJobs} Offres d'emplois • Score moyen: {Math.round(statistics.matchingStats?.averageMatchScore || 0)}%
                      </span>
                    </div>
                    <div style={{
                      background: 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)',
                      padding: '12px 20px',
                      borderRadius: '16px',
                      border: '2px solid rgba(34, 197, 94, 0.1)',
                      boxShadow: '0 4px 15px rgba(34, 197, 94, 0.1)'
                    }}>
                      <span style={{ color: '#16a34a', fontWeight: '700', fontSize: '16px' }}>
                        <FontAwesomeIcon icon={faCheckCircle} className="me-2" />
                        {statistics.matchingStats?.highMatches || 0} excellentes correspondances
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Profile Status Check */}
            {profileStatus && !profileStatus.canUseJobMatching && (
              <div className="alert alert-warning d-flex align-items-center mb-4" role="alert">
                <FontAwesomeIcon icon={faExclamationTriangle} className="me-3" style={{ fontSize: '24px' }} />
                <div className="flex-grow-1">
                  <h6 className="alert-heading mb-2">Profil incomplet</h6>
                  <p className="mb-2">
                    Votre profil n'est pas assez complet pour utiliser le système de correspondance d'emplois. 
                    Complétude actuelle: {profileStatus.completionPercentage}%
                  </p>
                  {profileStatus.missingRequirements && profileStatus.missingRequirements.length > 0 && (
                    <ul className="mb-3 small">
                      {profileStatus.missingRequirements.map((requirement, index) => (
                        <li key={index}>{requirement}</li>
                      ))}
                    </ul>
                  )}
                  <button
                    onClick={() => router.push('/profile/personal-info')}
                    className="btn btn-warning"
                  >
                    <FontAwesomeIcon icon={faUser} className="me-2" />
                    Compléter mon profil
                  </button>
                </div>
              </div>
            )}

            {/* Enhanced Filters */}
            {profileStatus && profileStatus.canUseJobMatching && (
              <div className="row mb-5">
                <div className="col-md-6">
                  <div style={{
                    background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                    padding: '24px',
                    borderRadius: '20px',
                    border: '2px solid #e2e8f0',
                    boxShadow: '0 8px 25px rgba(0,0,0,0.04)'
                  }}>
                    <label className="form-label fw-bold mb-3" style={{ 
                      color: '#334155', 
                      fontSize: '16px',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <FontAwesomeIcon icon={faPercentage} className="me-2" style={{ color: '#2D8BBA' }} />
                      Score minimum de correspondance
                    </label>
                    <select 
                      className="form-select"
                      style={{
                        borderRadius: '12px',
                        border: '2px solid #e2e8f0',
                        padding: '14px 18px',
                        fontSize: '16px',
                        fontWeight: '500',
                        background: 'white',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                        transition: 'all 0.3s ease'
                      }}
                      value={filters.minScore}
                      onChange={(e) => handleFilterChange({ minScore: parseInt(e.target.value), page: 1 })}
                      onFocus={(e) => {
                        e.target.style.borderColor = '#2D8BBA';
                        e.target.style.boxShadow = '0 0 0 4px rgba(45, 139, 186, 0.1)';
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = '#e2e8f0';
                        e.target.style.boxShadow = '0 4px 12px rgba(0,0,0,0.05)';
                      }}
                    >
                      <option value={70}>🌟 Excellente correspondance (70%+)</option>
                      <option value={40}>✅ Bonne correspondance (40%+)</option>
                      <option value={20}>⚠️ Correspondance faible (20%+)</option>
                      <option value={0}>📋 Toutes les offres</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

          {loading ? (
            <div className="text-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Chargement...</span>
              </div>
              <p className="mt-3 text-muted">Recherche des emplois correspondant à votre profil...</p>
            </div>
          ) : profileStatus && !profileStatus.canUseJobMatching ? (
            <div className="text-center py-5">
              <FontAwesomeIcon icon={faCog} size="3x" className="text-muted mb-3" />
              <h4>Configuration du profil requise</h4>
              <p className="text-muted">
                Complétez votre profil pour découvrir les emplois qui vous correspondent.
              </p>
            </div>
          ) : matchedJobs.length > 0 ? (
            <div>
              <div className="row g-4">
                {matchedJobs.map((job) => (
                  <div key={job._id} className="col-md-6 col-xl-4 mb-4">
                    <div style={{
                      background: 'white',
                      borderRadius: '20px',
                      padding: '0',
                      height: '480px',
                      border: '1px solid #e5e7eb',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                      transition: 'all 0.3s ease',
                      cursor: 'pointer',
                      position: 'relative',
                      display: 'flex',
                      flexDirection: 'column',
                      overflow: 'hidden'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-4px)';
                      e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
                      e.currentTarget.style.borderColor = '#2D8BBA';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
                      e.currentTarget.style.borderColor = '#e5e7eb';
                    }}>
                      
                      {/* Header with gradient background */}
                      <div style={{
                        background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                        padding: '24px 24px 40px 24px',
                        position: 'relative',
                        borderRadius: '20px 20px 0 0'
                      }}>
                        {/* Match Score Badge */}
                        <div style={{
                          position: 'absolute',
                          top: '16px',
                          right: '16px',
                          background: 'rgba(255, 255, 255, 0.95)',
                          color: getMatchScoreColor(job.matchingScore || job.matchScore || 0),
                          padding: '8px 12px',
                          borderRadius: '12px',
                          fontSize: '12px',
                          fontWeight: '700',
                          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                          backdropFilter: 'blur(10px)'
                        }}>
                          <span className="d-none d-md-inline">{Math.round(job.matchingScore || job.matchScore || 0)}% Match</span>
                          <span className="d-md-none">{Math.round(job.matchingScore || job.matchScore || 0)}%</span>
                        </div>

                        {/* Job Title */}
                        <h3 style={{ 
                          color: 'white',
                          fontSize: '18px',
                          fontWeight: '700',
                          margin: '0 60px 12px 0',
                          lineHeight: '1.4',
                          height: '50px',
                          overflow: 'hidden',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical'
                        }}>
                          {job.descriptionDuPoste || job.jobOffer?.title || 'Titre non disponible'}
                        </h3>
                        
                        {/* Company */}
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          color: 'rgba(255, 255, 255, 0.9)',
                          fontSize: '14px',
                          fontWeight: '600'
                        }}>
                          <div style={{
                            background: 'rgba(255, 255, 255, 0.2)',
                            borderRadius: '8px',
                            width: '32px',
                            height: '32px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            marginRight: '12px'
                          }}>
                            <FontAwesomeIcon icon={faBuilding} style={{ color: 'white', fontSize: '14px' }} />
                          </div>
                          <span style={{
                            overflow: 'hidden',
                            whiteSpace: 'nowrap',
                            textOverflow: 'ellipsis'
                          }}>
                            {job.agence || job.jobOffer?.company || 'Entreprise non spécifiée'}
                          </span>
                        </div>
                      </div>

                      {/* Content Section */}
                      <div style={{ 
                        padding: '24px',
                        flex: '1',
                        display: 'flex',
                        flexDirection: 'column'
                      }}>
                        {/* Location and Contract Type */}
                        <div className="mb-4">
                          <div className="d-flex align-items-center mb-3">
                            <FontAwesomeIcon icon={faMapMarkerAlt} style={{ 
                              color: '#2D8BBA', 
                              marginRight: '8px',
                              fontSize: '14px'
                            }} />
                            <span style={{
                              fontSize: '14px',
                              color: '#374151',
                              fontWeight: '500',
                              overflow: 'hidden',
                              whiteSpace: 'nowrap',
                              textOverflow: 'ellipsis'
                            }}>
                              {job.ville || job.jobOffer?.location || 'Localisation non spécifiée'}
                              {job.region && ` • ${job.region}`}
                            </span>
                          </div>
                          
                          {(job.contract || job.jobOffer?.contractType) && (
                            <div>
                              <span style={{
                                background: '#f3f4f6',
                                color: '#374151',
                                padding: '6px 12px',
                                borderRadius: '12px',
                                fontSize: '12px',
                                fontWeight: '600',
                                border: '1px solid #e5e7eb'
                              }}>
                                {job.contract || job.jobOffer.contractType}
                              </span>
                            </div>
                          )}
                        </div>
                        
                        {/* Description */}
                        <div style={{
                          flex: '1',
                          marginBottom: '20px'
                        }}>
                          {(job.conditionsEtAvantages || job.jobOffer?.conditionsEtAvantages
) && (
                            <div style={{
                              background: '#f9fafb',
                              borderRadius: '12px',
                              padding: '16px',
                              border: '1px solid #f3f4f6',
                              height: '60px',
                              overflow: 'hidden',
                              position: 'relative'
                            }}>
                              <p style={{
                                fontSize: '13px',
                                lineHeight: '1.5',
                                color: '#6b7280',
                                margin: '0',
                                display: '-webkit-box',
                                WebkitLineClamp: 4,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden'
                              }}>
                                {job.conditionsEtAvantages || job.jobOffer?.conditionsEtAvantages || ''}
                              </p>
                              <div style={{
                                position: 'absolute',
                                bottom: '0',
                                left: '0',
                                right: '0',
                                height: '20px',
                                background: 'linear-gradient(transparent, #f9fafb)',
                                pointerEvents: 'none'
                              }}></div>
                            </div>
                          )}
                        </div>

                        {/* Match Quality Indicator */}
                        <div className="mb-4">
                          <div style={{
                            background: `${getMatchScoreColor(job.matchingScore || job.matchScore || 0)}15`,
                            padding: '8px 12px',
                            borderRadius: '8px',
                            border: `1px solid ${getMatchScoreColor(job.matchingScore || job.matchScore || 0)}30`,
                            textAlign: 'center'
                          }}>
                            <span style={{ 
                              color: getMatchScoreColor(job.matchingScore || job.matchScore || 0),
                              fontWeight: '600',
                              fontSize: '12px'
                            }}>
                              {getMatchScoreLabel(job.matchingScore || job.matchScore || 0)}
                            </span>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="d-flex gap-2 mt-auto">
                          <button
                            onClick={() => router.push(`/offre/${job.uuid}`)}
                            style={{
                              background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                              border: 'none',
                              borderRadius: '10px',
                              color: 'white',
                              padding: '12px 16px',
                              fontSize: '13px',
                              fontWeight: '600',
                              cursor: 'pointer',
                              flex: '1',
                              transition: 'all 0.2s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.target.style.transform = 'translateY(-1px)';
                              e.target.style.boxShadow = '0 4px 12px rgba(45, 139, 186, 0.4)';
                            }}
                            onMouseLeave={(e) => {
                              e.target.style.transform = 'translateY(0)';
                              e.target.style.boxShadow = 'none';
                            }}
                          >
                            <FontAwesomeIcon icon={faEye} className="me-2" />
                            Voir l'offre
                          </button>
                          
                         
                          
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Enhanced Pagination */}
              {pagination.totalPages > 1 && (
                <div className="d-flex justify-content-center mt-5">
                  <nav>
                    <ul className="pagination" style={{ gap: '8px' }}>
                      <li className={`page-item ${!pagination.hasPrev ? 'disabled' : ''}`}>
                        <button 
                          className="page-link"
                          style={{
                            background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                            border: '2px solid #e2e8f0',
                            borderRadius: '12px',
                            padding: '12px 20px',
                            fontWeight: '600',
                            color: '#475569',
                            transition: 'all 0.3s ease'
                          }}
                          onClick={() => handleFilterChange({ page: pagination.currentPage - 1 })}
                          disabled={!pagination.hasPrev}
                          onMouseEnter={(e) => {
                            if (!e.target.disabled) {
                              e.target.style.background = 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)';
                              e.target.style.color = 'white';
                              e.target.style.borderColor = '#2D8BBA';
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (!e.target.disabled) {
                              e.target.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';
                              e.target.style.color = '#475569';
                              e.target.style.borderColor = '#e2e8f0';
                            }
                          }}
                        >
                          Précédent
                        </button>
                      </li>
                      {[...Array(pagination.totalPages)].map((_, index) => (
                        <li key={index + 1} className={`page-item ${pagination.currentPage === index + 1 ? 'active' : ''}`}>
                          <button 
                            className="page-link"
                            style={{
                              background: pagination.currentPage === index + 1 
                                ? 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)' 
                                : 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                              border: '2px solid',
                              borderColor: pagination.currentPage === index + 1 ? '#2D8BBA' : '#e2e8f0',
                              borderRadius: '12px',
                              padding: '12px 16px',
                              fontWeight: '600',
                              color: pagination.currentPage === index + 1 ? 'white' : '#475569',
                              transition: 'all 0.3s ease',
                              minWidth: '48px'
                            }}
                            onClick={() => handleFilterChange({ page: index + 1 })}
                            onMouseEnter={(e) => {
                              if (pagination.currentPage !== index + 1) {
                                e.target.style.background = 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)';
                                e.target.style.color = 'white';
                                e.target.style.borderColor = '#2D8BBA';
                              }
                            }}
                            onMouseLeave={(e) => {
                              if (pagination.currentPage !== index + 1) {
                                e.target.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';
                                e.target.style.color = '#475569';
                                e.target.style.borderColor = '#e2e8f0';
                              }
                            }}
                          >
                            {index + 1}
                          </button>
                        </li>
                      ))}
                      <li className={`page-item ${!pagination.hasNext ? 'disabled' : ''}`}>
                        <button 
                          className="page-link"
                          style={{
                            background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                            border: '2px solid #e2e8f0',
                            borderRadius: '12px',
                            padding: '12px 20px',
                            fontWeight: '600',
                            color: '#475569',
                            transition: 'all 0.3s ease'
                          }}
                          onClick={() => handleFilterChange({ page: pagination.currentPage + 1 })}
                          disabled={!pagination.hasNext}
                          onMouseEnter={(e) => {
                            if (!e.target.disabled) {
                              e.target.style.background = 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)';
                              e.target.style.color = 'white';
                              e.target.style.borderColor = '#2D8BBA';
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (!e.target.disabled) {
                              e.target.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';
                              e.target.style.color = '#475569';
                              e.target.style.borderColor = '#e2e8f0';
                            }
                          }}
                        >
                          Suivant
                        </button>
                      </li>
                    </ul>
                  </nav>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-5">
              <FontAwesomeIcon icon={faSearch} size="3x" className="text-muted mb-3" />
              <h4>Aucun emploi correspondant trouvé</h4>
              <p className="text-muted mb-4">
                Nous n'avons pas trouvé d'offres correspondant à vos critères avec un score minimum de {filters.minScore}%.
                <br />
                Essayez de réduire le score minimum ou de mettre à jour votre profil.
              </p>
              
              <div className="d-flex gap-3 justify-content-center flex-wrap">
                <button
                  onClick={() => handleFilterChange({ minScore: 0, page: 1 })}
                  className="btn btn-outline-primary"
                >
                  <FontAwesomeIcon icon={faSearch} className="me-2" />
                  Voir toutes les offres
                </button>
                <button
                  onClick={() => router.push('/profile/personal-info')}
                  className="btn btn-primary"
                >
                  <FontAwesomeIcon icon={faUser} className="me-2" />
                  Mettre à jour mon profil
                </button>
              </div>
              
              {statistics && statistics.recommendations && (
                <div className="mt-4 p-3" style={{ background: '#f8f9fa', borderRadius: '8px', maxWidth: '500px', margin: '0 auto' }}>
                  <h6 className="mb-2">Recommandations pour améliorer vos correspondances :</h6>
                  <ul className="list-unstyled mb-0 text-start">
                    {statistics.recommendations.addMoreSkills && (
                      <li className="mb-1">• Ajoutez plus de compétences à votre profil</li>
                    )}
                    {statistics.recommendations.expandLocations && (
                      <li className="mb-1">• Élargissez vos préférences de localisation</li>
                    )}
                    {statistics.recommendations.diversifyRoles && (
                      <li className="mb-1">• Diversifiez vos rôles préférés</li>
                    )}
                    {statistics.recommendations.improveProfile && (
                      <li className="mb-1">• Complétez davantage votre profil professionnel</li>
                    )}
                  </ul>
                </div>
              )}
            </div>
          )}
          </div>
        </div>
      </div>
    </Layout>
  );
}
