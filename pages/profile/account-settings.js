import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowLeft,
  faUserShield,
  faBell,
  faGlobe,
  faTrash,
  faCheck,
  faTimes,
  faSpinner,
  faEye,
  faEyeSlash,
  faSave,
  faCog
} from '@fortawesome/free-solid-svg-icons';
import Layout from '@/components/layout/Layout';
import styles from '@/styles/Profile.module.css';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import axios from 'axios';
import { jwtDecode } from 'jwt-decode';

export default function AccountSettings() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('security');
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [notificationSettings, setNotificationSettings] = useState({
    email: true,
    sms: false,
    jobAlerts: true,
    applicationUpdates: true,
    newsletter: true
  });
  const [language, setLanguage] = useState('fr');
  const [timezone, setTimezone] = useState('Europe/Paris');
  const [saving, setSaving] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNotificationChange = (e) => {
    const { name, checked } = e.target;
    setNotificationSettings(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleSavePassword = async (e) => {
    e.preventDefault();

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error('Les nouveaux mots de passe ne correspondent pas.');
      return;
    }

    if (passwordData.newPassword.length < 8) {
      toast.error('Le nouveau mot de passe doit contenir au moins 8 caractères.');
      return;
    }

    setSaving(true);

    try {
      const token = localStorage.getItem("token");
      if (!token) {
        toast.error("Vous devez être connecté");
        return;
      }

      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      await axios.put(
        'http://82.165.144.72:5000/api/users/change-password',
        {
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword
        },
        config
      );

      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      toast.success('Votre mot de passe a été mis à jour avec succès !');
    } catch (error) {
      console.error('Error changing password:', error);
      if (error.response?.status === 400) {
        toast.error('Mot de passe actuel incorrect');
      } else {
        toast.error('Erreur lors de la modification du mot de passe');
      }
    } finally {
      setSaving(false);
    }
  };

  const handleSaveNotifications = (e) => {
    e.preventDefault();
    setSaving(true);

    // Simulate API call
    setTimeout(() => {
      setSaving(false);
      toast.success('Vos préférences de notification ont été mises à jour !');
    }, 1000);
  };

  const handleSavePreferences = (e) => {
    e.preventDefault();
    setSaving(true);

    // Simulate API call
    setTimeout(() => {
      setSaving(false);
      toast.success('Vos préférences ont été mises à jour !');
    }, 1000);
  };

  const handleDeleteAccount = async () => {
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        toast.error("Vous devez être connecté");
        return;
      }

      const decoded = jwtDecode(token);
      const userId = decoded.id;

      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      await axios.delete(`http://82.165.144.72:5000/api/users/${userId}`, config);

      localStorage.removeItem('token');
      toast.success('Compte supprimé avec succès');
      router.push('/');
    } catch (error) {
      console.error('Error deleting account:', error);
      toast.error('Erreur lors de la suppression du compte');
    }
    setShowDeleteModal(false);
  };

  return (
    <Layout headerStyle={2} footerStyle={3}>
      <style jsx>{`
        @media (max-width: 767.98px) {
          .col-md-9 {
            padding-left: 10px !important;
            padding-right: 10px !important;
          }
          .account-settings-container {
            padding: 20px !important;
            margin: 0 !important;
          }
          .password-form-container {
            padding: 20px !important;
            margin: 0 -15px 32px -15px !important;
            border-radius: 12px !important;
            width: calc(100% + 30px) !important;
          }
        }
      `}</style>
      <ToastContainer position="top-right" autoClose={3000} />
      <div className="container" style={{ marginTop: '150px', marginBottom: '120px', minHeight: '70vh' }}>
        <div style={{
          background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
          borderRadius: '24px',
          padding: '3px',
          marginBottom: '30px',
          boxShadow: '0 25px 50px rgba(45, 139, 186, 0.15)'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
            borderRadius: '21px',
            padding: '50px',
            boxShadow: '0 30px 60px rgba(0,0,0,0.08)'
          }}>
            <button
              onClick={() => router.back()}
              className="btn-link p-2 mb-4 d-flex align-items-center"
              style={{
                background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                border: 'none',
                borderRadius: '12px',
                color: 'white',
                padding: '14px 28px',
                textDecoration: 'none',
                fontSize: '16px',
                fontWeight: '600',
                transition: 'all 0.3s ease',
                boxShadow: '0 8px 25px rgba(45, 139, 186, 0.3)'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 12px 35px rgba(45, 139, 186, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 8px 25px rgba(45, 139, 186, 0.3)';
              }}
            >
              <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
              Retour au profil
            </button>

            <h1 style={{
              fontSize: '36px',
              fontWeight: '800',
              background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              marginBottom: '12px',
              letterSpacing: '-0.5px'
            }}>
              <FontAwesomeIcon icon={faCog} className="me-3" style={{ color: '#2D8BBA' }} />
              Paramètres du compte
            </h1>
            <p style={{ 
              color: '#64748b', 
              fontSize: '18px', 
              marginBottom: '40px',
              fontWeight: '500'
            }}>
              Gérez les paramètres de sécurité de votre compte
            </p>

            <div className="row">
              <div className="col-md-3">
                <div style={{
                  background: 'white',
                  borderRadius: '20px',
                  padding: '32px',
                  boxShadow: '0 15px 35px rgba(0, 0, 0, 0.08)',
                  border: '1px solid #e5e7eb',
                  marginBottom: '20px'
                }}>
                  <div>
                    <h5 style={{
                      fontSize: '20px',
                      fontWeight: '700',
                      color: '#374151',
                      marginBottom: '24px'
                    }}>Paramètres</h5>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                      <button 
                        style={{
                          background: activeTab === 'security' 
                            ? 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)'
                            : 'transparent',
                          border: 'none',
                          borderRadius: '12px',
                          color: activeTab === 'security' ? 'white' : '#374151',
                          padding: '14px 18px',
                          textDecoration: 'none',
                          fontSize: '16px',
                          fontWeight: '600',
                          transition: 'all 0.3s ease',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          width: '100%',
                          justifyContent: 'flex-start'
                        }}
                        onMouseEnter={(e) => {
                          if (activeTab !== 'security') {
                            e.target.style.background = '#f8fafc';
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (activeTab !== 'security') {
                            e.target.style.background = 'transparent';
                          }
                        }}
                        onClick={() => setActiveTab('security')}
                      >
                        <FontAwesomeIcon icon={faUserShield} className="me-3" />
                        Sécurité
                      </button>
                    </div>
                  </div>
                </div>

                <div style={{
                  background: 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)',
                  borderRadius: '20px',
                  padding: '32px',
                  border: '2px solid #ef4444',
                  boxShadow: '0 15px 35px rgba(239, 68, 68, 0.1)'
                }}>
                  <div>
                    <h5 style={{
                      fontSize: '18px',
                      fontWeight: '700',
                      color: '#dc2626',
                      marginBottom: '12px'
                    }}>
                      <FontAwesomeIcon icon={faTrash} className="me-2" />
                      Zone de danger
                    </h5>
                    <p style={{
                      fontSize: '14px',
                      color: '#7f1d1d',
                      marginBottom: '20px',
                      lineHeight: '1.5'
                    }}>
                      La suppression de votre compte est irréversible. Toutes vos données seront définitivement supprimées.
                    </p>
                    <button 
                      style={{
                        background: '#ef4444',
                        border: '2px solid #dc2626',
                        borderRadius: '12px',
                        color: 'white',
                        padding: '12px 20px',
                        fontSize: '14px',
                        fontWeight: '600',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        width: '100%'
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.background = '#dc2626';
                        e.target.style.transform = 'translateY(-1px)';
                        e.target.style.boxShadow = '0 4px 12px rgba(220, 38, 38, 0.3)';
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.background = '#ef4444';
                        e.target.style.transform = 'translateY(0)';
                        e.target.style.boxShadow = 'none';
                      }}
                      onClick={() => setShowDeleteModal(true)}
                    >
                      Supprimer mon compte
                    </button>
                  </div>
                </div>
              </div>
          
              <div className="col-md-9">
                <div
                  className="account-settings-container"
                  style={{
                    background: 'white',
                    borderRadius: '20px',
                    padding: '40px',
                    boxShadow: '0 15px 35px rgba(0, 0, 0, 0.08)',
                    border: '1px solid #e5e7eb'
                  }}>
                  <div>
                    {activeTab === 'security' && (
                      <>
                        <h5 style={{
                          fontSize: '24px',
                          fontWeight: '700',
                          color: '#2D8BBA',
                          marginBottom: '24px',
                          display: 'flex',
                          alignItems: 'center'
                        }}>
                          <FontAwesomeIcon icon={faUserShield} className="me-3" />
                          Sécurité du compte
                        </h5>
                    
                        <form onSubmit={handleSavePassword}>
                          <div
                            className="password-form-container"
                            style={{
                              background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                              borderRadius: '16px',
                              padding: '32px',
                              border: '2px solid rgba(45, 139, 186, 0.1)',
                              marginBottom: '32px'
                            }}>
                            <h6 style={{
                              fontSize: '18px',
                              fontWeight: '700',
                              color: '#374151',
                              marginBottom: '8px'
                            }}>Changer de mot de passe</h6>
                            <p style={{
                              color: '#6b7280',
                              fontSize: '14px',
                              marginBottom: '24px'
                            }}>
                              Assurez-vous que votre nouveau mot de passe est fort et unique.
                            </p>
                            
                            <div className="mb-4">
                              <label htmlFor="currentPassword" style={{
                                display: 'block',
                                marginBottom: '8px',
                                fontWeight: '600',
                                color: '#374151',
                                fontSize: '16px'
                              }}>Mot de passe actuel</label>
                              <div style={{ position: 'relative' }}>
                                <input
                                  type={showCurrentPassword ? "text" : "password"}
                                  style={{
                                    width: '100%',
                                    padding: '14px 50px 14px 18px',
                                    border: '2px solid #e2e8f0',
                                    borderRadius: '12px',
                                    fontSize: '16px',
                                    transition: 'all 0.3s ease',
                                    outline: 'none'
                                  }}
                                  onFocus={(e) => {
                                    e.target.style.borderColor = '#2D8BBA';
                                    e.target.style.boxShadow = '0 0 0 4px rgba(45, 139, 186, 0.1)';
                                  }}
                                  onBlur={(e) => {
                                    e.target.style.borderColor = '#e2e8f0';
                                    e.target.style.boxShadow = 'none';
                                  }}
                                  id="currentPassword"
                                  name="currentPassword"
                                  value={passwordData.currentPassword}
                                  onChange={handlePasswordChange}
                                  required
                                />
                                <button 
                                  type="button"
                                  style={{
                                    position: 'absolute',
                                    right: '12px',
                                    top: '50%',
                                    transform: 'translateY(-50%)',
                                    background: 'none',
                                    border: 'none',
                                    color: '#6b7280',
                                    cursor: 'pointer',
                                    padding: '8px',
                                    fontSize: '16px'
                                  }}
                                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                >
                                  <FontAwesomeIcon icon={showCurrentPassword ? faEyeSlash : faEye} />
                                </button>
                              </div>
                            </div>
                            
                            <div className="mb-4">
                              <label htmlFor="newPassword" style={{
                                display: 'block',
                                marginBottom: '8px',
                                fontWeight: '600',
                                color: '#374151',
                                fontSize: '16px'
                              }}>Nouveau mot de passe</label>
                              <div style={{ position: 'relative' }}>
                                <input
                                  type={showNewPassword ? "text" : "password"}
                                  style={{
                                    width: '100%',
                                    padding: '14px 50px 14px 18px',
                                    border: '2px solid #e2e8f0',
                                    borderRadius: '12px',
                                    fontSize: '16px',
                                    transition: 'all 0.3s ease',
                                    outline: 'none'
                                  }}
                                  onFocus={(e) => {
                                    e.target.style.borderColor = '#2D8BBA';
                                    e.target.style.boxShadow = '0 0 0 4px rgba(45, 139, 186, 0.1)';
                                  }}
                                  onBlur={(e) => {
                                    e.target.style.borderColor = '#e2e8f0';
                                    e.target.style.boxShadow = 'none';
                                  }}
                                  id="newPassword"
                                  name="newPassword"
                                  value={passwordData.newPassword}
                                  onChange={handlePasswordChange}
                                  required
                                  minLength="8"
                                />
                                <button 
                                  type="button"
                                  style={{
                                    position: 'absolute',
                                    right: '12px',
                                    top: '50%',
                                    transform: 'translateY(-50%)',
                                    background: 'none',
                                    border: 'none',
                                    color: '#6b7280',
                                    cursor: 'pointer',
                                    padding: '8px',
                                    fontSize: '16px'
                                  }}
                                  onClick={() => setShowNewPassword(!showNewPassword)}
                                >
                                  <FontAwesomeIcon icon={showNewPassword ? faEyeSlash : faEye} />
                                </button>
                              </div>
                              <div style={{
                                fontSize: '14px',
                                color: '#6b7280',
                                marginTop: '6px'
                              }}>
                                Le mot de passe doit contenir au moins 8 caractères.
                              </div>
                            </div>
                            
                            <div className="mb-4">
                              <label htmlFor="confirmPassword" style={{
                                display: 'block',
                                marginBottom: '8px',
                                fontWeight: '600',
                                color: '#374151',
                                fontSize: '16px'
                              }}>Confirmer le nouveau mot de passe</label>
                              <div style={{ position: 'relative' }}>
                                <input
                                  type={showConfirmPassword ? "text" : "password"}
                                  style={{
                                    width: '100%',
                                    padding: '14px 50px 14px 18px',
                                    border: '2px solid #e2e8f0',
                                    borderRadius: '12px',
                                    fontSize: '16px',
                                    transition: 'all 0.3s ease',
                                    outline: 'none'
                                  }}
                                  onFocus={(e) => {
                                    e.target.style.borderColor = '#2D8BBA';
                                    e.target.style.boxShadow = '0 0 0 4px rgba(45, 139, 186, 0.1)';
                                  }}
                                  onBlur={(e) => {
                                    e.target.style.borderColor = '#e2e8f0';
                                    e.target.style.boxShadow = 'none';
                                  }}
                                  id="confirmPassword"
                                  name="confirmPassword"
                                  value={passwordData.confirmPassword}
                                  onChange={handlePasswordChange}
                                  required
                                  minLength="8"
                                />
                                <button 
                                  type="button"
                                  style={{
                                    position: 'absolute',
                                    right: '12px',
                                    top: '50%',
                                    transform: 'translateY(-50%)',
                                    background: 'none',
                                    border: 'none',
                                    color: '#6b7280',
                                    cursor: 'pointer',
                                    padding: '8px',
                                    fontSize: '16px'
                                  }}
                                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                >
                                  <FontAwesomeIcon icon={showConfirmPassword ? faEyeSlash : faEye} />
                                </button>
                              </div>
                            </div>
                            
                            <div className="d-flex justify-content-center">
                              <button
                                type="submit"
                                disabled={saving || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}
                                style={{
                                  background: saving || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword
                                    ? '#6c757d'
                                    : 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                                  border: 'none',
                                  borderRadius: '12px',
                                  color: 'white',
                                  padding: '10px 20px',
                                  fontSize: '14px',
                                  fontWeight: '600',
                                  cursor: saving || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword ? 'not-allowed' : 'pointer',
                                  transition: 'all 0.3s ease',
                                  boxShadow: !saving && passwordData.currentPassword && passwordData.newPassword && passwordData.confirmPassword
                                    ? '0 8px 20px rgba(45, 139, 186, 0.3)' : 'none'
                                }}
                                onMouseEnter={(e) => {
                                  if (!saving && passwordData.currentPassword && passwordData.newPassword && passwordData.confirmPassword) {
                                    e.target.style.transform = 'translateY(-2px)';
                                    e.target.style.boxShadow = '0 12px 25px rgba(45, 139, 186, 0.4)';
                                  }
                                }}
                                onMouseLeave={(e) => {
                                  e.target.style.transform = 'translateY(0)';
                                  e.target.style.boxShadow = !saving && passwordData.currentPassword && passwordData.newPassword && passwordData.confirmPassword 
                                    ? '0 8px 20px rgba(45, 139, 186, 0.3)' : 'none';
                                }}
                              >
                                {saving ? (
                                  <>
                                    <FontAwesomeIcon icon={faSpinner} className="me-md-2" spin />
                                    <span className="d-none d-md-inline">Enregistrement...</span>
                                    <span className="d-md-none">Sauvegarde...</span>
                                  </>
                                ) : (
                                  <>
                                    <FontAwesomeIcon icon={faSave} className="me-md-2" />
                                    <span className="d-none d-md-inline">Enregistrer les modifications</span>
                                    <span className="d-md-none">Enregistrer</span>
                                  </>
                                )}
                              </button>
                            </div>
                          </div>
                        </form>
                    
                   
                  </>
                )}
                
               
            
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Enhanced Delete Account Modal */}
      {showDeleteModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 9999,
          backdropFilter: 'blur(4px)'
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '20px',
            padding: '40px',
            maxWidth: '650px',
            width: '90%',
            maxHeight: '80vh',
            overflow: 'auto',
            boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
            border: '1px solid #e5e7eb'
          }}>
            <h3 style={{ 
              color: '#dc2626', 
              marginBottom: '24px',
              fontSize: '28px',
              fontWeight: '700',
              display: 'flex',
              alignItems: 'center'
            }}>
              <FontAwesomeIcon icon={faTrash} className="me-3" />
              Suppression de compte 
            </h3>

            <div style={{ marginBottom: '32px', lineHeight: '1.6' }}>
              <h4 style={{ 
                color: '#374151', 
                marginBottom: '16px',
                fontSize: '20px',
                fontWeight: '600'
              }}>Vos droits selon le RGPD :</h4>
              <ul style={{ 
                paddingLeft: '24px', 
                marginBottom: '24px',
                color: '#6b7280'
              }}>
                <li style={{ marginBottom: '12px', fontSize: '16px' }}>
                  <strong style={{ color: '#374151' }}>Délai de traitement :</strong> Votre demande sera traitée dans un délai maximum de 30 jours.
                </li>
                <li style={{ marginBottom: '12px', fontSize: '16px' }}>
                  <strong style={{ color: '#374151' }}>Données conservées :</strong> Certaines données peuvent être conservées pour des obligations légales (comptabilité, etc.).
                </li>
              </ul>

              <div style={{
                background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                border: '2px solid rgba(45, 139, 186, 0.2)',
                borderRadius: '16px',
                padding: '24px',
                marginBottom: '24px'
              }}>
                <h5 style={{ 
                  color: '#2D8BBA', 
                  marginBottom: '12px',
                  fontSize: '18px',
                  fontWeight: '600'
                }}>Procédure de suppression :</h5>
                <p style={{ 
                  margin: '0 0 16px 0', 
                  fontSize: '16px',
                  color: '#374151'
                }}>
                  Pour supprimer définitivement votre compte et toutes vos données personnelles,
                  veuillez nous envoyer un email à l'adresse suivante :
                </p>
                <div style={{
                  background: 'white',
                  padding: '16px',
                  borderRadius: '12px',
                  textAlign: 'center',
                  border: '2px solid #2D8BBA'
                }}>
                  <p style={{
                    margin: 0,
                    fontSize: '18px',
                    fontWeight: '700',
                    color: '#2D8BBA'
                  }}>
                    <EMAIL>
                  </p>
                </div>
              </div>
            </div>

            <div style={{ 
              display: 'flex', 
              gap: '16px', 
              justifyContent: 'flex-end',
              flexWrap: 'wrap'
            }}>
              <button
                onClick={() => setShowDeleteModal(false)}
                style={{
                  padding: '12px 24px',
                  backgroundColor: '#6c757d',
                  color: 'white',
                  border: 'none',
                  borderRadius: '12px',
                  cursor: 'pointer',
                  fontSize: '16px',
                  fontWeight: '600',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = '#5a6268';
                  e.target.style.transform = 'translateY(-1px)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = '#6c757d';
                  e.target.style.transform = 'translateY(0)';
                }}
              >
                Fermer
              </button>
              <a
                href="mailto:<EMAIL>?subject=Demande de suppression de compte&body=Bonjour,%0D%0A%0D%0AJe souhaite supprimer définitivement mon compte et toutes mes données personnelles conformément au RGPD.%0D%0A%0D%0AEmail du compte : [VOTRE_EMAIL]%0D%0A%0D%0AMerci de confirmer la suppression.%0D%0A%0D%0ACordialement"
                style={{
                  padding: '12px 24px',
                  background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
                  color: 'white',
                  textDecoration: 'none',
                  borderRadius: '12px',
                  fontSize: '16px',
                  fontWeight: '600',
                  display: 'inline-block',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 4px 12px rgba(220, 38, 38, 0.3)'
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = 'translateY(-2px)';
                  e.target.style.boxShadow = '0 8px 20px rgba(220, 38, 38, 0.4)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 4px 12px rgba(220, 38, 38, 0.3)';
                }}
              >
                Envoyer l'email de suppression
              </a>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
}
