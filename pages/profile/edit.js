"use client";
import { useEffect, useState } from "react";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import axios from "axios";
import Layout from "@/components/layout/Layout";
import { jwtDecode } from "jwt-decode";
import styles from "@/styles/Profile.module.css";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { 
  faUser, 
  faEnvelope, 
  faPhone, 
  faEdit,
  faArrowLeft
} from "@fortawesome/free-solid-svg-icons";
import Link from "next/link";
import { useRouter } from "next/router";

const EditProfile = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const router = useRouter();

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const token = localStorage.getItem("token");
        if (!token) {
          setError("No token found");
          router.push("/signin");
          setLoading(false);
          return;
        }

        const decoded = jwtDecode(token);
        const userId = decoded.id;

        const config = {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };

        const response = await axios.get(
          `http://82.165.144.72:5000/api/users/${userId}`,
          config
        );
        setUser(response.data);
      } catch (err) {
        setError("Failed to fetch user data");
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [router]);

  if (loading) return (
    <Layout headerStyle={2} footerStyle={3}>
      <div className={styles.loader}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Chargement...</span>
        </div>
      </div>
    </Layout>
  );
  
  if (error) return (
    <Layout headerStyle={2} footerStyle={3}>
      <div className={styles.error}>
        <div className="alert alert-danger text-center mb-0" role="alert">
          {error}
        </div>
      </div>
    </Layout>
  );

  return (
    <Layout headerStyle={2} footerStyle={3}>
      <div className={styles.profileContainer} style={{marginTop: "10%", marginBottom: "10%"}}>
        <ToastContainer position="top-right" autoClose={3000} />
        <div className={styles.profileHeader} style={{ position: 'relative', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Link href="/profile" className={styles.backLink} style={{
            position: 'absolute',
            left: '10px',
            top: '50%',
            transform: 'translateY(-50%)',
            color: '#2D8BBA',
            textDecoration: 'none',
            display: 'flex',
            alignItems: 'center',
            fontSize: '16px',
            fontWeight: '500'
          }}>
            <FontAwesomeIcon icon={faArrowLeft} className={window.innerWidth <= 768 ? "" : "me-2"} />
            {window.innerWidth > 768 && "Retour"}
          </Link>
          <h1 className={styles.profileTitle} style={{ margin: '0', textAlign: 'center' }}>
            {window.innerWidth <= 768 ? "Modifier mes informations" : "Modifier mes informations personnelles"}
          </h1>
        </div>

        <div className={styles.formContainer}>
          <div className={styles.cardWithForm}>
            <form onSubmit={(e) => {
              e.preventDefault();
              if (user) {
                const token = localStorage.getItem("token");
                const decoded = jwtDecode(token);
                const userId = decoded.id;
                const config = {
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                };
                axios.put(
                  `http://82.165.144.72:5000/api/users/${userId}`,
                  user,
                  config
                )
                .then(() => {
                  toast.success("Profil mis à jour avec succès!");
                  router.push("/profile");
                })
                .catch(error => {
                  console.error("Error updating user:", error);
                  toast.error("Échec de la mise à jour du profil");
                });
              }
            }}>
              <div className={styles.formGroup}>
                <label htmlFor="firstName" className={styles.formLabel}>
                  <FontAwesomeIcon icon={faUser} className="me-2" />
                  Prénom:
                </label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  className={styles.formInput}
                  value={user?.firstName || ""}
                  onChange={(e) => setUser({...user, firstName: e.target.value})}
                  placeholder="Entrez votre prénom"
                />
              </div>
              <div className={styles.formGroup}>
                <label htmlFor="lastName" className={styles.formLabel}>
                  <FontAwesomeIcon icon={faUser} className="me-2" />
                  Nom de famille:
                </label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  className={styles.formInput}
                  value={user?.lastName || ""}
                  onChange={(e) => setUser({...user, lastName: e.target.value})}
                  placeholder="Entrez votre nom de famille"
                />
              </div>
              <div className={styles.formGroup}>
                <label htmlFor="telephone" className={styles.formLabel}>
                  <FontAwesomeIcon icon={faPhone} className="me-2" />
                  Téléphone:
                </label>
                <input
                  type="text"
                  id="telephone"
                  name="telephone"
                  className={styles.formInput}
                  value={user?.telephone || ""}
                  onChange={(e) => setUser({...user, telephone: e.target.value})}
                  placeholder="Entrez votre numéro de téléphone"
                />
              </div>
              <div className={styles.formGroup}>
                <label htmlFor="email" className={styles.formLabel}>
                  <FontAwesomeIcon icon={faEnvelope} className="me-2" />
                  E-mail:
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  className={styles.formInput}
                  value={user?.email || ""}
                  onChange={(e) => setUser({...user, email: e.target.value})}
                  placeholder="Entrez votre adresse e-mail"
                />
              </div>
              <button type="submit" className={styles.submitButton}>
                Mettre à jour le profil
              </button>
            </form>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default EditProfile; 