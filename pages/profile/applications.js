import { useEffect, useState } from "react";
import { useRouter } from 'next/router';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faArrowLeft,
  faList,
  faCheckCircle,
  faClock,
  faTimesCircle,
  faMapMarkerAlt,
  faBuilding,
  faCalendarAlt,
  faEye
} from "@fortawesome/free-solid-svg-icons";
import Layout from "@/components/layout/Layout";
import styles from "@/styles/Profile.module.css";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import axios from 'axios';

export default function Applications() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [applications, setApplications] = useState([]);

  // Fetch Applications
  const fetchApplications = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        toast.error("Vous devez être connecté pour accéder à cette page");
        router.push('/signin');
        return;
      }

      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      const response = await axios.get("http://82.165.144.72:5000/api/applications/my-applications?page=1&limit=50", config);
      setApplications(response.data.applications || []);
    } catch (err) {
      console.error("Failed to fetch applications:", err);
      if (err.response?.status === 401) {
        toast.error("Session expirée, veuillez vous reconnecter");
        router.push('/signin');
      } else {
        toast.error("Erreur lors du chargement des candidatures");
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApplications();
  }, []);

  const getStatusBadge = (status) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return <span className="badge bg-warning"><FontAwesomeIcon icon={faClock} className="me-1" />En attente</span>;
      case 'accepted':
        return <span className="badge bg-success"><FontAwesomeIcon icon={faCheckCircle} className="me-1" />Acceptée</span>;
      case 'rejected':
        return <span className="badge bg-danger"><FontAwesomeIcon icon={faTimesCircle} className="me-1" />Refusée</span>;
      default:
        return <span className="badge bg-secondary"><FontAwesomeIcon icon={faClock} className="me-1" />En cours</span>;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Date non disponible';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Layout headerStyle={2} footerStyle={3}>
      <ToastContainer position="top-right" autoClose={3000} />
      <div className="container" style={{ marginTop: '150px', marginBottom: '120px', minHeight: '70vh' }}>
        <div style={{
          background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
          borderRadius: '20px',
          padding: '2px',
          marginBottom: '30px'
        }}>
          <div style={{
            background: '#fff',
            borderRadius: '18px',
            padding: '40px',
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
          }}>
            <button
              onClick={() => router.back()}
             className=" btn-link p-2 mb-4 d-flex align-items-center"
                  style={{
                    
                     background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                    border: 'none',
                    borderRadius: '8px',
                    color: 'white',
                     padding: '12px 24px',
                    textDecoration: 'none',
                    fontSize: '16px',
                    fontWeight: '500'
                  }}
                >
              <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
              Retour au profil
            </button>

            <h1 style={{
              fontSize: '32px',
              fontWeight: '700',
              background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              marginBottom: '8px'
            }}>
              <FontAwesomeIcon icon={faList} className="me-3" style={{ color: '#2D8BBA' }} />
              Mes Candidatures
            </h1>
            <p style={{ color: '#6c757d', fontSize: '16px', marginBottom: '30px' }}>
              Suivez l'état de vos candidatures et gérez vos postulations
            </p>

          {loading ? (
            <div className="text-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Chargement...</span>
              </div>
            </div>
            ) : applications.length > 0 ? (
              <div style={{ display: 'grid', gap: '20px' }}>
                {applications.map((app) => (
                  <div key={app._id} style={{
                    background: '#fff',
                    borderRadius: '16px',
                    padding: '24px',
                    boxShadow: '0 8px 25px rgba(0,0,0,0.08)',
                    border: '1px solid #e9ecef',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 12px 30px rgba(45, 139, 186, 0.15)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.08)';
                  }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
                    <div style={{ flex: 1 }}>
                      <h4 style={{ margin: '0 0 8px 0', color: '#2D8BBA' }}>
                        {app.postId?.descriptionDuPoste || 'Poste non spécifié'}
                      </h4>
                      <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>
                        <span>{app.postId?.agence || 'Agence'} • {app.postId?.ville || 'Ville'}</span>
                        <span> • {app.postId?.contract || 'Type de contrat'}</span>
                      </div>
                      <div style={{ fontSize: '14px', color: '#888' }}>
                        <span>Candidature envoyée le {new Date(app.createdAt).toLocaleDateString('fr-FR')}</span>
                        <span> • Type: {app.cvType === 'upload' ? 'CV téléchargé' : app.cvType === 'form' ? 'Données formulaire' : 'CV bibliothèque'}</span>
                        {app.source && <span> • Source: {app.source === 'direct' ? 'Candidature directe' : app.source === 'library' ? 'Bibliothèque CV' : app.source}</span>}
                      </div>
                      {app.cvFile && (
                        <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
                          Fichier: {app.cvFile.originalName}
                        </div>
                      )}
                      {app.cvLibraryId && (
                        <div style={{ fontSize: '12px', color: '#2D8BBA', marginTop: '4px' }}>
                          📁 CV utilisé depuis la bibliothèque
                        </div>
                      )}
                    </div>
                    <div style={{
                      padding: '4px 12px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: 'bold',
                      backgroundColor: app.status === 'En attente' ? '#fff3cd' :
                                     app.status === 'Accepté' ? '#d1edff' :
                                     app.status === 'Refusé' ? '#f8d7da' : '#e2e3e5',
                      color: app.status === 'En attente' ? '#856404' :
                             app.status === 'Accepté' ? '#0c5460' :
                             app.status === 'Refusé' ? '#721c24' : '#383d41'
                    }}>
                      {app.status || 'En attente'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-5">
              <FontAwesomeIcon icon={faList} size="3x" className="text-muted mb-3" />
              <h4>Aucune candidature trouvée</h4>
              <p className="text-muted">
                Vous n'avez pas encore postulé à des offres d'emploi.
                <br />
                Explorez nos offres et postulez dès maintenant !
              </p>
              <button
                onClick={() => router.push('/jobs')}
                style={{
                  background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  padding: '12px 24px',
                  fontSize: '16px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = 'translateY(-2px)';
                  e.target.style.boxShadow = '0 8px 20px rgba(45, 139, 186, 0.3)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = 'none';
                }}
              >
                Voir les offres d'emploi
              </button>
            </div>
          )}
          </div>
        </div>
      </div>
    </Layout>
  );
}
