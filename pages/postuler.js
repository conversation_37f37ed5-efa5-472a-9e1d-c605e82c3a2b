import React, { useEffect, useState, useRef } from 'react';
import axios from 'axios';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { jwtDecode } from "jwt-decode";
import { useRouter } from 'next/router';
import Layout from "@/components/layout/Layout";

// Step Components defined outside the main component
const Step1 = ({ handleInputChange, handleNextStep, errors = {}, user, handleInputChangeText }) => {
  return (
    <div style={{ padding: '10px 0' }}>
      <h3 style={{ 
        color: '#2D8BBA', 
        fontSize: '24px', 
        fontWeight: '600', 
        marginBottom: '25px',
        textAlign: 'center',
        position: 'relative'
      }}>
        Informations personnelles
        <div style={{
          content: '""',
          position: 'absolute',
          bottom: '-8px',
          left: '50%',
          transform: 'translateX(-50%)',
          width: '60px',
          height: '3px',
          background: 'linear-gradient(90deg, #2D8BBA, #1e6b8a)',
          borderRadius: '2px'
        }}></div>
      </h3>

      {/* Civilité and Portable on the same line */}
      <div className="form-group-container" style={{ 
        display: 'flex', 
        gap: '20px',
        marginBottom: '20px',
        flexWrap: 'wrap'
      }}>
        <div className="form-grp" style={{ flex: 1, minWidth: '200px' }}>
          <label htmlFor="titleSelect" style={{ 
            color: '#333', 
            fontWeight: '600', 
            marginBottom: '8px',
            display: 'block'
          }}>
            Civilité *
          </label>
          <select
            id="titleSelect"
            className="form-select"
            name="title"
            onChange={(event) => {
              handleInputChangeText(event);
              handleInputChange(event);
            }}
            value={user?.title || ''}
            style={{
              width: '100%',
              padding: '12px 15px',
              border: '2px solid #e0e0e0',
              borderRadius: '10px',
              fontSize: '16px',
              transition: 'all 0.3s ease',
              background: '#fff'
            }}
            onFocus={(e) => e.target.style.borderColor = '#2D8BBA'}
            onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}
          >
            <option value="">Sélectionnez...</option>
            <option value="Mme">Mme</option>
            <option value="M.">M.</option>
          </select>
        </div>
        
        <div className="form-grp" style={{ flex: 1, minWidth: '200px' }}>
          <label htmlFor="phone" style={{ 
            color: '#333', 
            fontWeight: '600', 
            marginBottom: '8px',
            display: 'block'
          }}>
            Portable *
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            className="form-select"
            placeholder="Votre numéro de portable"
            value={user?.telephone || ''}
            onChange={handleInputChange}
            required
            style={{
              width: '100%',
              padding: '12px 15px',
              border: '2px solid #e0e0e0',
              borderRadius: '10px',
              fontSize: '16px',
              transition: 'all 0.3s ease'
            }}
            onFocus={(e) => e.target.style.borderColor = '#2D8BBA'}
            onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}
          />
          {errors.phone && <span style={{ color: '#e74c3c', fontSize: '14px', marginTop: '5px', display: 'block' }}>{errors.phone}</span>}
        </div>
      </div>

      {/* Nom and Prénom on the same line */}
      <div className="form-group-container" style={{ 
        display: 'flex', 
        gap: '20px',
        marginBottom: '20px',
        flexWrap: 'wrap'
      }}>
        <div className="form-grp" style={{ flex: 1, minWidth: '200px' }}>
          <label htmlFor="lastName" style={{ 
            color: '#333', 
            fontWeight: '600', 
            marginBottom: '8px',
            display: 'block'
          }}>
            Nom *
          </label>
          <input
            type="text"
            id="lastName"
            className="form-select"
            name="lastName"
            placeholder="Votre nom de famille"
            value={user?.lastName || ''}
            onChange={handleInputChangeText}
            required
            style={{
              width: '100%',
              padding: '12px 15px',
              border: '2px solid #e0e0e0',
              borderRadius: '10px',
              fontSize: '16px',
              transition: 'all 0.3s ease'
            }}
            onFocus={(e) => e.target.style.borderColor = '#2D8BBA'}
            onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}
          />
          {errors.lastName && <span style={{ color: '#e74c3c', fontSize: '14px', marginTop: '5px', display: 'block' }}>{errors.lastName}</span>}
        </div>
        
        <div className="form-grp" style={{ flex: 1, minWidth: '200px' }}>
          <label htmlFor="firstName" style={{ 
            color: '#333', 
            fontWeight: '600', 
            marginBottom: '8px',
            display: 'block'
          }}>
            Prénom *
          </label>
          <input
            type="text"
            id="firstName"
            className="form-select"
            name="firstName"
            placeholder="Votre prénom"
            value={user?.firstName || ''}
            onChange={handleInputChange}
            required
            style={{
              width: '100%',
              padding: '12px 15px',
              border: '2px solid #e0e0e0',
              borderRadius: '10px',
              fontSize: '16px',
              transition: 'all 0.3s ease'
            }}
            onFocus={(e) => e.target.style.borderColor = '#2D8BBA'}
            onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}
          />
          {errors.firstName && <span style={{ color: '#e74c3c', fontSize: '14px', marginTop: '5px', display: 'block' }}>{errors.firstName}</span>}
        </div>
      </div>

      {/* Email */}
      <div className="form-grp" style={{ marginBottom: '20px' }}>
        <label htmlFor="email" style={{ 
          color: '#333', 
          fontWeight: '600', 
          marginBottom: '8px',
          display: 'block'
        }}>
          Email *
        </label>
        <input
          type="email"
          id="email"
          name="email"
          className="form-select"
          placeholder="<EMAIL>"
          value={user?.email || ''}
          onChange={handleInputChange}
          required
          style={{
            width: '100%',
            padding: '12px 15px',
            border: '2px solid #e0e0e0',
            borderRadius: '10px',
            fontSize: '16px',
            transition: 'all 0.3s ease'
          }}
          onFocus={(e) => e.target.style.borderColor = '#2D8BBA'}
          onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}
        />
        {errors.email && <span style={{ color: '#e74c3c', fontSize: '14px', marginTop: '5px', display: 'block' }}>{errors.email}</span>}
      </div>

      {/* Comments */}
      <div className="form-grp" style={{ marginBottom: '30px' }}>
        <label htmlFor="comments" style={{ 
          color: '#333', 
          fontWeight: '600', 
          marginBottom: '8px',
          display: 'block'
        }}>
          Commentaires (optionnel)
        </label>
        <textarea
          id="comments"
          name="comments"
          className="form-select"
          placeholder="Informations supplémentaires..."
          onChange={handleInputChange}
          rows={4}
          style={{
            width: '100%',
            padding: '12px 15px',
            border: '2px solid #e0e0e0',
            borderRadius: '10px',
            fontSize: '16px',
            transition: 'all 0.3s ease',
            resize: 'vertical',
            minHeight: '100px'
          }}
          onFocus={(e) => e.target.style.borderColor = '#2D8BBA'}
          onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}
        />
      </div>

      {/* Next Button */}
      <div style={{ textAlign: 'center' }}>
        <button 
          type="button" 
          onClick={handleNextStep}
          style={{
            background: 'linear-gradient(135deg, #2D8BBA 0%, #1e6b8a 100%)',
            color: 'white',
            border: 'none',
            padding: '15px 40px',
            borderRadius: '25px',
            fontSize: '16px',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            boxShadow: '0 4px 15px rgba(45, 139, 186, 0.3)',
            minWidth: '200px'
          }}
          onMouseOver={(e) => {
            e.target.style.transform = 'translateY(-2px)';
            e.target.style.boxShadow = '0 6px 20px rgba(45, 139, 186, 0.4)';
          }}
          onMouseOut={(e) => {
            e.target.style.transform = 'translateY(0)';
            e.target.style.boxShadow = '0 4px 15px rgba(45, 139, 186, 0.3)';
          }}
        >
          Suivant →
        </button>
      </div>
    </div>
  );
};

const Step2 = ({ education, handleEducationChange, addEducation, goToStep }) => {
  const [windowWidth, setWindowWidth] = React.useState(typeof window !== 'undefined' ? window.innerWidth : 1024);

  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleResize = () => setWindowWidth(window.innerWidth);
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  return (
    <div>
      <h3>Étape 2: Éducation</h3>
      {education.map((edu, index) => (
        <div key={index} className="form-grp">
          <label htmlFor={`degree-${index}`}>Degré</label>
          <input
            type="text"
            id={`degree-${index}`}
            name="degree"
            value={edu.degree}
            placeholder="Degré"
            onChange={(e) => handleEducationChange(index, e)}
          />
          <label htmlFor={`institution-${index}`}>Institution</label>
          <input
            type="text"
            id={`institution-${index}`}
            name="institution"
            value={edu.institution}
            placeholder="Institution"
            onChange={(e) => handleEducationChange(index, e)}
          />
          <label htmlFor={`year-${index}`}>Année</label>
          <input
            type="text"
            id={`year-${index}`}
            name="year"
            value={edu.year}
            placeholder="Année"
            onChange={(e) => handleEducationChange(index, e)}
          />
        </div>
      ))}
      <div style={{
        display: 'flex',
        flexDirection: windowWidth <= 768 ? 'column' : 'row',
        gap: '10px',
        alignItems: windowWidth <= 768 ? 'center' : 'flex-start',
        justifyContent: windowWidth <= 768 ? 'center' : 'flex-start'
      }}>
        <button
          type="button"
          style={{
            width: 'auto',
            order: windowWidth <= 768 ? 1 : 0,
            marginBottom: windowWidth <= 768 ? '15px' : '0'
          }}
          onClick={addEducation}
        >
          Ajouter une éducation
        </button>

        <div style={{
          display: 'flex',
          gap: '10px',
          order: windowWidth <= 768 ? 2 : 0,
          width: windowWidth <= 768 ? '100%' : 'auto',
          justifyContent: windowWidth <= 768 ? 'space-between' : 'flex-start'
        }}>
          <button
            type="button"
            style={{
              width: 'auto',
              flex: windowWidth <= 768 ? '1' : 'none',
              maxWidth: windowWidth <= 768 ? '48%' : 'none'
            }}
            onClick={() => goToStep(1)}
          >
            Précédent
          </button>
          <button
            type="button"
            style={{
              width: 'auto',
              flex: windowWidth <= 768 ? '1' : 'none',
              maxWidth: windowWidth <= 768 ? '48%' : 'none'
            }}
            onClick={() => goToStep(3)}
          >
            Suivant
          </button>
        </div>
      </div>
    </div>
  );
};

const Step3 = ({ experience, handleExperienceChange, addExperience, goToStep }) => {
  const [windowWidth, setWindowWidth] = React.useState(typeof window !== 'undefined' ? window.innerWidth : 1024);

  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleResize = () => setWindowWidth(window.innerWidth);
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  return (
    <div>
      <h3>Étape 3: L'expérience professionnelle</h3>
      {experience.map((exp, index) => (
        <div key={index} className="form-grp">
          <label htmlFor={`jobTitle-${index}`}>Titre d'emploi</label>
          <input
            type="text"
            id={`jobTitle-${index}`}
            name="jobTitle"
            value={exp.jobTitle}
            placeholder="Titre d'emploi"
            onChange={(e) => handleExperienceChange(index, e)}
          />
          <label htmlFor={`company-${index}`}>Entreprise</label>
          <input
            type="text"
            id={`company-${index}`}
            name="company"
            value={exp.company}
            placeholder="Entreprise"
            onChange={(e) => handleExperienceChange(index, e)}
          />
          <label htmlFor={`duration-${index}`}>Durée</label>
          <input
            type="text"
            id={`duration-${index}`}
            name="duration"
            value={exp.duration}
            placeholder="Durée"
            onChange={(e) => handleExperienceChange(index, e)}
          />
        </div>
      ))}
      <div style={{
        display: 'flex',
        flexDirection: windowWidth <= 768 ? 'column' : 'row',
        gap: '10px',
        alignItems: windowWidth <= 768 ? 'center' : 'flex-start',
        justifyContent: windowWidth <= 768 ? 'center' : 'flex-start'
      }}>
        <button
          type="button"
          style={{
            width: 'auto',
            order: windowWidth <= 768 ? 1 : 0,
            marginBottom: windowWidth <= 768 ? '15px' : '0'
          }}
          onClick={addExperience}
        >
          Ajouter une autre expérience
        </button>

        <div style={{
          display: 'flex',
          gap: '10px',
          order: windowWidth <= 768 ? 2 : 0,
          width: windowWidth <= 768 ? '100%' : 'auto',
          justifyContent: windowWidth <= 768 ? 'space-between' : 'flex-start'
        }}>
          <button
            type="button"
            style={{
              width: 'auto',
              flex: windowWidth <= 768 ? '1' : 'none',
              maxWidth: windowWidth <= 768 ? '48%' : 'none'
            }}
            onClick={() => goToStep(2)}
          >
            Précédent
          </button>
          <button
            type="button"
            style={{
              width: 'auto',
              flex: windowWidth <= 768 ? '1' : 'none',
              maxWidth: windowWidth <= 768 ? '48%' : 'none'
            }}
            onClick={() => goToStep(4)}
          >
            Suivant
          </button>
        </div>
      </div>
    </div>
  );
};

const Step4 = ({
  handleFileChange,
  handleFileChangeUpload,
  handleFileChangeWithValidation,
  isFileValid,
  isSubmitting,
  cvLibrary,
  suggestedCVs,
  selectedCVId,
  setSelectedCVId,
  applicationMethod,
  setApplicationMethod,
  coverLetter,
  setCoverLetter,
  cvLibraryLoading
}) => {
  const [windowWidth, setWindowWidth] = React.useState(typeof window !== 'undefined' ? window.innerWidth : 1024);

  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleResize = () => setWindowWidth(window.innerWidth);
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  const isSubmitDisabled = () => {
    if (applicationMethod === 'library') return !selectedCVId || isSubmitting;
    if (applicationMethod === 'upload') return !isFileValid || isSubmitting;
    return isSubmitting; // form method always valid
  };

  return (
    <div>
      <h3>Étape 4: Choisir votre CV</h3>

      {/* Application Method Selection */}
      <div className="form-grp" style={{ marginBottom: '20px' }}>
        <label>Méthode de candidature:</label>
        <div style={{
          display: 'flex',
          flexDirection: windowWidth <= 768 ? 'column' : 'row',
          gap: windowWidth <= 768 ? '12px' : '15px',
          marginTop: '10px'
        }}>
          <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
            <input
              type="radio"
              name="applicationMethod"
              value="library"
              checked={applicationMethod === 'library'}
              onChange={(e) => setApplicationMethod(e.target.value)}
              style={{
                marginRight: '8px',
                transform: windowWidth <= 768 ? 'scale(0.8)' : 'scale(1)',
                width: windowWidth <= 768 ? '14px' : '16px',
                height: windowWidth <= 768 ? '14px' : '16px'
              }}
            />
            Utiliser CV
          </label>
          <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
            <input
              type="radio"
              name="applicationMethod"
              value="upload"
              checked={applicationMethod === 'upload'}
              onChange={(e) => setApplicationMethod(e.target.value)}
              style={{
                marginRight: '8px',
                transform: windowWidth <= 768 ? 'scale(0.8)' : 'scale(1)',
                width: windowWidth <= 768 ? '14px' : '16px',
                height: windowWidth <= 768 ? '14px' : '16px'
              }}
            />
            Télécharger CV
          </label>
          <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
            <input
              type="radio"
              name="applicationMethod"
              value="form"
              checked={applicationMethod === 'form'}
              onChange={(e) => setApplicationMethod(e.target.value)}
              style={{
                marginRight: '8px',
                transform: windowWidth <= 768 ? 'scale(0.8)' : 'scale(1)',
                width: windowWidth <= 768 ? '14px' : '16px',
                height: windowWidth <= 768 ? '14px' : '16px'
              }}
            />
            Remplir formulaire
          </label>
        </div>
      </div>

      {/* CV Library Selection */}
      {applicationMethod === 'library' && (
        <div className="form-grp" style={{ marginBottom: '20px' }}>
          <h4>Vos CVs disponibles:</h4>

          {/* Suggested CVs */}
          {suggestedCVs.length > 0 && (
            <div style={{ marginBottom: '15px' }}>
              <h5 style={{ color: '#2D8BBA', fontSize: '14px' }}>📋 Recommandés pour ce poste:</h5>
              <div style={{ display: 'grid', gap: '10px' }}>
                {suggestedCVs.map((cv) => (
                  <div
                    key={cv._id}
                    onClick={() => setSelectedCVId(cv._id)}
                    style={{
                      border: selectedCVId === cv._id ? '2px solid #2D8BBA' : '1px solid #ddd',
                      borderRadius: '8px',
                      padding: '12px',
                      cursor: 'pointer',
                      backgroundColor: selectedCVId === cv._id ? '#f0f8ff' : '#fff',
                      transition: 'all 0.2s'
                    }}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div>
                        <strong>{cv.title}</strong>
                        {cv.isDefault && <span style={{ color: '#2D8BBA', fontSize: '12px', marginLeft: '8px' }}>⭐ Par défaut</span>}
                        <p style={{ margin: '4px 0', fontSize: '13px', color: '#666' }}>{cv.description}</p>
                        <div style={{ fontSize: '12px', color: '#888' }}>
                          Utilisé {cv.timesUsed} fois
                          {cv.lastUsed && <span> • Dernière utilisation: {new Date(cv.lastUsed).toLocaleDateString('fr-FR')}</span>}
                          {cv.tags && cv.tags.length > 0 && <span> • {cv.tags.join(', ')}</span>}
                        </div>
                      </div>
                      <div style={{ fontSize: '12px', color: '#2D8BBA' }}>
                        ✨ Recommandé
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* All CVs */}
          {cvLibrary.length > 0 ? (
            <div>
              <h5 style={{ fontSize: '14px', marginBottom: '10px' }}>Tous vos CVs:</h5>
              <div style={{ display: 'grid', gap: '10px', maxHeight: '300px', overflowY: 'auto' }}>
                {cvLibrary.map((cv) => (
                  <div
                    key={cv._id}
                    onClick={() => setSelectedCVId(cv._id)}
                    style={{
                      border: selectedCVId === cv._id ? '2px solid #2D8BBA' : '1px solid #ddd',
                      borderRadius: '8px',
                      padding: '12px',
                      cursor: 'pointer',
                      backgroundColor: selectedCVId === cv._id ? '#f0f8ff' : '#fff',
                      transition: 'all 0.2s'
                    }}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div>
                        <strong>{cv.title}</strong>
                        {cv.isDefault && <span style={{ color: '#2D8BBA', fontSize: '12px', marginLeft: '8px' }}>⭐ Par défaut</span>}
                        <p style={{ margin: '4px 0', fontSize: '13px', color: '#666' }}>{cv.description}</p>
                        <div style={{ fontSize: '12px', color: '#888' }}>
                          Utilisé {cv.timesUsed} fois
                          {cv.lastUsed && <span> • Dernière utilisation: {new Date(cv.lastUsed).toLocaleDateString('fr-FR')}</span>}
                          {cv.tags && cv.tags.length > 0 && <span> • {cv.tags.join(', ')}</span>}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div style={{ textAlign: 'center', padding: '20px', color: '#666' }}>
              <p>Aucun CV trouvé dans votre bibliothèque.</p>
              <p style={{ fontSize: '14px' }}>Téléchargez votre premier CV ou utilisez les données du formulaire.</p>
            </div>
          )}
        </div>
      )}

      {/* File Upload */}
      {applicationMethod === 'upload' && (
        <div className="form-grp" style={{ marginBottom: '20px' }}>
          <label htmlFor="cvUpload">Télécharger un nouveau CV:</label>
          <input
            type="file"
            id="cvUpload"
            name="cvUpload"
            accept=".pdf,.doc,.docx"
            onChange={(event) => {
              handleFileChange(event);
              handleFileChangeWithValidation(event);
              handleFileChangeUpload(event);
            }}
            disabled={isSubmitting}
            style={{ marginTop: '8px' }}
          />
          {!isFileValid && (
            <span className="error-message" style={{ color: 'red', marginTop: '5px', display: 'block' }}>
              Veuillez télécharger un fichier CV valide (PDF, DOC, DOCX).
            </span>
          )}
          <p style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
            Le CV sera automatiquement ajouté à votre bibliothèque pour une utilisation future.
          </p>
        </div>
      )}

      {/* Form Data Info */}
      {applicationMethod === 'form' && (
        <div className="form-grp" style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f9f9f9', borderRadius: '8px' }}>
          <h5>📝 Candidature avec les données du formulaire</h5>
          <p style={{ fontSize: '14px', color: '#666', margin: '8px 0' }}>
            Votre candidature sera créée à partir des informations saisies dans les étapes précédentes
            (informations personnelles, éducation, expérience).
          </p>
        </div>
      )}

      {/* Cover Letter */}
      <div className="form-grp" style={{ marginBottom: '20px' }}>
        <label htmlFor="coverLetter">Lettre de motivation (optionnel):</label>
        <textarea
          id="coverLetter"
          name="coverLetter"
          className="form-select"
          placeholder="Expliquez pourquoi vous êtes intéressé par ce poste..."
          value={coverLetter}
          onChange={(e) => setCoverLetter(e.target.value)}
          rows={4}
          style={{ marginTop: '8px', resize: 'vertical' }}
        />
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        style={{
          width: 'auto',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          backgroundColor: isSubmitDisabled() ? '#ccc' : '#2D8BBA',
          color: 'white',
          border: 'none',
          padding: '12px 24px',
          borderRadius: '6px',
          cursor: isSubmitDisabled() ? 'not-allowed' : 'pointer'
        }}
        disabled={isSubmitDisabled()}
      >
        {isSubmitting && (
          <div
            style={{
              width: '16px',
              height: '16px',
              border: '2px solid #f3f3f3',
              borderTop: '2px solid #3498db',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }}
          />
        )}
        {isSubmitting ? 'Soumission en cours...' : 'Soumettre ma candidature'}
      </button>

      {/* CSS Animation */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

// Main Component
export default function Postuler({ agence: propAgence, uuid: propUuid }) {
  const router = useRouter();
  const { uuid: queryUuid, agence: queryAgence } = router.query;
  
  // Use props if available, otherwise use query params
  const uuid = propUuid || queryUuid;
  const agence = propAgence || queryAgence;
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [user, setUser] = useState('');
  const [errors, setErrors] = useState({});
  const [isFileValid, setIsFileValid] = useState(false);
  const [fileError, setFileError] = useState('');

  // Custom step management
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;

  // New CV Library states
  const [cvLibrary, setCvLibrary] = useState([]);
  const [suggestedCVs, setSuggestedCVs] = useState([]);
  const [selectedCVId, setSelectedCVId] = useState(null);
  const [applicationMethod, setApplicationMethod] = useState('library'); // 'library', 'upload', 'form'
  const [coverLetter, setCoverLetter] = useState('');
  const [cvLibraryLoading, setCvLibraryLoading] = useState(false);
  const generatedFilename ="";
  const validateFile = (file) => {
    if (!file) {
      toast.error('Veuillez sélectionner un fichier PDF.');
      setIsFileValid(false);
      return false;
    }
    if (file.type !== 'application/pdf') {
      toast.error('Le fichier doit être au format PDF.');
      setIsFileValid(false);
      return false;
    }
    setFileError('');
    setIsFileValid(true); 
    return true;
  };
 
  const handleFileChangeWithValidation = (event) => {
    const file = event.target.files[0];
    if (validateFile(file)) {
      handleFileChange(event);
      handleFileChangeUpload(event);
    }else {
      console.log("nott Trueee")
      setIsFileValid(false);
    }
  };

  const validateFields = () => {
    const newErrors = {};

    if (!user?.title) newErrors.title = "Veuillez sélectionner une civilité.";
    if (!user?.lastName) newErrors.lastName = "Veuillez entrer un nom.";
    if (!user?.firstName) newErrors.firstName = "Veuillez entrer un prénom.";
    if (!user?.telephone) newErrors.phone = "Veuillez entrer un numéro de portable.";
    if (!user?.email) newErrors.email = "Veuillez entrer une adresse e-mail.";

    Object.values(newErrors).forEach((error) => {
      toast.error(error);
    });

    return Object.keys(newErrors).length === 0;
  };

  const handleNextStep = () => {
    if (validateFields()) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  const goToStep = (step) => {
    setCurrentStep(step);
  };

  const nextStep = () => {
    setCurrentStep(prev => Math.min(prev + 1, totalSteps));
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };
  const company = agence;
  const PostId = uuid;

  const fetchUserData = async () => {
    try {
      const token = localStorage.getItem("token");
      
      const decoded = jwtDecode(token); 
      const userId = decoded.id;

      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      const response = await axios.get(
        `http://82.165.144.72:5000/api/users/${userId}`,
        config
      );
      const userData = response.data;
      setUser(userData);
      setFormData((prevFormData) => ({
        ...prevFormData,
        lastName: userData.lastName,
        firstName: userData.firstName,
        phone: userData.telephone,
        email: userData.email,
      }));
    } catch (err) {
      setError("Failed to fetch user data");
    } finally {
      setLoading(false);
    }
  };

  // Fetch CV Library
  const fetchCVLibrary = async () => {
    try {
      const token = localStorage.getItem("token");
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      const response = await axios.get("http://82.165.144.72:5000/api/cv-library?active=true", config);
      setCvLibrary(response.data.cvs || []);

      // Set default CV if available
      if (response.data.defaultCV) {
        setSelectedCVId(response.data.defaultCV._id);
      }
    } catch (err) {
      console.error("Failed to fetch CV library:", err);
    }
  };

  // Fetch job details to get contract type
  const fetchJobDetails = async () => {
    if (!uuid) {
      console.warn("No UUID provided for job details");
      return null;
    }

    try {
      const token = localStorage.getItem("token");
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      // Check if uuid is a UUID (contains hyphens) or MongoDB ObjectId
      const isUUID = uuid && uuid.includes('-');
      const endpoint = isUUID
        ? `http://82.165.144.72:5000/api/posts/uuid/${uuid}`
        : `http://82.165.144.72:5000/api/posts/${uuid}`;

      const response = await axios.get(endpoint, config);
      return response.data;
    } catch (err) {
      console.error("Failed to fetch job details:", err);
      return null;
    }
  };

  // Fetch CV suggestions for this job
  const fetchCVSuggestions = async () => {
    try {
      const token = localStorage.getItem("token");
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      // Get job details to determine contract type
      const jobDetails = await fetchJobDetails();
      const contractType = jobDetails?.contract || agence || 'CDI'; // fallback to CDI

      const response = await axios.get(
        `http://82.165.144.72:5000/api/cv-library/suggestions?contractType=${contractType}`,
        config
      );
      setSuggestedCVs(response.data.suggestions || []);
    } catch (err) {
      console.error("Failed to fetch CV suggestions:", err);
    }
  };

  useEffect(() => {
    fetchUserData();
    fetchCVLibrary();
    fetchCVSuggestions();
  }, []);

  
  const [formData, setFormData] = useState({
    title: '',
    lastName: '',
    firstName: '',
    filename: generatedFilename, 
    phone: '',
    email: '',
    comments: '',
    PostId:PostId,
    company: company,
    cvUpload: null,
    education: [{ degree: '', institution: '', year: '' }],
    experience: [{ jobTitle: '', company: '', duration: '' }],
  });
  
  const generateFilename = (file) => {
    const timestamp = Date.now(); 
    const fileExtension = file.name.split('.').pop(); 
    const generatedFilename = `${timestamp}.${fileExtension}`; 
    return generatedFilename;
  };

  const uploadPdf = async (file, generatedFilename) => {
    const formData = new FormData();
    formData.append('cvUpload', file);
    formData.append('filename', generatedFilename);

    try {
      const response = await fetch('http://82.165.144.72:4000/upload_cv', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = generatedFilename; 
        document.body.appendChild(a);
        a.click();
        a.remove();
      } else {
        console.error('Upload failed');
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const handleInputChangeText = (e) => {
    if (user) {
      const updatedUser = {
        ...user,
        [e.target.name]: e.target.value,
      };
      setUser(updatedUser);
    }
  };
  
  const handleFileChangeUpload = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      const generatedFilename = generateFilename(selectedFile);
      console.log(generateFilename)
      setFile(selectedFile);
      setFormData((prevFormData) => ({
        ...prevFormData,
        filename: generatedFilename, 
      }));
      uploadPdf(selectedFile, generatedFilename);
    }
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };
  
  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      const generatedFilename = generateFilename(selectedFile);
      setFile(selectedFile);
      setFormData((prevFormData) => ({
        ...prevFormData,
        cvUpload: selectedFile,
        filename: generatedFilename, 
      }));
      uploadPdf(selectedFile, generatedFilename);
    }
  };

  const handleEducationChange = (index, e) => {
    const { name, value } = e.target;
    const updatedEducation = [...formData.education];
    updatedEducation[index] = { ...updatedEducation[index], [name]: value };
    setFormData({ ...formData, education: updatedEducation });
  };

  const handleExperienceChange = (index, e) => {
    const { name, value } = e.target;
    const updatedExperience = [...formData.experience];
    updatedExperience[index] = { ...updatedExperience[index], [name]: value };
    setFormData({ ...formData, experience: updatedExperience });
  };

  const addEducation = () => {
    setFormData({
      ...formData,
      education: [...formData.education, { degree: '', institution: '', year: '' }],
    });
  };

  const addExperience = () => {
    setFormData({
      ...formData,
      experience: [...formData.experience, { jobTitle: '', company: '', duration: '' }],
    });
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    // Check if uuid is available
    if (!uuid) {
      toast.error('Erreur: Identifiant de poste manquant');
      return;
    }

    // Set loading state to true
    setIsSubmitting(true);

    try {
      const token = localStorage.getItem("token");
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      let response;
      let endpoint;
      let requestData;

      // Determine which API to use based on application method
      if (applicationMethod === 'library' && selectedCVId) {
        // Apply with existing CV from library
        endpoint = 'http://82.165.144.72:5000/api/applications/apply-existing-cv';
        requestData = {
          postId: uuid,
          postUuid: uuid,
          cvLibraryId: selectedCVId,
          coverLetter: coverLetter,
          comments: formData.comments || ''
        };

        response = await axios.post(endpoint, requestData, config);

      } else if (applicationMethod === 'upload' && formData.cvUpload) {
        // Apply with new CV upload
        endpoint = 'http://82.165.144.72:5000/api/applications/apply-upload';
        const uploadData = new FormData();
        uploadData.append('postId', uuid);
        uploadData.append('postUuid', uuid);
        uploadData.append('coverLetter', coverLetter);
        uploadData.append('comments', formData.comments || '');
        uploadData.append('cv', formData.cvUpload);

        response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: uploadData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          if (errorData.message === "You have already applied to this job") {
            toast.warning('Vous avez déjà postulé à cette offre d\'emploi');
            return;
          } else {
            throw new Error(errorData.message || 'Upload failed');
          }
        }
        response = { data: await response.json() };

      } else {
        // Apply with form data
        endpoint = 'http://82.165.144.72:5000/api/applications/apply-form';
        requestData = {
          postId: uuid,
          postUuid: uuid,
          coverLetter: coverLetter,
          comments: formData.comments || '',
          cvData: {
            title: user?.title || formData.title,
            firstName: user?.firstName || formData.firstName,
            lastName: user?.lastName || formData.lastName,
            email: user?.email || formData.email,
            phone: user?.telephone || formData.phone,
            education: formData.education,
            experience: formData.experience
          }
        };

        response = await axios.post(endpoint, requestData, config);
      }

      if (response.data) {
        // Show success toast
        toast.success('Votre candidature a été soumise avec succès !');

        // Redirect to /offres page after a short delay
        setTimeout(() => {
          router.push('/offres');
        }, 2000);
      } else {
        toast.error('Erreur lors de la soumission');
      }
    } catch (error) {
      console.error('Error submitting application:', error);

      // Check if it's a duplicate application error
      if (error.response && error.response.data && error.response.data.message) {
        const errorMessage = error.response.data.message;
        if (errorMessage === "You have already applied to this job") {
          toast.warning('Vous avez déjà postulé à cette offre d\'emploi');
        } else {
          toast.error(errorMessage);
        }
      } else {
        toast.error('Erreur lors de la soumission de votre candidature');
      }
    } finally {
      // Set loading state to false regardless of success or failure
      setIsSubmitting(false);
    }
  };

  return (
    <Layout headerStyle={2} footerStyle={3}>
      <section className="contact-area contact-bg flex items-center justify-center" style={{
        minHeight: '100vh',
        background: `
          linear-gradient(135deg, rgba(45, 139, 186, 0.05) 0%, rgba(45, 139, 186, 0.15) 100%),
          url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KPGcgZmlsbD0iIzJkOGJiYSIgZmlsbC1vcGFjaXR5PSIwLjA1Ij4KPGNpcmNsZSBjeD0iNzUiIGN5PSI3NSIgcj0iMyIvPgo8Y2lyY2xlIGN4PSIxNSIgY3k9IjE1IiByPSIzIi8+CjxjaXJjbGUgY3g9IjQ1IiBjeT0iNDUiIHI9IjMiLz4KPC9nPgo8L2c+Cjwvc3ZnPg==')
        `,
        backgroundSize: '60px 60px',
        backgroundAttachment: 'fixed',
        position: 'relative',
        paddingTop: '80px',
        paddingBottom: '80px'
      }}>
        {/* Background decorative elements */}
        <div style={{
          position: 'absolute',
          top: '10%',
          left: '5%',
          width: '100px',
          height: '100px',
          borderRadius: '50%',
          background: 'linear-gradient(45deg, rgba(45, 139, 186, 0.1), rgba(45, 139, 186, 0.2))',
          zIndex: 1
        }}></div>
        <div style={{
          position: 'absolute',
          bottom: '15%',
          right: '8%',
          width: '150px',
          height: '150px',
          borderRadius: '30px',
          background: 'linear-gradient(135deg, rgba(45, 139, 186, 0.08), rgba(45, 139, 186, 0.15))',
          transform: 'rotate(15deg)',
          zIndex: 1
        }}></div>
        <div style={{
          position: 'absolute',
          top: '60%',
          left: '2%',
          width: '80px',
          height: '80px',
          background: 'linear-gradient(45deg, rgba(45, 139, 186, 0.05), rgba(45, 139, 186, 0.12))',
          borderRadius: '15px',
          transform: 'rotate(-10deg)',
          zIndex: 1
        }}></div>

        <div className="container" style={{ position: 'relative', zIndex: 2 }}>
          <div className="row justify-center">
            <div className="col-lg-8 mx-auto">
              <div className="contact-form" style={{
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(10px)',
                borderRadius: '20px',
                padding: '40px',
                margin:'10% 0 10% 0',
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                position: 'relative',
                overflow: 'hidden'
              }}>
                {/* Form header with gradient */}
                <div style={{
                  background: 'linear-gradient(135deg, #2D8BBA 0%, #1e6b8a 100%)',
                  margin: '-40px -40px 30px -40px',
                  padding: '30px 40px',
                  borderRadius: '20px 20px 0 0',
                  color: 'white',
                  textAlign: 'center'
                }}>
                  <h2 style={{ margin: 0, fontSize: '28px', fontWeight: '600' , color: 'white'}}>
                    Postuler à l'offre
                  </h2>
                  <p style={{ margin: '8px 0 0 0', opacity: 0.9, fontSize: '16px', color: 'white'  }}>
                    Complétez votre candidature en quelques étapes
                  </p>
                </div>

                {/* Step Progress Indicator */}
                <div style={{ marginBottom: '30px', textAlign: 'center' }}>
                  <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: '15px', marginBottom: '15px' }}>
                    {[1, 2, 3, 4].map((step, index) => (
                      <React.Fragment key={step}>
                        <div
                          style={{
                            width: '50px',
                            height: '50px',
                            borderRadius: '50%',
                            background: currentStep >= step 
                              ? 'linear-gradient(135deg, #2D8BBA 0%, #1e6b8a 100%)' 
                              : 'linear-gradient(135deg, #e0e0e0 0%, #f5f5f5 100%)',
                            color: currentStep >= step ? 'white' : '#666',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontWeight: 'bold',
                            cursor: 'pointer',
                            transition: 'all 0.3s ease',
                            boxShadow: currentStep >= step 
                              ? '0 4px 15px rgba(45, 139, 186, 0.3)' 
                              : '0 2px 8px rgba(0, 0, 0, 0.1)',
                            transform: currentStep === step ? 'scale(1.1)' : 'scale(1)'
                          }}
                          onClick={() => goToStep(step)}
                        >
                          {step}
                        </div>
                        {index < 3 && (
                          <div style={{
                            width: '40px',
                            height: '2px',
                            background: currentStep > step 
                              ? 'linear-gradient(90deg, #2D8BBA, #1e6b8a)' 
                              : '#e0e0e0',
                            transition: 'all 0.3s ease'
                          }}></div>
                        )}
                      </React.Fragment>
                    ))}
                  </div>
                  <div style={{ 
                    color: '#2D8BBA', 
                    fontWeight: '600',
                    fontSize: '16px'
                  }}>
                    Étape {currentStep} sur {totalSteps}
                  </div>
                </div>

                <form onSubmit={handleSubmit}>
                  {currentStep === 1 && (
                    <Step1
                      handleInputChange={handleInputChange}
                      handleInputChangeText={handleInputChangeText}
                      handleNextStep={handleNextStep}
                      user={user}
                    />
                  )}
                  {currentStep === 2 && (
                    <Step2
                      education={formData.education}
                      handleEducationChange={handleEducationChange}
                      addEducation={addEducation}
                      goToStep={goToStep}
                    />
                  )}
                  {currentStep === 3 && (
                    <Step3
                      experience={formData.experience}
                      handleExperienceChange={handleExperienceChange}
                      addExperience={addExperience}
                      goToStep={goToStep}
                    />
                  )}
                  {currentStep === 4 && (
                    <Step4
                      handleFileChange={handleFileChange}
                      handleFileChangeUpload={handleFileChangeUpload}
                      handleFileChangeWithValidation={handleFileChangeWithValidation}
                      isFileValid={isFileValid}
                      isSubmitting={isSubmitting}
                      cvLibrary={cvLibrary}
                      suggestedCVs={suggestedCVs}
                      selectedCVId={selectedCVId}
                      setSelectedCVId={setSelectedCVId}
                      applicationMethod={applicationMethod}
                      setApplicationMethod={setApplicationMethod}
                      coverLetter={coverLetter}
                      setCoverLetter={setCoverLetter}
                      cvLibraryLoading={cvLibraryLoading}
                    />
                  )}
                </form>
              </div>
            </div>
          </div>
        </div>
        
        {/* Floating decorative shape */}
        <div style={{
          position: 'absolute',
          bottom: '20px',
          right: '20px',
          zIndex: 1
        }}>
          <img 
            src="/images/temporels/cercle.png" 
            alt="" 
            style={{ 
              width: '120px', 
              height: 'auto',
              opacity: 0.6,
              filter: 'hue-rotate(200deg)'
            }} 
          />
        </div>
      </section>
      <ToastContainer 
        position="top-right" 
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        style={{ zIndex: 9999 }}
      />
    </Layout>
  );
}
