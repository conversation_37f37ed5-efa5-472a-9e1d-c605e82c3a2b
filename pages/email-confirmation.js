import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import axios from 'axios';
import { MDBContainer, MDBCol, MDBRow, MDBCard, MDBCardBody, MDBCardImage, MDBIcon } from 'mdb-react-ui-kit';
import { ToastContainer, toast } from "react-toastify";
import 'react-toastify/dist/ReactToastify.css';
import { motion } from 'framer-motion';
import styles from '@/components/Auth/Signin/signin.module.css';

export default function EmailConfirmation() {
  const router = useRouter();
  const { email, confirmation } = router.query;
  const [isLoading, setIsLoading] = useState(false);
  const [canResend, setCanResend] = useState(true);
  const [countdown, setCountdown] = useState(0);

  useEffect(() => {
    if (confirmation === 'success') {
      toast.success('Votre email a été confirmé avec succès ! Vous pouvez maintenant vous connecter.');
    }
  }, [confirmation]);

  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    } else if (countdown === 0 && !canResend) {
      setCanResend(true);
    }
    return () => clearTimeout(timer);
  }, [countdown, canResend]);

  const handleResendEmail = async () => {
    if (!email) {
      toast.error('Adresse email manquante');
      return;
    }

    setIsLoading(true);
    try {
      const response = await axios.post('http://82.165.144.72:5000/api/users/resend-confirmation', {
        email: email
      });

      if (response.status === 200) {
        toast.success('Email de confirmation renvoyé avec succès. Vérifiez votre boîte e-mail.');
        setCanResend(false);
        setCountdown(60); // 60 seconds cooldown
      }
    } catch (error) {
      console.error('Erreur lors du renvoi:', error);
      if (error.response && error.response.data && error.response.data.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error('Erreur lors du renvoi de l\'email. Veuillez réessayer.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <MDBContainer fluid>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <MDBCard className={styles.signinCard}>
            <MDBRow className="g-0">
              <MDBCol md="5" className={styles.imageCol}>
                <div className={styles.logoContainer}>
                  <Link href="/" style={{ display: "inline-block", cursor: "pointer" }}>
                    <MDBCardImage
                      src="/assets/img/logo/logov2.png"
                      alt="Professional Image"
                      className={styles.logoImage}
                    />
                  </Link>
                  <div className={styles.benefitsList}>
                    <h4 className={styles.benefitsTitle}>Vérification d'email</h4>
                    <ul className={styles.benefits}>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Sécurisez votre compte</span>
                      </li>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Accédez à toutes les fonctionnalités</span>
                      </li>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Recevez nos notifications importantes</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </MDBCol>
              <MDBCol md="7">
                <MDBCardBody className={styles.formContainer}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="text-center"
                  >
                    <div className="mb-4">
                      <MDBIcon 
                        fas 
                        icon="envelope-open" 
                        size="4x" 
                        style={{ color: '#2D8BBA', marginBottom: '20px' }}
                      />
                    </div>

                    <h3 className={styles.stepTitle}>
                      {confirmation === 'success' ? 'Email confirmé !' : 'Vérifiez votre email'}
                    </h3>

                    {confirmation === 'success' ? (
                      <div>
                        <p className={styles.stepDescription}>
                          Votre adresse email a été confirmée avec succès ! 
                          Vous pouvez maintenant vous connecter à votre compte.
                        </p>
                        <Link href="/signin">
                          <button className={styles.primaryButton}>
                            Se connecter
                          </button>
                        </Link>
                      </div>
                    ) : (
                      <div>
                        <p className={styles.stepDescription}>
                          Nous avons envoyé un email de confirmation à :
                        </p>
                        <p style={{ fontWeight: 'bold', color: '#2D8BBA', marginBottom: '20px' }}>
                          {email}
                        </p>
                        <p className={styles.stepDescription}>
                          Cliquez sur le lien dans l'email pour confirmer votre compte.
                          Si vous ne voyez pas l'email, vérifiez votre dossier spam.
                        </p>

                        <div className="mt-4">
                          <button
                            onClick={handleResendEmail}
                            disabled={!canResend || isLoading}
                            className={canResend ? styles.primaryButton : styles.secondaryButton}
                            style={{ 
                              opacity: (!canResend || isLoading) ? 0.6 : 1,
                              cursor: (!canResend || isLoading) ? 'not-allowed' : 'pointer'
                            }}
                          >
                            {isLoading ? (
                              <>
                                <MDBIcon fas icon="spinner" spin className="me-2" />
                                Envoi en cours...
                              </>
                            ) : !canResend ? (
                              `Renvoyer dans ${countdown}s`
                            ) : (
                              'Renvoyer l\'email'
                            )}
                          </button>
                        </div>

                        <div className="mt-4">
                          <p className={styles.stepDescription}>
                            Vous avez déjà confirmé votre email ?{' '}
                            <Link href="/signin" className={styles.forgotPassword}>
                              Se connecter
                            </Link>
                          </p>
                        </div>
                      </div>
                    )}
                  </motion.div>
                </MDBCardBody>
              </MDBCol>
            </MDBRow>
          </MDBCard>
        </motion.div>
      </MDBContainer>
      <ToastContainer position="bottom-left" />
    </div>
  );
}
