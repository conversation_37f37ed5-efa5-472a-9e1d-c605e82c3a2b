import { useEffect } from "react";

const Chatbot = () => {
  useEffect(() => {
    // Charger le script Tidio uniquement côté client
    const script = document.createElement("script");
    script.src = "//code.tidio.co/6fvpchzvuselzmnqgi8oaumdz5qmbxvs.js";
    script.async = true;
    document.body.appendChild(script);

    return () => {
      // Nettoyage : supprimer le script lorsqu'on quitte le composant
      document.body.removeChild(script);
    };
  }, []);

  return null; // Ce composant ne rend rien, il charge uniquement le script
};

export default Chatbot;
