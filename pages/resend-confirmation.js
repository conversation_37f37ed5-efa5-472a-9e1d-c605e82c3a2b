import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import axios from 'axios';
import { MDBContainer, MDBCol, MDBRow, MDBCard, MDBCardBody, MDBCardImage, MDBInput, MDBIcon } from 'mdb-react-ui-kit';
import { ToastContainer, toast } from "react-toastify";
import 'react-toastify/dist/ReactToastify.css';
import { motion } from 'framer-motion';
import styles from '@/components/Auth/Signin/signin.module.css';

export default function ResendConfirmation() {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({ email: "" });

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email) {
      setErrors({ email: "L'email est requis." });
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setErrors({ email: "Format d'email invalide." });
      return;
    }

    setIsLoading(true);
    setErrors({ email: "" });

    try {
      const response = await axios.post('http://82.165.144.72:5000/api/users/resend-confirmation', {
        email: email
      });

      if (response.status === 200) {
        toast.success('Email de confirmation renvoyé avec succès. Vérifiez votre boîte e-mail.');
        setTimeout(() => {
          router.push('/email-confirmation?email=' + encodeURIComponent(email));
        }, 2000);
      }
    } catch (error) {
      console.error('Erreur lors du renvoi:', error);
      if (error.response && error.response.data && error.response.data.message) {
        setErrors({ email: error.response.data.message });
      } else {
        setErrors({ email: 'Erreur lors du renvoi de l\'email. Veuillez réessayer.' });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <MDBContainer fluid>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <MDBCard className={styles.signinCard}>
            <MDBRow className="g-0">
              <MDBCol md="5" className={styles.imageCol}>
                <div className={styles.logoContainer}>
                  <Link href="/" style={{ display: "inline-block", cursor: "pointer" }}>
                    <MDBCardImage
                      src="/assets/img/logo/logov2.png"
                      alt="Professional Image"
                      className={styles.logoImage}
                    />
                  </Link>
                  <div className={styles.benefitsList}>
                    <h4 className={styles.benefitsTitle}>Renvoyer la confirmation</h4>
                    <ul className={styles.benefits}>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Email de confirmation rapide</span>
                      </li>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Vérification sécurisée</span>
                      </li>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Accès immédiat après confirmation</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </MDBCol>
              <MDBCol md="7">
                <MDBCardBody className={styles.formContainer}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="text-center mb-4">
                      <MDBIcon 
                        fas 
                        icon="paper-plane" 
                        size="3x" 
                        style={{ color: '#2D8BBA', marginBottom: '20px' }}
                      />
                    </div>

                    <h3 className={styles.stepTitle}>Renvoyer l'email de confirmation</h3>
                    <p className={styles.stepDescription}>
                      Entrez votre adresse email pour recevoir un nouvel email de confirmation.
                    </p>

                    <form onSubmit={handleSubmit}>
                      <div className="mb-4">
                        <label className="form-label">Adresse email</label>
                        <MDBInput
                          placeholder="Entrez votre adresse email"
                          type='email'
                          value={email}
                          onChange={(e) => {
                            setEmail(e.target.value);
                            setErrors({ email: "" });
                          }}
                          className={styles.formInput}
                          contrast
                        />
                        {errors.email && <div className="text-danger mt-1">{errors.email}</div>}
                      </div>

                      <div className="d-flex justify-content-center mb-4">
                        <button
                          type="submit"
                          disabled={isLoading}
                          className={styles.primaryButton}
                          style={{ 
                            opacity: isLoading ? 0.6 : 1,
                            cursor: isLoading ? 'not-allowed' : 'pointer'
                          }}
                        >
                          {isLoading ? (
                            <>
                              <MDBIcon fas icon="spinner" spin className="me-2" />
                              Envoi en cours...
                            </>
                          ) : (
                            'Renvoyer l\'email'
                          )}
                        </button>
                      </div>

                      <div className="text-center">
                        <p className={styles.stepDescription}>
                          Vous vous souvenez de votre mot de passe ?{' '}
                          <Link href="/signin" className={styles.forgotPassword}>
                            Se connecter
                          </Link>
                        </p>
                      </div>
                    </form>
                  </motion.div>
                </MDBCardBody>
              </MDBCol>
            </MDBRow>
          </MDBCard>
        </motion.div>
      </MDBContainer>
      <ToastContainer position="bottom-left" />
    </div>
  );
}
