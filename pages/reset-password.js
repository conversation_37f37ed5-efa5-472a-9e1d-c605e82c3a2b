import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import axios from 'axios';
import Link from 'next/link';
import { MDBContainer, MDBCol, MDBRow, MDBInput, MDBCard, MDBCardBody, MDBCardImage, MDBIcon } from 'mdb-react-ui-kit';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { motion } from 'framer-motion';
import styles from '../components/Auth/Signin/signin.module.css';

export default function ResetPassword() {
  const router = useRouter();
  const { token } = router.query;
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState({ password: '', confirmPassword: '' });
  const [isMobile, setIsMobile] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState({ level: 0, color: 'red', width: 0 });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  const calculatePasswordStrength = (password) => {
    if (!password) {
      return { level: 0, color: 'red', width: 0 };
    }

    const hasLetter = /[a-zA-Z]/.test(password);
    const hasDigit = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
    const hasMinLength = password.length >= 8;

    if (hasLetter && !hasDigit && !hasSpecialChar) {
      return { level: 1, color: '#ef4444', width: 30 };
    }

    if ((hasLetter && hasDigit && !hasSpecialChar) ||
        (hasLetter && !hasDigit && hasSpecialChar) ||
        (hasLetter && hasDigit && hasSpecialChar && !hasMinLength) || (!hasLetter && hasDigit && hasSpecialChar)) {
      return { level: 2, color: '#eab308', width: 60 };
    }

    if (hasLetter && hasDigit && hasSpecialChar && hasMinLength) {
      return { level: 3, color: '#22c55e', width: 100 };
    }

    return { level: 0, color: '#ef4444', width: 10 };
  };

  const handlePasswordChange = (e) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    setErrors({ ...errors, password: '' });
    const strength = calculatePasswordStrength(newPassword);
    setPasswordStrength(strength);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    let hasErrors = false;
    const newErrors = { password: '', confirmPassword: '' };

    if (!password) {
      newErrors.password = 'Le mot de passe est requis.';
      hasErrors = true;
    } else if (passwordStrength.level < 3) {
      newErrors.password = 'Le mot de passe doit être plus fort.';
      hasErrors = true;
    }

    if (!confirmPassword) {
      newErrors.confirmPassword = 'La confirmation du mot de passe est requise.';
      hasErrors = true;
    } else if (password !== confirmPassword) {
      newErrors.confirmPassword = 'Les mots de passe ne correspondent pas.';
      hasErrors = true;
    }

    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await axios.post("http://82.165.144.72:5000/api/users/reset-password", { 
        token, 
        password 
      });

      if (response.status === 200) {
        toast.success('Mot de passe réinitialisé avec succès.');
        setTimeout(() => {
          router.push('/signin'); 
        }, 2000);
      }
    } catch (error) {
      setIsSubmitting(false);
      console.error('Erreur lors de la réinitialisation du mot de passe :', error);
      toast.error('Échec de la réinitialisation du mot de passe. Le lien de réinitialisation est peut-être invalide ou expiré.');
    }
  };

  return (
    <div className={styles.container}>
      <MDBContainer fluid>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <MDBCard className={styles.signinCard}>
            <MDBRow className="g-0">
              <MDBCol md="5" className={styles.imageCol}>
                <div className={styles.logoContainer}>
                  <Link href="/" style={{ display: "inline-block", cursor: "pointer" }}>
                    <MDBCardImage
                      src="/assets/img/logo/logov2.png"
                      alt="Reset Password"
                      className={styles.logoImage}
                    />
                  </Link>
                  <div className={styles.benefitsList}>
                    <h4 className={styles.benefitsTitle}>
                      Sécurisez votre compte :
                    </h4>
                    <ul className={styles.benefits}>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Créez un mot de passe fort et unique</span>
                      </li>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Utilisez au moins 8 caractères</span>
                      </li>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Incluez lettres, chiffres et caractères spéciaux</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </MDBCol>
              <MDBCol md="7">
                <MDBCardBody className={styles.formContainer}>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginBottom: '2rem'
                  }}>
                    <div style={{
                      width: '50px',
                      height: '50px',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      background: '#2196F3',
                      color: 'white'
                    }}>
                      <MDBIcon fas icon="lock" />
                    </div>
                  </div>

                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <h3 className={styles.stepTitle}>
                      Réinitialiser le mot de passe
                    </h3>

                    <form onSubmit={handleSubmit}>
                      <div className="mb-4">
                        <label className="form-label">Nouveau mot de passe</label>
                        <MDBInput
                          placeholder={isMobile ? "Nouveau mot de passe" : "Entrez votre nouveau mot de passe"}
                          type='password'
                          value={password}
                          onChange={handlePasswordChange}
                          className={styles.formInput}
                          contrast
                        />
                        
                        {/* Password Strength Progress Bar */}
                        <div style={{
                          marginTop: '4px',
                          width: '100%',
                          position: 'relative'
                        }}>
                          <div style={{
                            width: '100%',
                            height: '6px',
                            backgroundColor: '#e5e7eb',
                            borderRadius: '2px',
                            overflow: 'hidden'
                          }}>
                            <div style={{
                              width: `${passwordStrength.width}%`,
                              height: '100%',
                              backgroundColor: passwordStrength.color,
                              transition: 'all 0.3s ease',
                              borderRadius: '2px'
                            }}></div>
                          </div>
                        </div>

                        {errors.password && <div className="text-danger mt-1">{errors.password}</div>}
                      </div>

                      <div className="mb-4">
                        <label className="form-label">Confirmer le mot de passe</label>
                        <MDBInput
                          placeholder={isMobile ? "Confirmer le mot de passe" : "Confirmez votre nouveau mot de passe"}
                          type='password'
                          value={confirmPassword}
                          onChange={(e) => {
                            setConfirmPassword(e.target.value);
                            setErrors({ ...errors, confirmPassword: '' });
                          }}
                          className={styles.formInput}
                          contrast
                        />
                        {errors.confirmPassword && <div className="text-danger mt-1">{errors.confirmPassword}</div>}
                      </div>

                      <div className="d-flex justify-content-center mt-4">
                        <button
                          type="submit"
                          disabled={passwordStrength.level < 3 || isSubmitting}
                          className={styles.primaryButton}
                          style={{ 
                            opacity: (passwordStrength.level < 3 || isSubmitting) ? 0.6 : 1,
                            cursor: (passwordStrength.level < 3 || isSubmitting) ? 'not-allowed' : 'pointer'
                          }}
                        >
                          {isSubmitting ? (
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                              <div style={{
                                width: '20px',
                                height: '20px',
                                border: '2px solid #ffffff',
                                borderTop: '2px solid transparent',
                                borderRadius: '50%',
                                animation: 'spin 1s linear infinite'
                              }}></div>
                            </div>
                          ) : (
                            <>
                              <span className="desktop-text">Réinitialiser le mot de passe</span>
                              <span className="mobile-text">Réinitialiser</span>
                            </>
                          )}
                        </button>
                      </div>

                      <div className="text-center mt-4">
                        <p className={styles.stepDescription}>
                          Vous vous souvenez de votre mot de passe ?{' '}
                          <Link href="/signin" className={styles.forgotPassword}>
                            Connectez-vous
                          </Link>
                        </p>
                      </div>
                    </form>
                  </motion.div>
                </MDBCardBody>
              </MDBCol>
            </MDBRow>
          </MDBCard>
        </motion.div>
      </MDBContainer>

      <ToastContainer position="bottom-left" />

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .desktop-text {
          display: inline;
        }

        .mobile-text {
          display: none;
        }

        @media (max-width: 768px) {
          .desktop-text {
            display: none !important;
          }

          .mobile-text {
            display: inline !important;
          }
        }
      `}</style>
    </div>
  );
}