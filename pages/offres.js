import Link from "next/link";
import Image from "next/image";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { FiBookmark, FiArrowUpRight } from "@/public/assets/icons/vander";
import FormSelect from "@/components/sections/home2/FormSelect";
import { useRouter } from 'next/router';
import { useEffect, useState } from "react";
import axios from 'axios';
import Layout from "@/components/layout/Layout";

export default function JobGridThree() {
  const router = useRouter();
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [isMobile, setIsMobile] = useState(false);
  const itemsPerPage = 6; // Number of items per page

  const updateUrlParams = (newSearchTerm, newRegion) => {
    const queryParams = {};
    if (newSearchTerm) queryParams.keyword = newSearchTerm;
    if (newRegion) queryParams.region = newRegion;

    router.push({
      pathname: router.pathname,
      query: queryParams,
    }, undefined, { shallow: true });
  };

  // Fetch and filter the files once they are loaded
  const filteredFiles = files.filter((file) => {
    // Only show approved posts
    const isApproved = file.validate === 'approved';

    const matchesSearchTerm =
      (file.descriptionDuPoste && file.descriptionDuPoste.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (file.agence && file.agence.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (file.ville && file.ville.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (file.region && file.region.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesRegion = selectedRegion ? (file.region && file.region.toLowerCase() === selectedRegion.toLowerCase()) : true;

    return isApproved && matchesSearchTerm && matchesRegion;
  });

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredFiles.slice(indexOfFirstItem, indexOfLastItem);

  // Handle page change
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // Calculate total pages
  const totalPages = Math.ceil(filteredFiles.length / itemsPerPage);

  useEffect(() => {
    const { region, keyword } = router.query;
    if (region) {
      setSelectedRegion(region);
    }
    if (keyword) {
      setSearchTerm(keyword);
    }

    const fetchFiles = async () => {
      try {
        const token = localStorage.getItem('token');

        const config = {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };

        const response = await axios.get("http://82.165.144.72:5000/api/posts/all", config);
        setFiles(response.data);
      } catch (err) {
        if (err.response && err.response.status === 401) {
          setError("Unauthorized access, please login.");
          toast.error("Unauthorized access, please login.");
        } else {
          toast.error("Veuillez vous connecter d'abord");
          setTimeout(() => {
            router.push('/signin');
          });
        }
      } finally {
        setLoading(false);
      }
    };

    fetchFiles();
  }, [router.query]);

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  useEffect(() => {
    // Reset page to 1 if the files or filters change
    setCurrentPage(1);
  }, [files, searchTerm, selectedRegion]);

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '100vh' }}>
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Chargement...</span>
        </div>
      </div>
    );
  }

  if (error) {
    toast.error("Veuillez vous connecter d'abord");
    setTimeout(() => {
      router.push('/signin');
    });
  }

  if (files.length === 0) {
    toast.error("Veuillez vous connecter d'abord");
    setTimeout(() => {
      router.push('/signin');
    });
  }

  return (
    <>
      {/* Hero Section */}
      <Layout headerStyle={2} footerStyle={3}>
        <div style={{ paddingTop: "120px", background: "#fff",  }}></div>
        <section
          className="bg-half-170 d-table w-100"
          style={{ backgroundImage: "url('/images/Slider/bg.jpg')", backgroundPosition: "top" }}
        >
          <div  style={{ 
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            
          }}></div>
          <div className="container position-relative" style={{ zIndex: 1 }}>
            <div className="row justify-content-center">
              <div className="col-lg-10">
                <div className="title-heading text-center text-white">
                  <h1 className="fw-bold mb-3" style={{ color: '#2D8BBA' }}>Nos offres d'emploi</h1>
                  <p className="para-desc mx-auto text-white">
                    Découvrez nos opportunités d'emploi et trouvez le poste qui vous correspond
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        <div className="position-relative">
          <div className="shape overflow-hidden text-white">
            <svg viewBox="0 0 2880 48" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M0 48H1437.5H2880V0H2160C1442.5 52 720 0 720 0H0V48Z" fill="currentColor"></path>
            </svg>
          </div>
        </div>

        <section className="section py-5">
          <div className="container custom-container">
            <div className="row justify-content-center">
              <div className="col-12">
                <div className="title-heading text-center mb-5">
                  <h2 className="heading fw-semibold mb-3 " style={{ color: '#2D8BBA' }}>
                    Nos dernières offres pour vous
                  </h2>
                  <div className="mt-4">
                    <FormSelect />
                  </div>
                </div>
              </div>
            </div>

            <div className="row g-4 mt-2">
              {currentItems.length === 0 ? (
                <div className="col-12 text-center py-5">
                  <div className="d-flex flex-column align-items-center justify-content-center" style={{ minHeight: "300px" }}>
                    <div style={{
                      width: "100px",
                      height: "100px",
                      borderRadius: "50%",
                      backgroundColor: "rgba(45, 139, 186, 0.1)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      marginBottom: "20px"
                    }}>
                      <i className="fas fa-search" style={{ fontSize: "40px", color: "#2D8BBA" }}></i>
                    </div>
                    <h3 style={{ color: "#2D8BBA", marginBottom: "15px" }}>Aucune offre trouvée</h3>
                    <p className="text-muted" style={{ maxWidth: "500px", margin: "0 auto 25px" }}>
                      Aucune offre ne correspond à votre recherche. Essayez de modifier vos critères ou revenez plus tard.
                    </p>
                    <button 
                      onClick={() => {
                        setSearchTerm("");
                        setSelectedRegion("");
                        router.push("/offres", undefined, { shallow: true });
                      }}
                      className=" button-primary"
                      style={{
                        backgroundColor: "#2D8BBA",
                        border: "none",
                        color: "white",
                        padding: "10px 25px",
                        borderRadius: "25px",
                        fontWeight: "500",
                        transition: "all 0.3s ease"
                      }}
                     
                    >
                      Réinitialiser la recherche
                    </button>
                  </div>
                </div>
              ) : (
                currentItems.map((item, index) => (
                  <div className="col-lg-4 col-md-6 col-12" key={index}>
                    <div className="job-card bg-white rounded-lg overflow-hidden border border-gray-200 hover:border-blue-500 transition-all duration-300"
                    style={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      position: 'relative',
                      boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                    }}>
                    {/* Circular image at the top */}
                    <div className="w-full text-center pt-4">
                      <div style={{
                        width: '80px',
                        height: '80px',
                        margin: '0 auto',
                        position: 'relative',
                        borderRadius: '50%',
                        overflow: 'hidden',
                        border: '3px solid #2D8BBA'
                      }}>
                        <Image
                          src={item.recruiterPhoto || item.assignedRecruiter?.photoUrl || "/images/Slider/sabrina.png"}
                          layout="fill"
                          objectFit="cover"
                          alt="Recruiter Image"
                          className="transition-transform duration-300 hover:scale-105"
                        />
                      </div>
                      <div className="mt-2" style={{ color: '#2D8BBA', fontWeight: '500', fontSize: '0.95rem' }}>
                        {item.recruiterName || item.assignedRecruiter?.name || "Recruteur"}
                      </div>
                    </div>
                    
                    <div className="p-4">
                      {/* Header */}
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1 text-center">
                          <h4 className="text-xl font-semibold mb-2" style={{ color: '#2D8BBA' }}>
                            {item.contract}
                          </h4>
                          <p className="text-gray-600 mb-2" style={{ fontSize: '0.95rem' }}>
                            {item.agence}
                          </p>
                        </div>
                      </div>

                      {/* Location Tags */}
                      <div className="flex flex-wrap text-center gap-2 mb-4 justify-center">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm"
                          style={{ 
                            backgroundColor: 'rgba(52, 133, 182, 0.1)', 
                            color: '#2D8BBA',
                            fontWeight: '500'
                          }}>
                          <i className="fas fa-map-marker-alt px-2 mr-2"></i>
                          {item.ville}
                        </span>
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm"
                          style={{ 
                            backgroundColor: 'rgba(52, 133, 182, 0.1)', 
                            color: '#2D8BBA',
                            fontWeight: '500'
                          }}>
                          {item.region}
                        </span>
                      </div>

                      {/* Description */}
                      <div className="mb-4" style={{ flex: '1 1 auto' }}>
                        <p className="text-gray-600" style={{
                          display: '-webkit-box',
                          WebkitBoxOrient: 'vertical',
                          WebkitLineClamp: 3,
                          overflow: 'hidden',
                          lineHeight: '1.6',
                          fontSize: '0.95rem'
                        }}>
                          {item.descriptionDuPoste}
                        </p>
                      </div>

                      {/* Action Button */}
                      <div className="text-center">
                        <Link href={`/offre/${item.uuid || item._id}`}>
                          <div className="inline-flex items-center justify-center px-5 py-2 transition-all duration-300"
                            style={{
                              backgroundColor: '#2D8BBA',
                              color: 'white',
                              fontSize: '0.95rem',
                              fontWeight: '500',
                              cursor: 'pointer',
                              border: '2px solid #2D8BBA',
                              boxShadow: '0 2px 4px rgba(52, 133, 182, 0.1)',
                              minWidth: '130px',
                              maxWidth: 'fit-content',
                              margin: '0 auto',
                              borderRadius: '25px'
                            }}
                            onMouseOver={(e) => {
                              e.currentTarget.style.backgroundColor = 'transparent';
                              e.currentTarget.style.color = '#2D8BBA';
                              e.currentTarget.style.transform = 'translateY(-1px)';
                            }}
                            onMouseOut={(e) => {
                              e.currentTarget.style.backgroundColor = '#2D8BBA';
                              e.currentTarget.style.color = 'white';
                              e.currentTarget.style.transform = 'translateY(0)';
                            }}>
                            <span>Voir l'offre</span>
                            <i className="fas fa-arrow-right px-2 ml-2"></i>
                          </div>
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
                            )))}
            </div>

            <div className="row justify-content-center mt-5" style={{marginBottom: "20%"}}>
              <div className="col-12">
                <nav>
                  <ul className="pagination justify-content-center">
                    <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                      <button
                        className="page-link rounded-circle"
                        onClick={() => handlePageChange(currentPage - 1)}
                        style={{
                          width: '40px',
                          height: '40px',
                          padding: '0',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <i className="fas fa-chevron-left"></i>
                      </button>
                    </li>
                    {(() => {
                      // Mobile pagination: show only 3 buttons

                      if (isMobile && totalPages > 3) {
                        let startPage, endPage;

                        if (currentPage <= 2) {
                          // Show first 3 pages
                          startPage = 1;
                          endPage = 3;
                        } else if (currentPage >= totalPages - 1) {
                          // Show last 3 pages
                          startPage = totalPages - 2;
                          endPage = totalPages;
                        } else {
                          // Show current page and one on each side
                          startPage = currentPage - 1;
                          endPage = currentPage + 1;
                        }

                        const pages = [];
                        for (let i = startPage; i <= endPage; i++) {
                          pages.push(i);
                        }

                        return pages.map((pageNum) => (
                          <li
                            key={pageNum}
                            className={`page-item ${currentPage === pageNum ? 'active' : ''}`}
                          >
                            <button
                              className="page-link rounded-circle mx-1"
                              onClick={() => handlePageChange(pageNum)}
                              style={currentPage === pageNum ?
                                {
                                  backgroundColor: '#2D8BBA',
                                  borderColor: '#2D8BBA',
                                  color: '#fff',
                                  width: '40px',
                                  height: '40px',
                                  padding: '0',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                } : {
                                  width: '40px',
                                  height: '40px',
                                  padding: '0',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }}
                            >
                              {pageNum}
                            </button>
                          </li>
                        ));
                      } else {
                        // Desktop: show all pages
                        return Array.from({ length: totalPages }).map((_, index) => (
                          <li
                            key={index}
                            className={`page-item ${currentPage === index + 1 ? 'active' : ''}`}
                          >
                            <button
                              className="page-link rounded-circle mx-1"
                              onClick={() => handlePageChange(index + 1)}
                              style={currentPage === index + 1 ?
                                {
                                  backgroundColor: '#2D8BBA',
                                  borderColor: '#2D8BBA',
                                  color: '#fff',
                                  width: '40px',
                                  height: '40px',
                                  padding: '0',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                } : {
                                  width: '40px',
                                  height: '40px',
                                  padding: '0',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }}
                            >
                              {index + 1}
                            </button>
                          </li>
                        ));
                      }
                    })()}
                    <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                      <button
                        className="page-link rounded-circle"
                        onClick={() => handlePageChange(currentPage + 1)}
                        style={{
                          width: '40px',
                          height: '40px',
                          padding: '0',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <i className="fas fa-chevron-right"></i>
                      </button>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </section>
      </Layout>
      <ToastContainer />
    </>
  );
}
