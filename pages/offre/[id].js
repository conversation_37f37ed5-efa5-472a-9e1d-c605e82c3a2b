import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import Layout from "@/components/layout/Layout";
import styles from '@/styles/JobOffer.module.css';

export default function PostDetails() {
    const router = useRouter();
    const { id } = router.query;
    const [post, setPost] = useState(null);
    const [loading, setLoading] = useState(true);
    const [isConnected, setIsConnected] = useState(false);

    useEffect(() => {
        if (id) {
            fetchPostDetails();
        }
    }, [id]);

    const fetchPostDetails = async () => {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setIsConnected(!token);
            }

            const config = {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            };

            // Check if id is a UUID (contains hyphens) or MongoDB ObjectId
            const isUUID = id && id.includes('-');
            const endpoint = isUUID
                ? `http://82.165.144.72:5000/api/posts/uuid/${id}`
                : `http://82.165.144.72:5000/api/posts/${id}`;

            const res = await fetch(endpoint, config);
            const data = await res.json();
            console.log('Post data:', data); // Log the post data to inspect its structure

            setPost(data);
            setLoading(false);
        } catch (error) {
            console.error('Failed to fetch post details:', error);
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-screen bg-gray-50">
                <div className={styles.spinner}></div>
            </div>
        );
    }

    if (!post) {
        return (
            <div className="container mx-auto px-4  mt-20" >
                <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 rounded" role="alert">
                    <p className="font-medium">Aucun message trouvé</p>
                </div>
            </div>
        );
    }

    return (
        <Layout headerStyle={2} footerStyle={3}>
            <div className={styles.wrapper} style={{marginBottom:'6%'}}>
                <div className={styles.jobCard}>
                    {/* Header Section */}
                    <div className={styles.cardHeader}>
                        <div className={styles.headerContent}>
                            <div className={styles.jobInfo}>
                                <span className={styles.contractBadge}>{post.contract}</span>
                                <h1 className={styles.jobTitle}>{post.agence}</h1>
                                <div className={styles.locationInfo}>
                                    <span><i className="fas fa-map-marker-alt"></i> {post.ville}</span>
                                    <span><i className="fas fa-map"></i> {post.region}</span>
                                </div>
                            </div>
                            <div className={styles.recruiterInfo}>
                                <div className={styles.recruiterImage}>
                                    <img 
                                        src={post.recruiterPhoto || "/images/Slider/sabrina.png"} 
                                        alt={post.recruiterName || 'Votre recruteur'} 
                                        onError={(e) => {
                                            e.target.onerror = null; 
                                            e.target.src = "/images/Slider/sabrina.png";
                                        }}
                                    />
                                </div>
                                <h3>{post.recruiterName || 'Votre recruteur'}</h3>
                                <p>Votre recruteur</p>
                            </div>
                        </div>
                    </div>

                    {/* Quick Info Cards */}
                    <div className={styles.quickInfo}>
                        <div className={styles.infoCard}>
                            <div className={styles.iconWrapper}>
                                <i className="fas fa-briefcase "></i>
                            </div>
                            <div className={styles.infoContent}>
                                <h4>Type de contrat</h4>
                                <p>{post.contract}</p>
                            </div>
                        </div>
                        <div className={styles.infoCard}>
                            <div className={styles.iconWrapper}>
                                <i className="fas fa-building"></i>
                            </div>
                            <div className={styles.infoContent}>
                                <h4>Agence</h4>
                                <p>{post.agence}</p>
                            </div>
                        </div>
                        <div className={styles.infoCard}>
                            <div className={styles.iconWrapper}>
                                <i className="fas fa-map-marked-alt"></i>
                            </div>
                            <div className={styles.infoContent}>
                                <h4>Localisation</h4>
                                <p>{post.ville}, {post.region}</p>
                            </div>
                        </div>
                    </div>

                    {/* Job Details */}
                    <div className={styles.jobDetails}>
                        <section className={styles.detailSection}>
                            <h2><i className="fas fa-info-circle"></i> Description du poste</h2>
                            <div className={styles.content}>{post.descriptionDuPoste}</div>
                        </section>

                        <section className={styles.detailSection}>
                            <h2><i className="fas fa-check-circle"></i> Conditions et avantages</h2>
                            <div className={styles.content}>{post.conditionsEtAvantages}</div>
                        </section>

                        <section className={styles.detailSection}>
                            <h2><i className="fas fa-building"></i> L'entreprise</h2>
                            <div className={styles.content}>{post.presentationDeLEntreprise}</div>
                        </section>
                    </div>

                    {/* Apply Section */}
                    <div className={styles.applySection}>
                        <h2>Intéressé(e) par cette offre ?</h2>
                        {!isConnected ? (
                            <div className={styles.applyContent}>
                                <p>Postulez dès maintenant à cette offre</p>
                                <button
                                    onClick={() => router.push(`/postuler?uuid=${id}&agence=${encodeURIComponent(post?.agence || '')}`)}
                                    className={styles.applyButton}
                                >
                                    Postuler maintenant
                                    <i className="fas fa-arrow-right"></i>
                                </button>
                            </div>
                        ) : (
                            <div className={styles.applyContent}>
                                <p>Connectez-vous pour postuler à cette offre</p>
                                <button
                                    onClick={() => router.push('/signin')}
                                    className={styles.applyButton}
                                >
                                    Se connecter pour postuler
                                    <i className="fas fa-arrow-right"></i>
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </Layout>
    );
}
