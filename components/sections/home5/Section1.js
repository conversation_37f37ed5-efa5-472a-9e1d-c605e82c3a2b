import React from 'react';
import styles from '@/styles/SecondBanner.module.css';
import FormSelect from '@/components/sections/home2/FormSelect';
import Link from 'next/link';
import { motion } from 'framer-motion';
const Section1 = () => {
    return (
        <>
            <style jsx global>{`
                @media (max-width: 768px) {
                    .mobile-text-smaller {
                        font-size: 1.8rem !important;
                        line-height: 1.3 !important;
                        margin-bottom: 15px !important;
                    }

                    .mobile-form-title-smaller {
                        font-size: 1.2rem !important;
                        margin-bottom: 15px !important;
                    }

                    .mobile-form-container {
                        top: 35% !important;
                    }

                    .mobile-search-button {
                        display: flex !important;
                        justify-content: center !important;
                        align-items: center !important;
                        width: 100% !important;
                    }

                    .mobile-discover-button {
                        font-size: 14px !important;
                        padding: 12px 24px !important;
                    }

                    /* Target search button in FormSelect component */
                    .form-select-container button,
                    .search-form button,
                    form button[type="submit"] {
                        margin: 0 auto !important;
                        display: flex !important;
                        align-items: center !important;
                        justify-content: center !important;
                        text-align: center !important;
                        width: auto !important;
                        min-width: 150px !important;
                        padding: 12px 20px !important;
                        position: relative !important;
                    }
                }
            `}</style>
            <div id="emploi" className={styles.bannerSection}>
            <div 
                className={styles.backgroundImage}
                style={{
                    backgroundImage: `linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(255,255,255,1) 1%, rgba(255,255,255,0) 40%), url("/images/home/<USER>")`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center top',
                    backgroundRepeat: 'no-repeat',
                    width: '100%',
                    height: '100%',
                    position: 'absolute',
                    top: 0,
                    left: 0
                }}
            />
            
            <div style={{
                position: 'absolute',
                top: '20%',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '100%',
                maxWidth: '800px',
                padding: '0 20px',
                textAlign: 'center',
                zIndex: 2
            }}>
                <h1 className="mobile-text-smaller" style={{
                    fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
                    fontSize: 'clamp(32px, 5vw, 48px)',
                    color: '#2D8BBA',
                    marginBottom: '20px',
                    textShadow: '0 1px 2px rgba(0,0,0,0.1)'
                }}>
                    Trouvez votre prochaine offre
                </h1>

            </div>
            
            <div className="mobile-form-container" style={{
                position: 'absolute',
                top: '40%',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '90%',
                maxWidth: '1000px',
                zIndex: 2
            }}>
                <div style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderRadius: '15px',
                    padding: '30px',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                    backdropFilter: 'blur(10px)'
                }}>
                    <h2 className="mobile-form-title-smaller" style={{
                        fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
                        fontSize: 'clamp(20px, 2.5vw, 28px)',
                        color: '#2D8BBA',
                        marginBottom: '15px',
                        textAlign: 'center'
                    }}>
                        RECHERCHE D'EMPLOI
                    </h2>
                    <FormSelect />
                    
                </div>
                <div style={{ 
                    display: 'flex', 
                    justifyContent: 'center', 
                    marginTop: '25px', 
                    width: '100%' 
                }}>
                  <Link href="/offres">
                    <motion.button
                      whileHover={{ scale: 1.05, y: -3 }}
                      whileTap={{ scale: 0.98 }}
                      className="mobile-discover-button"
                      style={{
                        width: 'fit-content',
                        background: '#2D8BBA',
                        color: '#fff',
                        border: '2px solid #2D8BBA',
                        padding: '15px 30px',
                        fontSize: '18px',
                        borderRadius: '9999px',
                        cursor: 'pointer',
                        fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                        zIndex: 10
                      }}
                    >
                      DÉCOUVRER NOS OFFRES
                    </motion.button>
                  </Link>
                </div>
            </div>
        </div>
        </>
    );
};

export default Section1;