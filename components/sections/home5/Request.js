import Link from "next/link"

export default function Request() {
    return (
        <>
            <section className="request-area request-bg" data-background="/assets/img/bg/rq_bg.png">
                <div className="container">
                    <div className="row align-items-center">
                        <div className="col-lg-5">
                            <div className="request-content tg-heading-subheading animation-style2">
                                <h2 className="title tg-element-title">SIMPLIFIER VOTRE  <br /> RECHERCHE D’EMPLOI</h2>
                            </div>
                        </div>
                        <div className="col-lg-7">
                            <div className="request-content-right">
                                
                                <div className="request-btn">
                                    <Link href="/candidats#candidature" className="custom-btn">
                                        <span className="button-text">Déposer une candidature spontanée</span>
                                        <span className="button-icon">→</span>
                                    </Link>
                                </div>
                                <style jsx>{`
                                    .custom-button {
                                        display: inline-flex;
                                        align-items: center;
                                        padding: 16px 32px;
                                        font-size: 15px;
                                        font-weight: 500;
                                        border-radius: 50px;
                                        background: #FF4B2B;
                                        color: white;
                                        border: 2px solid transparent;
                                        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                                        position: relative;
                                        overflow: hidden;
                                        text-decoration: none;
                                    }
                                    
                                    .custom-button::before {
                                        content: '';
                                        position: absolute;
                                        top: 0;
                                        left: -100%;
                                        width: 100%;
                                        height: 100%;
                                        background: linear-gradient(120deg, transparent, rgba(255,255,255,0.3), transparent);
                                        transition: all 0.6s;
                                    }
                                    
                                    .custom-button:hover {
                                        transform: scale(1.05);
                                        background: #FF6B4B;
                                        box-shadow: 0 5px 15px rgba(255, 75, 43, 0.4);
                                    }
                                    
                                    .custom-button:hover::before {
                                        left: 100%;
                                    }
                                    
                                    .button-text {
                                        margin-right: 12px;
                                        position: relative;
                                        z-index: 1;
                                    }
                                    
                                    .button-icon {
                                        font-size: 18px;
                                        transition: transform 0.3s ease;
                                        position: relative;
                                        z-index: 1;
                                    }
                                    
                                    .custom-button:hover .button-icon {
                                        transform: translateX(5px);
                                    }

                                    @media (max-width: 768px) {
                                        .custom-button {
                                            padding: 14px 28px;
                                            font-size: 14px;
                                        }
                                    }
                                `}</style>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="request-shape">
                    <img src="/assets/img/images/request_shape.png" alt="" />
                </div>
            </section>
        </>
    )
}
