import React from 'react';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';

const Section5 = () => {
  const router = useRouter();

  const handleCreateAlert = () => {
    router.push('/profile');
  };

  return (
    <>
      <style jsx global>{`
        @media (max-width: 768px) {
          .mobile-section5-container {
            flex-direction: column !important;
            height: auto !important;
            min-height: auto !important;
            padding: 20px 0 !important;
          }

          .mobile-left-content {
            flex: 1 !important;
            width: 100% !important;
            padding: 20px 20px !important;
            text-align: center !important;
          }

          .mobile-logo-container {
            margin-bottom: 20px !important;
            padding: 4px 15px !important;
          }

          .mobile-logo-text {
            font-size: 1.2rem !important;
          }

          .mobile-main-heading {
            font-size: 1.8rem !important;
            margin-bottom: 20px !important;
            line-height: 1.3 !important;
          }

          .mobile-description {
            font-size: 0.9rem !important;
            margin-bottom: 30px !important;
            line-height: 1.4 !important;
            max-width: 300px !important;
            margin-left: auto !important;
            margin-right: auto !important;
          }

          .mobile-button {
            padding: 12px 25px !important;
            font-size: 0.85rem !important;
          }

          .mobile-button-icon {
            width: 30px !important;
            height: 30px !important;
            font-size: 30px !important;
          }

          .mobile-right-image {
            display: none !important;
          }
        }
      `}</style>
      <section id="avantage-alerte" className="mobile-section5-container" style={{
      backgroundColor: '#2D8BBA',
      display: 'flex',
      color: 'white',
      height: '100vh',
      position: 'relative'
    }}>
      {/* Left Content (2/3) */}
      <motion.div
        initial={{ opacity: 0, x: -50 }}
        whileInView={{ opacity: 1, x: 0 }}
        viewport={{ once: false }}
        transition={{ duration: 0.8 }}
        className="mobile-left-content"
        style={{
          flex: '3',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          textAlign: 'center',
          paddingRight: '40px',
          paddingLeft: '10%',
          padding: '60px'
        }}
      >
        {/* Logo */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: false }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="mobile-logo-container"
          style={{
            marginBottom: '40px',
            backgroundColor: 'white',
            padding: '6px 25px',
            borderRadius: '50px',
            display: 'flex',
            alignItems: 'center',
            gap: '10px'
          }}
        >
          <motion.img 
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: false }}
            transition={{ duration: 0.5, delay: 0.5 }}
            src="/images/temporels/cercle.png"
            alt="Alerte Emploi Logo"
            style={{
              height: '50px'
            }}
          />
          <motion.span
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: false }}
            transition={{ duration: 0.5, delay: 0.7 }}
            className="mobile-logo-text"
            style={{
              color: '#2D8BBA',
              fontSize: '2rem',
              fontWeight: 'bold',
              fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif'
            }}
          >
            ALERTE EMPLOI
          </motion.span>
        </motion.div>

        {/* Main Heading */}
        <motion.h2
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: false }}
          transition={{ duration: 0.7, delay: 0.9 }}
          className="mobile-main-heading"
          style={{
            fontSize: '3.5rem',
            fontWeight: 'bold',
            marginBottom: '30px',
            textTransform: 'uppercase',
            lineHeight: '1.2',
            fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
            color: 'white'
          }}
        >
          Chaque opportunité<br />
           commence par une alerte
        </motion.h2>

        {/* Description Text */}
        <motion.p
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: false }}
          transition={{ duration: 0.5, delay: 1.1 }}
          className="mobile-description"
          style={{
            fontSize: '1.1rem',
            marginBottom: '40px',
            lineHeight: '1.5',
            fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif',
            color: 'white'
          }}
        >
         Activez votre alerte emploi personnalisée et recevez <br/> en temps réel les offres qui vous correspondent.
        </motion.p>

        {/* Button */}
        <motion.button
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: false }}
          transition={{ duration: 0.5, delay: 1.3 }}
          whileHover={{ scale: 1.05, y: -5 }}
          whileTap={{ scale: 0.98 }}
          onClick={handleCreateAlert}
          className="mobile-button"
          style={{
            backgroundColor: 'white',
            color: '#2D8BBA',
            padding: '15px 40px',
            border: 'none',
            borderRadius: '40px',
            fontSize: '1rem',
            fontWeight: 'bold',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            transition: 'all 0.3s ease',
          }}
        >
          JE CRÉE MON ALERTE
          <motion.span
            whileHover={{ rotate: 90 }}
            transition={{ duration: 0.3 }}
            className="mobile-button-icon"
            style={{
              marginLeft: '10px',
              backgroundColor: '#8b8a89',
              color: 'white',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '40px'
            }}
          >&gt;</motion.span>
        </motion.button>
      </motion.div>

      {/* Right Image (1/3) */}
      <motion.div
        initial={{ opacity: 0, x: 50 }}
        whileInView={{ opacity: 1, x: 0 }}
        viewport={{ once: false }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className="mobile-right-image"
        style={{
          flex: '2',
          position: 'relative',
        }}
      >
        <Image
          src="/images/temporels/alerte.png"
          alt="Alerte Emploi"
          layout="fill"
          objectFit="cover"
          priority
          style={{
            width: '100%',
            height: '100%'
          }}
        />
      </motion.div>
    </section>
    </>
  );
};

export default Section5;