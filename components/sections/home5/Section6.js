import React from 'react';
import Link from 'next/link';

const Section6 = () => {
  const contactIcons = [
    {
      icon: (
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
          <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72c.127.96.362 1.903.7 2.81a2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45c.907.338 1.85.573 2.81.7A2 2 0 0 1 22 16.92z"/>
        </svg>
      ),
      label: 'Phone'
    },
    {
      icon: (
        <span style={{ color: '#fff', fontSize: 'clamp(48px, 6vw, 64px)' }}>@</span>
      ),
      label: 'Email'
    },
    {
      icon: (
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <rect x="5" y="2" width="14" height="20" rx="3" ry="3"/>
        <rect x="8" y="5" width="8" height="10" rx="1"/>
      
        <circle cx="12" cy="19" r="0.8" fill="white"/>
      </svg>
      
      ),
      label: 'Mobile'
    },
    {
      icon: (
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
          <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
          <path d="M22 6l-10 7L2 6"/>
        </svg>
      ),
      label: 'Message'
    }
  ];

  return (
    <section style={{
      backgroundImage: 'url("/assets/img/images/social-hd.png")',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      minHeight: '60vh',
      position: 'relative',
      display: 'flex',
      alignItems: 'center',
      padding: 'clamp(32px, 5vw, 64px) 0'
    }}>
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)'
      }}></div>
      
      <div className="container" style={{
        position: 'relative',
        zIndex: 1,
        maxWidth: '1200px',
        margin: '0 auto',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '48px'
      }}>
        {/* Icons Container */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          flexWrap: 'wrap',
          gap: 'clamp(48px, 8vw, 96px)',
          width: '100%',
          maxWidth: '1200px',
          margin: '0 auto',
          padding: 'clamp(32px, 5vw, 48px) 0'
        }}>
          {contactIcons.map((item, index) => (
            <div key={index} style={{
              width: 'clamp(120px, 15vw, 160px)',
              height: 'clamp(120px, 15vw, 160px)',
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(15px)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: 'clamp(48px, 6vw, 64px)',
              color: '#fff',
              boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
              transition: 'all 0.3s ease',
              cursor: 'pointer'
            }}>
              {item.icon}
            </div>
          ))}
        </div>

        {/* Contact Button */}
        <Link href="/contact">
          <button style={{
            background: '#2D8BBA',
            color: '#fff',
            padding: '15px 40px',
            border: '2px solid #2D8BBA',
            borderRadius: '9999px',
            fontSize: 'clamp(16px, 2vw, 20px)',
            cursor: 'pointer',
            fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s ease',
            textAlign: 'center',
            maxWidth: '800px',
            width: 'clamp(300px, 80vw, 800px)',
            marginTop: 'clamp(32px, 5vw, 48px)'
          }}>
            VOUS SOUHAITEZ CONTACTER NOTRE ÉQUIPE DE RECRUTEURS ?
          </button>
        </Link>
      </div>
    </section>
  );
};

export default Section6;
