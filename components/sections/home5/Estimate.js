import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import styles from "@/styles/Estimate.module.css";

export default function Estimate() {
    const [activeIndex, setActiveIndex] = useState(0);
    const [regions, setRegions] = useState([]);
    const [posts, setPosts] = useState([]);
    const [totalPages, setTotalPages] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [loading, setLoading] = useState(false);
    const [searchKeyword, setSearchKeyword] = useState("");
    const contactRef = useRef(null);

    const handleOnClick = (index) => {
        setActiveIndex(index);
        fetchPosts(index, searchKeyword);
    };

    const handleSearch = (event) => {
        event.preventDefault();
        fetchPosts(activeIndex, searchKeyword);
    };
  
    const fetchRegions = async () => {
        try {
            const res = await fetch('http://82.165.144.72:5000/api/postss/regions');
            const data = await res.json();
            setRegions(data);
            if (data.length > 0) {
                setActiveIndex(1);
            }
        } catch (error) {
            console.error("Failed to fetch regions:", error);
        }
    };

    const fetchPosts = async (regionIndex, keyword) => {
        setLoading(true);
        try {
            const region = regions[regionIndex - 1];
            const query = new URLSearchParams({ region, page: currentPage, limit: 10, keyword }).toString();
            const res = await fetch(`http://82.165.144.72:5000/api/postss/by-region?${query}`);
            const data = await res.json();
            setPosts(data.posts);
            setTotalPages(data.totalPages);
            setLoading(false);
        } catch (error) {
            console.error("Failed to fetch posts:", error);
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchRegions();
    }, []);
    
    useEffect(() => {
        if (regions.length > 0) {
            fetchPosts(activeIndex, searchKeyword);
        }
    }, [activeIndex, currentPage, searchKeyword]);

    const scrollToContact = (company) => {
        localStorage.setItem("selectedCompany", company); 
        if (contactRef.current) {
            contactRef.current.scrollIntoView({ behavior: "smooth" });
        }

    };

    return (
        <section id="offres" className={styles.estimateArea}>
            <div className="container">
                <div className={styles.sectionTitle}>
                    <span className={styles.subTitle}>TROUVER LE BON</span>
                    <h2>OFFRES D'EMPLOI</h2>
                </div>

                <div className="text-center mb-4">
                    <Link href="/offres" className={styles.viewButton}>
                        VOIR TOUTES NOS OFFRES
                    </Link>
                </div>

                <div className="row align-items-center">
                    <div className="col-lg-8">
                        <div className={styles.searchSection}>
                            <div className="row">
                                <div className="col-md-4 mb-3">
                                    <div className={styles.selectWrapper}>
                                        <select 
                                            className={styles.selectInput}
                                            value={activeIndex} 
                                            onChange={(e) => handleOnClick(parseInt(e.target.value))}
                                        >
                                            {regions.length > 0 ? (
                                                regions.map((region, index) => (
                                                    <option key={index} value={index + 1}>
                                                        {region}
                                                    </option>
                                                ))
                                            ) : (
                                                <option disabled>No regions available</option>
                                            )}
                                        </select>
                                        <div className={styles.selectIcon}>
                                            <i className="fas fa-chevron-down"></i>
                                        </div>
                                    </div>
                                </div>
                                <div className="col-md-8">
                                    <form onSubmit={handleSearch} className={styles.searchForm}>
                                        <div className={styles.searchInputWrapper}>
                                            <i className="fas fa-search"></i>
                                            <input 
                                                type="text" 
                                                placeholder="Recherche par mot-clé..." 
                                                value={searchKeyword} 
                                                onChange={(e) => setSearchKeyword(e.target.value)} 
                                                className={styles.searchInput}
                                            />
                                        </div>
                                        <button type="submit" className={styles.searchButton}>
                                            <span>Recherche</span>
                                            <i className="fas fa-arrow-right"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div className={styles.tableContainer}>
                            {loading ? (
                                <div className={styles.loadingSpinner} />
                            ) : (
                                <table className={styles.table}>
                                    <thead>
                                        <tr>
                                            <th>Titre</th>
                                            <th>Description</th>
                                            <th>Conditions</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {Array.isArray(posts) && posts.length > 0 ? (
                                            posts.map((post, postIndex) => (
                                                <tr key={postIndex}>
                                                    <td>{post.conditionsEtAvantages.length > 50 ? 
                                                        post.conditionsEtAvantages.substring(0, 50) + '...' : 
                                                        post.conditionsEtAvantages}
                                                    </td>
                                                    <td>{post.descriptionDuPoste.length > 50 ? 
                                                        post.descriptionDuPoste.substring(0, 50) + '...' : 
                                                        post.descriptionDuPoste}
                                                    </td>
                                                    <td>{post.presentationDeLEntreprise.length > 50 ? 
                                                        post.presentationDeLEntreprise.substring(0, 50) + '...' : 
                                                        post.presentationDeLEntreprise}
                                                    </td>
                                                    <td>
                                                        <Link 
                                                            href={`/${post._id}`} 
                                                            className={styles.viewButton}>
                                                            Voir détails
                                                        </Link>
                                                    </td>
                                                </tr>
                                            ))
                                        ) : (
                                            <tr>
                                                <td colSpan="4" className="text-center py-4">
                                                    Aucune offre trouvée
                                                </td>
                                            </tr>
                                        )}
                                    </tbody>
                                </table>
                            )}
                        </div>
                    </div>
                    <div className="col-lg-4">
                        <div className={styles.estimateImg}>
                            <img src="/assets/img/images/estimate_img.png" alt="Estimate" className={styles.sideImage} />
                        </div>
                    </div>
                </div>
            </div>
            <div className={styles.estimateShape}>
                <img 
                    src="/assets/img/services/h4_services_shape.png" 
                    alt="Shape" 
                    data-aos="fade-left" 
                    data-aos-delay={200} 
                />
            </div>
        </section>
    );
}
