import Link from "next/link"
import Slider from "react-slick"

const settings = {
    autoplay: true,
    autoplaySpeed: 5000,
    dots: true,
    fade: true,
    arrows: false,
    responsive: [
        { breakpoint: 767, settings: { dots: true, arrows: false } }
    ]
}

export default function MainSlider() {
    return (
        <>
            <section className="slider-area">
                <Slider {...settings} className="slider-active">
                    <div className="single-slider">
                        <div className="slider-wrapper" style={{
                            backgroundImage: 'linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url("/images/candidat/home.jpg")',
                            backgroundSize: 'cover',
                            backgroundPosition: 'center',
                            minHeight: '100vh',
                            display: 'flex',
                            alignItems: 'center'
                        }}>
                            <div className="container">
                                <div className="row" >
                                    <div className="col-lg-7" >
                                        <div className="slider-content bg-white bg-opacity-10 p-10 rounded-lg" style={{ padding: '2rem 1rem' }}>
                                            <span className="sub-title">AVEC CONFIANCE</span>
                                            <h2 className="title">Trouvez le poste qui vous correspond.</h2>
                                            <p className="description">
                                                Atlantis Conseil valorise le rôle stratégique des ressources humaines dans la croissance des entreprises en offrant des solutions adaptées aux besoins spécifiques du marché.
                                            </p>
                                            <Link href="/candidats#offres" className="custom-btn">
                                                NOS OFFRES
                                            </Link>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </Slider>
            </section>

            <style jsx>{`
                .slider-area {
                    position: relative;
                    overflow: hidden;
                }
                .slider-wrapper {
                    position: relative;
                }
                .slider-content {
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    backdrop-filter: blur(10px);
                }
                .sub-title {
                    display: inline-block;
                    font-size: 1rem;
                    font-weight: 600;
                    color: #3B82F6;
                    margin-bottom: 1.5rem;
                    letter-spacing: 1px;
                }
                .title {
                    font-size: 2.75rem;
                    font-weight: 700;
                    color: white;
                    margin-bottom: 1.5rem;
                    line-height: 1.2;
                }
                .description {
                    font-size: 1.125rem;
                    color: #E5E7EB;
                    margin-bottom: 2rem;
                    line-height: 1.7;
                    max-width: 600px;
                }
                .custom-btn {
                    display: inline-block;
                    background-color: #3B82F6;
                    color: white;
                    padding: 1rem 2rem;
                    border-radius: 0.5rem;
                    font-weight: 600;
                    text-decoration: none;
                    transition: background-color 0.3s;
                }
                .custom-btn:hover {
                    background-color: #2563EB;
                }
                :global(.slick-dots) {
                    bottom: 30px;
                }
                :global(.slick-dots li button:before) {
                    color: white;
                    opacity: 0.5;
                    font-size: 12px;
                }
                :global(.slick-dots li.slick-active button:before) {
                    color: white;
                    opacity: 1;
                }
                @media (max-width: 768px) {
                    .title {
                        font-size: 2rem;
                    }
                    .description {
                        font-size: 1rem;
                    }
                }
            `}</style>
        </>
    )
}
