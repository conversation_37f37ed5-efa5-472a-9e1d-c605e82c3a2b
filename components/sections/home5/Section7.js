import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faGlobe } from '@fortawesome/free-solid-svg-icons';
import { motion } from 'framer-motion';

const Section7 = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isMobile, setIsMobile] = useState(false);

  const newsCards = [
    { id: 2, title: 'LES RÉMUNÉRATIONS ', subtitle: 'INFOS CCI', image: '/images/temporels/first.png' },
    { id: 1, title: 'UNE ENTREPRISE QUI DÉCHIRE EN PACA', subtitle: 'RESTAURANT...', image: '/images/temporels/second.png' },
    { id: 3, title: 'STRATÉGIE INTERNE', subtitle: 'RSE...', image: '/images/temporels/third.png' }
  ];

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Auto-slide functionality for mobile
  useEffect(() => {
    if (isMobile) {
      const interval = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % 3); // 3 slides
      }, 3000); // Change slide every 3 seconds

      return () => clearInterval(interval);
    }
  }, [isMobile]);

  return (
    <>
      <style jsx global>{`
        @media (max-width: 768px) {
          .mobile-section7-title {
            font-size: 1.8rem !important;
            margin-bottom: 25px !important;
            line-height: 1.3 !important;
          }

          .mobile-section7-container {
            padding: 30px 0 !important;
          }

          .mobile-slider-container {
            position: relative !important;
            overflow: hidden !important;
            width: 100% !important;
            height: 400px !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin-top: -30px !important;
          }

          .mobile-slide {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            opacity: 0 !important;
            transition: opacity 0.5s ease !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
          }

          .mobile-slide.active {
            opacity: 1 !important;
          }

          .mobile-slide-dots {
            display: flex !important;
            justify-content: center !important;
            gap: 8px !important;
            margin-top: 20px !important;
          }

          .mobile-slide-dot {
            width: 10px !important;
            height: 10px !important;
            border-radius: 50% !important;
            background-color: rgba(45, 139, 186, 0.3) !important;
            cursor: pointer !important;
            transition: background-color 0.3s ease !important;
          }

          .mobile-slide-dot.active {
            background-color: #2D8BBA !important;
          }

          .mobile-cards-container {
            display: none !important;
          }
        }
      `}</style>
      <section id="actions" className="mobile-section7-container" style={{
      backgroundColor: '#fff',
      padding: '60px 0',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      marginBottom: '100px'
    }}>
      <motion.h2
        initial={{ opacity: 0, y: -30 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: false }}
        transition={{ duration: 0.8 }}
        className="mobile-section7-title"
        style={{
          color: '#2D8BBA',
          fontSize: '2.5rem',
          fontWeight: '700',
          marginBottom: '40px',
          textAlign: 'center',
          fontSize: '4.5rem',
          fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
        }}
      >
        Les avantages de l'alerte emploi
      </motion.h2>

      {/* Cards Container */}
      {isMobile ? (
        <div className="mobile-slider-container">
          {newsCards.map((card, index) => (
            <div key={card.id} className={`mobile-slide ${index === currentSlide ? 'active' : ''}`}>
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: false }}
                transition={{ duration: 0.8 }}
                whileHover={{ y: -10, boxShadow: '0 10px 20px rgba(0, 0, 0, 0.15)' }}
                style={{
                  position: 'relative',
                  width: '300px',
                  height: '300px',
                  backgroundColor: '#fff',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                }}
              >
                <div style={{
                  position: 'relative',
                  width: '100%',
                  height: '100%'
                }}>
                  <Image
                    src={card.image}
                    alt={card.title}
                    layout="fill"
                    objectFit="cover"
                  />
                </div>
              </motion.div>
            </div>
          ))}

          {/* Slider Dots */}
          <div className="mobile-slide-dots">
            {newsCards.map((_, index) => (
              <div
                key={index}
                className={`mobile-slide-dot ${index === currentSlide ? 'active' : ''}`}
                onClick={() => setCurrentSlide(index)}
              />
            ))}
          </div>
        </div>
      ) : (
        <div className="mobile-cards-container" style={{
          display: 'flex',
          justifyContent: 'center',
          gap: '0',
          width: '100%',
          maxWidth: '1200px',
          marginBottom: '40px'
        }}>
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: false }}
          transition={{ duration: 0.8, delay: 0.2 }}
          whileHover={{ y: -10, boxShadow: '0 10px 20px rgba(0, 0, 0, 0.15)' }}
          style={{
            position: 'relative',
            width: '400px',
            height: '400px',
            backgroundColor: '#fff',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
          }}
        >
          {/* Image Container */}
          <div style={{
            position: 'relative',
            width: '100%',
            height: '100%'
          }}>
            <Image
              src="/images/temporels/first.png"
              alt="News"
              layout="fill"
              objectFit="cover"
            />
          </div>
        </motion.div>
        
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: false }}
          transition={{ duration: 0.8, delay: 0.4 }}
          whileHover={{ y: -10, boxShadow: '0 10px 20px rgba(0, 0, 0, 0.15)' }}
          style={{
            position: 'relative',
            width: '400px',
            height: '400px',
            backgroundColor: '#fff',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            marginLeft: '20px',
            marginRight: '20px'
          }}
        >
          {/* Image Container */}
          <div style={{
            position: 'relative',
            width: '100%',
            height: '100%'
          }}>
            <Image
              src="/images/temporels/second.png"
              alt="News"
              layout="fill"
              objectFit="cover"
            />
          </div>
        </motion.div>
        
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: false }}
          transition={{ duration: 0.8, delay: 0.6 }}
          whileHover={{ y: -10, boxShadow: '0 10px 20px rgba(0, 0, 0, 0.15)' }}
          style={{
            position: 'relative',
            width: '400px',
            height: '400px',
            backgroundColor: '#fff',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
          }}
        >
          {/* Image Container */}
          <div style={{
            position: 'relative',
            width: '100%',
            height: '100%'
          }}>
            <Image
              src="/images/temporels/third.png"
              alt="News"
              layout="fill"
              objectFit="cover"
            />
          </div>
        </motion.div>
        </div>
      )}

      {/* Navigation Button */}
      {/* <button style={{
        backgroundColor: '#2D8BBA',
        color: '#fff',
        padding: '15px 30px',
        border: 'none',
        borderRadius: '30px',
        fontSize: '1.1rem',
        fontWeight: '600',
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        gap: '10px',
        transition: 'all 0.3s ease',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
        ':hover': {
          backgroundColor: '#2d70a3',
          transform: 'translateY(-2px)'
        }
      }}>
        VOIR LES ACTUS...
        <FontAwesomeIcon icon={faGlobe} style={{ fontSize: '1.1rem' }} />
      </button> */}
    </section>
    </>
  );
};

export default Section7;