import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { useRouter } from 'next/router'; // Ajoute ceci
const Section3 = () => {
  const router = useRouter(); // Ajoute ceci
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Auto-slide functionality for mobile
  useEffect(() => {
    if (isMobile) {
      const interval = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % 2); // 2 slides
      }, 3000); // Change slide every 3 seconds

      return () => clearInterval(interval);
    }
  }, [isMobile]);

  return (
    <>
      <style jsx global>{`
        @media (max-width: 768px) {
          .mobile-hide-accueil2 {
            display: none !important;
          }

          .mobile-full-width {
            width: 100% !important;
            padding-right: 0 !important;
          }

          .mobile-flex-container {
            flex-direction: column !important;
          }

          .mobile-slider-container {
            position: relative !important;
            overflow: hidden !important;
            width: 100% !important;
            height: 300px !important;
          }

          .mobile-slide {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            opacity: 0 !important;
            transition: opacity 0.5s ease !important;
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
          }

          .mobile-slide.active {
            opacity: 1 !important;
          }

          .mobile-slide-dots {
            display: flex !important;
            justify-content: center !important;
            gap: 8px !important;
            margin-top: 20px !important;
          }

          .mobile-slide-dot {
            width: 10px !important;
            height: 10px !important;
            border-radius: 50% !important;
            background-color: rgba(45, 139, 186, 0.3) !important;
            cursor: pointer !important;
            transition: background-color 0.3s ease !important;
          }

          .mobile-slide-dot.active {
            background-color: #2D8BBA !important;
          }
        }
      `}</style>
      <section id="cv" style={{ background: '#fff', position: 'relative', padding: '64px 0' }}>
      <div className="container mx-auto px-4" style={{ maxWidth: '1200px', position: 'relative' }}>
        <div className="mobile-flex-container" style={{ display: 'flex', position: 'relative' }}>
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: false }}
            transition={{ duration: 0.8 }}
            className="mobile-full-width"
            style={{ width: 'calc(100% - 250px)', paddingRight: '32px' }}
          >
            <div>
              <motion.h2 
                initial={{ opacity: 0, y: -20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: false }}
                transition={{ duration: 0.6, delay: 0.2 }}
                style={{
                  color: '#2D8BBA',
                  fontSize: '48px',
                  marginBottom: '32px',
                  lineHeight: '1.2',
                  maxWidth: '540px',
                  fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif'
                }}
              >
                Déposez votre CV
              </motion.h2>
              <motion.p 
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: false }}
                transition={{ duration: 0.6, delay: 0.4 }}
                style={{
                  fontSize: '16px',
                  lineHeight: '1.8',
                  color: '#666',
                  maxWidth: '540px',
                  marginBottom: '32px',
                  fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
                }}
              >
                <strong style={{ color: '#2D8BBA', marginBottom: '16px', display: 'block' }}>
                  3 bonnes raisons de déposer votre CV sur notre site :
                </strong>
                <span style={{ display: 'block', marginBottom: '8px' }}>
                  • <strong>Soyez visible</strong>, avant les autres
                </span>
                <span style={{ display: 'block', marginBottom: '8px' }}>
                  • <strong>Attirez l'attention des recruteurs</strong>  avant la diffusion des offres. Faites partie des premiers profils repérés. 
Multipliez vos chances
                </span>
                <span style={{ display: 'block' }}>
                  • <strong>Multipliez vos chances</strong> - Déposez votre CV dans notre CVthèque et ouvrez la porte à plus d'opportunités professionnelles. 
                </span>
              </motion.p>
              <motion.div 
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: false }}
                transition={{ duration: 0.6, delay: 0.6 }}
                style={{
                  color: '#2D8BBA',
                  fontSize: '28px',
                  marginBottom: '32px',
                  lineHeight: '1.2',
                  maxWidth: '540px',
                  fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif'
                }}
              >
              <Link href="profile/cv-library">
                <motion.button 
                  whileHover={{ scale: 1.05, y: -3 }}
                  whileTap={{ scale: 0.98 }}
                  style={{
                    width: 'fit-content',
                    bottom: '20px',
                    right : '30%',
                    background: '#2D8BBA',
                    color: '#fff',
                    border: '2px solid #2D8BBA',
                    padding: '15px 10px',
                    fontSize: '18px',
                    borderRadius: '9999px',
                    cursor: 'pointer',
                    fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                    zIndex: 10
                  }}
                >
                  Déposer votre CV
                </motion.button>
              </Link>
             
              </motion.div>
            </div>

            {/* Images with Text */}
            {isMobile ? (
              <div className="mobile-slider-container">
                {/* Slide 1 - CV Image */}
                <div className={`mobile-slide ${currentSlide === 0 ? 'active' : ''}`}>
                  <motion.div
                    whileHover={{ scale: 1.03 }}
                    transition={{ duration: 0.3 }}
                    style={{
                      position: 'relative',
                      height: '200px',
                      width: '300px',
                      borderRadius: '20px',
                      overflow: 'hidden',
                      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                      marginBottom: '16px'
                    }}
                  >
                    <Image
                      src="/assets/img/cv-ph.png"
                      alt="CV sur un bureau"
                      layout="fill"
                      objectFit="cover"
                      quality={100}
                    />
                  </motion.div>
                  <motion.p
                    style={{
                      fontSize: '14px',
                      lineHeight: '1.8',
                      color: '#666',
                      maxWidth: '280px',
                      margin: '0 auto',
                      textAlign: 'center',
                      fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
                    }}
                  >
                    Le CV parfait pour décrocher l'emploi de ses rêves doit pouvoir parler pour vous.
                  </motion.p>
                </div>

                {/* Slide 2 - Lightbulb Image */}
                <div className={`mobile-slide ${currentSlide === 1 ? 'active' : ''}`}>
                  <motion.div
                    whileHover={{ scale: 1.03 }}
                    transition={{ duration: 0.3 }}
                    style={{
                      position: 'relative',
                      height: '200px',
                      width: '300px',
                      borderRadius: '20px',
                      overflow: 'hidden',
                      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                      marginBottom: '16px'
                    }}
                  >
                    <Image
                      src="/assets/img/led.png"
                      alt="Ampoule et mots-clés"
                      layout="fill"
                      objectFit="cover"
                      quality={100}
                    />
                  </motion.div>
                  <motion.p
                    style={{
                      fontSize: '14px',
                      lineHeight: '1.8',
                      color: '#666',
                      maxWidth: '280px',
                      margin: '0 auto',
                      textAlign: 'center',
                      fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
                    }}
                  >
                    4 exemples d'accroche pour son CV
                    Votre CV, c'est votre vitrine professionnelle. Pas question de passer à côté. Il doit être parfait sur la forme et le fond pour vous aider à décrocher l'emploi de vos rêves.
                  </motion.p>
                </div>

                {/* Slider Dots */}
                <div className="mobile-slide-dots">
                  {[0, 1].map((index) => (
                    <div
                      key={index}
                      className={`mobile-slide-dot ${index === currentSlide ? 'active' : ''}`}
                      onClick={() => setCurrentSlide(index)}
                    />
                  ))}
                </div>
              </div>
            ) : (
              <div style={{ display: 'flex', gap: '32px' }}>
                <motion.div
                  initial={{ opacity: 0, y: 40 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: false }}
                  transition={{ duration: 0.7, delay: 0.8 }}
                  style={{ width: '350px' }}
                >
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                    <motion.div
                      whileHover={{ scale: 1.03 }}
                      transition={{ duration: 0.3 }}
                      style={{
                        position: 'relative',
                        height: '200px',
                        width: '350px',
                        borderRadius: '20px',
                        overflow: 'hidden',
                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
                      }}
                    >
                      <Image
                        src="/assets/img/cv-ph.png"
                        alt="CV sur un bureau"
                        layout="fill"
                        objectFit="cover"
                        quality={100}
                      />
                    </motion.div>
                    <motion.div
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      viewport={{ once: false }}
                      transition={{ duration: 0.6, delay: 1.0 }}
                    >
                       <motion.p
                    style={{
                      fontSize: '14px',
                      lineHeight: '1.8',
                      color: '#666',
                      maxWidth: '280px',
                      margin: '0 auto',
                      textAlign: 'center',
                      fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
                    }}
                  >
                    <span style={{ display: 'block', marginBottom: '2px' }}>
                      Le CV parfait pour décrocher l'emploi de ses rêves doit pouvoir parler de vous.

                    </span>
                    <span style={{ display: 'block' }}>
                      Nos conseils et exemples vous aident à réaliser le meilleur CV.
                    </span>
                  </motion.p>
                    </motion.div>
                  </div>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, y: 40 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: false }}
                  transition={{ duration: 0.7, delay: 1.0 }}
                  style={{ width: '350px' }}
                >
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                    <motion.div
                      whileHover={{ scale: 1.03 }}
                      transition={{ duration: 0.3 }}
                      style={{
                        position: 'relative',
                        height: '200px',
                        width: '350px',
                        borderRadius: '20px',
                        overflow: 'hidden',
                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
                      }}
                    >
                      <Image
                        src="/assets/img/led.png"
                        alt="Ampoule et mots-clés"
                        layout="fill"
                        objectFit="cover"
                        quality={100}
                      />
                    </motion.div>
                    <motion.div
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      viewport={{ once: false }}
                      transition={{ duration: 0.6, delay: 1.2 }}
                    >
                      <motion.p
                        style={{
                          fontSize: '14px',
                          lineHeight: '1.8',
                          color: '#666',
                          maxWidth: '300px',
                          margin: '0 auto',
                          fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
                        }}
                      >
                       
                        Votre CV, c'est votre vitrine professionnelle, pas question de passer à côté, il doit être parfait sur la forme et le fond pour vous aider à décrocher l'emploi de vos rêves.
                      </motion.p>
                    </motion.div>
                  </div>
                </motion.div>
              </div>
            )}
          </motion.div>

          {/* Right Column */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: false }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="mobile-hide-accueil2"
            style={{ width: '350px', position: 'relative' }}
          >
            <div style={{ height: '500px', position: 'relative' }}>
              <motion.div 
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: false }}
                transition={{ duration: 1, delay: 0.8 }}
                style={{
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  width: '350px',
                  height: '650px',
                  overflow: 'hidden',
                  boxShadow: '-2px 0 4px rgba(0, 0, 0, 0.1)'
                }}
              >
                <Image
                  src="/assets/img/journal.png"
                  alt="Accueil 2"
                  layout="fill"
                  objectFit="cover"
                  quality={100}
                />
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
    </>
  );
};

export default Section3;