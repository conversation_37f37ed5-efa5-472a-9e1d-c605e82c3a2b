
  const Step1 =  ({ handleInputChange, handleNextStep, errors = {}, user, handleInputChangeText }) => {
    // return (
    //   <div>
    //     <h3>Étape 1: Informations personnelles</h3>
    //     <div className="form-grp">
    //       <label htmlFor="titleSelect">Civilité</label>
    //       <select
    //         id="titleSelect"
    //         className="form-select"
    //         name="title"
    //         onChange={(event) => {
    //           handleInputChangeText(event);
    //           handleInputChange(event);
             
    //         }}
           
    //         value={user?.title || ''}
            
    //       >
    //         <option value="">Sélectionnez...</option>
    //         <option value="Mme">Mme</option>
    //         <option value="M.">M.</option>
    //       </select>
       
    //     </div>
    //     <div className="form-grp">
    //       <label htmlFor="lastName">Nom</label>
    //       <input
    //         type="text"
    //         id="lastName"
    //         className="form-select"
    //         name="lastName"
    //         value={user?.lastName || ''}
    //         onChange={handleInputChangeText}
    //         required
    //       />
    //       {errors.lastName && <span className="error-message">{errors.lastName}</span>}
    //     </div>
    //     <div className="form-grp">
    //       <label htmlFor="firstName">Prénom</label>
    //       <input
    //         type="text"
    //         id="firstName"
    //         className="form-select"
    //         name="firstName"
    //         placeholder="Prénom *"
    //         value={user?.firstName || ''}
    //         onChange={handleInputChange}
    //         required
    //       />
    //       {errors.firstName && <span className="error-message">{errors.firstName}</span>}
    //     </div>
    //     <div className="form-grp">
    //       <label htmlFor="phone">Portable</label>
    //       <input
    //         type="tel"
    //         id="phone"
    //         name="phone"
    //         className="form-select"
    //         placeholder="Portable *"
    //         value={user?.telephone || ''}
    //         onChange={handleInputChange}
    //         required
    //       />
    //       {errors.phone && <span className="error-message">{errors.phone}</span>}
    //     </div>
    //     <div className="form-grp">
    //       <label htmlFor="email">Email</label>
    //       <input
    //         type="email"
    //         id="email"
    //         name="email"
    //         className="form-select"
    //         placeholder="Email *"
    //         value={user?.email || ''}
    //         onChange={handleInputChange}
    //         required
    //       />
    //       {errors.email && <span className="error-message">{errors.email}</span>}
    //     </div>
    //     <div className="form-grp">
    //       <label htmlFor="comments">Comments</label>
    //       <textarea
    //         id="comments"
    //         name="comments"
    //         className="form-select"
    //         placeholder="Comments"
    //         onChange={handleInputChange}
    //       />
    //     </div>
    //     <button type="button" style={{ width: 'auto', marginBottom: '5px' }} onClick={handleNextStep}>
    //       Suivant
    //     </button>
    //   </div>
    // );
  };
  
  export default Step1;
  
  const Step2 = ({ education, handleEducationChange, addEducation,goToStep  }) => (
    <div>
      <h3>Étape 2: Éducation</h3>
      {education.map((edu, index) => (
        <div key={index} className="form-grp">
          <label htmlFor={`degree-${index}`}>Degré</label>
          <input
            type="text"
            id={`degree-${index}`}
            name="degree"
            value={edu.degree}
            placeholder="Degré"
            onChange={(e) => handleEducationChange(index, e)}
          />
          <label htmlFor={`institution-${index}`}>Institution</label>
          <input
            type="text"
            id={`institution-${index}`}
            name="institution"
            value={edu.institution}
            placeholder="Institution"
            onChange={(e) => handleEducationChange(index, e)}
          />
          <label htmlFor={`year-${index}`}>Année</label>
          <input
            type="text"
            id={`year-${index}`}
            name="year"
            value={edu.year}
            placeholder="Année"
            onChange={(e) => handleEducationChange(index, e)}
          />
        </div>
      ))}
      <button type="button" style={{width:'auto', marginRight:'5%'}}  onClick={addEducation}>Ajouter une autre éducation</button>
      <button type="button" style={{width:'auto', marginRight:'5px'}}  onClick={() => goToStep(1)}>précédent</button>
      <button type="button" style={{width:'auto'}}  onClick={() => goToStep(3)}>Suivant</button>
    </div>
  );
  
  const Step3 = ({ experience, handleExperienceChange, addExperience, goToStep  }) => (
    <div>
      <h3>Étape 3: L'expérience professionnelle</h3>
      {experience.map((exp, index) => (
        <div key={index} className="form-grp">
          <label htmlFor={`jobTitle-${index}`}>Titre d'emploi</label>
          <input
            type="text"
            id={`jobTitle-${index}`}
            name="jobTitle"
            value={exp.jobTitle}
            placeholder="Titre d'emploi"
            onChange={(e) => handleExperienceChange(index, e)}
          />
          <label htmlFor={`company-${index}`}>Entreprise</label>
          <input
            type="text"
            id={`company-${index}`}
            name="company"
            value={exp.company}
            placeholder="Entreprise"
            onChange={(e) => handleExperienceChange(index, e)}
          />
          <label htmlFor={`duration-${index}`}>Durée</label>
          <input
            type="text"
            id={`duration-${index}`}
            name="duration"
            value={exp.duration}
            placeholder="Durée"
            onChange={(e) => handleExperienceChange(index, e)}
          />
        </div>
      ))}
      <button type="button" style={{width:'auto', marginRight:'5%'}}  onClick={addExperience}>Ajouter une autre expérience</button>
      <button type="button" style={{width:'auto', marginRight:'5px'}}  onClick={() => goToStep(2)}>précédent</button>
      <button type="button" style={{width:'auto'}}  onClick={() => goToStep(4)}>Suivant</button>
    </div>
  );
  
  const Step4 = ({ handleFileChange, handleFileChangeUpload, handleFileChangeWithValidation, isFileValid }) => (
    <div>
      <h3>Étape 4: Télécharger CV</h3>
      <div className="form-grp">
        <label htmlFor="cvUpload">Télécharger CV</label>
        <input
          type="file"
          id="cvUpload"
          name="cvUpload"
          accept=".pdf"
          onChange={(event) => {
            handleFileChange(event);
            handleFileChangeWithValidation(event);
            handleFileChangeUpload(event);
          }}
        />
  
        {!isFileValid && <span className="error-message" style={{ color: 'red', marginBottom: '10px' }}>Veuillez télécharger votre CV en fichier PDF valide.</span>}
      </div>
      <button
        type="submit"
        style={{ width: 'auto' }}
        disabled={!isFileValid} 
      >
        Soumettre
      </button>
    </div>
  );
  