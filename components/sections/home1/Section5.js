import React, { useEffect, useRef, useState } from 'react';
import Image from 'next/image';

const Section5 = () => {
  const partnersRef = useRef(null);
  const [animationPlayed, setAnimationPlayed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Auto-slide functionality for mobile
  useEffect(() => {
    if (isMobile && animationPlayed) {
      const interval = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % partners.length);
      }, 2000); // Change slide every 2 seconds

      return () => clearInterval(interval);
    }
  }, [isMobile, animationPlayed]);
  
  const partners = [
    {
      name: 'ML Nice PACA',
      url: 'https://www.mlnice-paca.org',
      logoPath: '/assets/img/partners/mince.svg',
      clickable: true
    },
    {
      name: 'APEC',
      url: 'https://www.apec.fr',
      logoPath: '/assets/img/partners/blue.svg',
      clickable: true
    },
    {
      name: 'France Travail',
      url: 'https://www.francetravail.fr',
      logoPath: '/assets/img/partners/francetravailsvg.svg',
      clickable: true
    },
    {
      name: 'PLIE Nice Côte d\'Azur',
      url: 'https://www.pitham.org/partenaires/plie-nice-cote-dazur/',
      logoPath: '/assets/img/partners/PITHAM.png',
      clickable: true
    }
  ];

  useEffect(() => {
    // Animation for the partners section
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !animationPlayed) {
          setAnimationPlayed(true);

          if (isMobile) {
            // Mobile: Just show the first slide immediately
            const partnerItems = entry.target.querySelectorAll('.partner-item');
            if (partnerItems[0]) {
              partnerItems[0].classList.add('active');
            }
          } else {
            // Desktop: Original animation
            const partnerItems = entry.target.querySelectorAll('.partner-item');
            partnerItems.forEach((item, index) => {
              setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
              }, 150 * index);
            });

            // Animate the connection lines
            setTimeout(() => {
              const lines = entry.target.querySelectorAll('.connection-line');
              lines.forEach((line, index) => {
                setTimeout(() => {
                  line.style.width = '100%';
                  line.style.opacity = '0.7';
                }, 100 * index);
              });
            }, 800);
          }

          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.3 });

    if (partnersRef.current) {
      observer.observe(partnersRef.current);
    }

    return () => {
      if (partnersRef.current) {
        observer.unobserve(partnersRef.current);
      }
    };
  }, [animationPlayed, isMobile]);

  // Update active slide for mobile
  useEffect(() => {
    if (isMobile && partnersRef.current) {
      const partnerItems = partnersRef.current.querySelectorAll('.partner-item');
      partnerItems.forEach((item, index) => {
        if (index === currentSlide) {
          item.classList.add('active');
        } else {
          item.classList.remove('active');
        }
      });
    }
  }, [currentSlide, isMobile]);

  // Generate random particle positions for the background
  const generateParticles = (count = 40) => {
    const particles = [];
    for (let i = 0; i < count; i++) {
      particles.push({
        top: `${Math.random() * 100}%`,
        left: `${Math.random() * 100}%`,
        size: Math.random() * 5 + 3,
        opacity: Math.random() * 0.5 + 0.1,
        delay: Math.random() * 10
      });
    }
    return particles;
  };

  const particles = generateParticles();

  return (
    <section id="partenaires" style={{
      background: 'linear-gradient(135deg, #f0f5fa 0%, #e4f0f9 100%)',
      padding: 'clamp(32px, 5vw, 64px) 0',
      position: 'relative',
      overflow: 'hidden',
      paddingBottom: '200px'
    }}>
      {/* Particle Background */}
      {particles.map((particle, i) => (
        <div key={`particle-${i}`} style={{
          position: 'absolute',
          width: `${particle.size}px`,
          height: `${particle.size}px`,
          borderRadius: '50%',
          backgroundColor: '#2D8BBA',
          top: particle.top,
          left: particle.left,
          opacity: particle.opacity,
          animation: `float 10s ease-in-out ${particle.delay}s infinite alternate`
        }} />
      ))}
      
      {/* CSS Animation for particles */}
      <style jsx>{`
        @keyframes float {
          0% { transform: translateY(0) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(5deg); }
          100% { transform: translateY(10px) rotate(-5deg); }
        }

        @media (max-width: 768px) {
          .partners-container {
            flex-direction: row !important;
            justify-content: flex-start !important;
            align-items: center !important;
            overflow: hidden !important;
            position: relative !important;
            height: 200px !important;
          }

          .partner-item {
            width: 100% !important;
            max-width: 300px !important;
            flex-shrink: 0 !important;
            position: absolute !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            opacity: 0 !important;
            transition: opacity 0.5s ease !important;
          }

          .partner-item.active {
            opacity: 1 !important;
          }

          .connection-line {
            display: none !important;
          }

          .slider-dots {
            display: flex !important;
            justify-content: center !important;
            gap: 8px !important;
            margin-top: 20px !important;
          }

          .slider-dot {
            width: 10px !important;
            height: 10px !important;
            border-radius: 50% !important;
            background-color: rgba(45, 139, 186, 0.3) !important;
            cursor: pointer !important;
            transition: background-color 0.3s ease !important;
          }

          .slider-dot.active {
            background-color: #2D8BBA !important;
          }
        }
      `}</style>

      <div className="container" style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 clamp(16px, 3vw, 32px)',
        position: 'relative',
        zIndex: 1
      }}>
        <h2 style={{
          fontSize: 'clamp(40px, 5vw, 64px)',
          fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
          textAlign: 'center',
          marginBottom: 'clamp(32px, 6vw, 64px)',
          color: '#2D8BBA',
          position: 'relative',
          display: 'inline-block',
          left: '50%',
          transform: 'translateX(-50%)',
          paddingBottom: '15px'
        }}>
          Partenaires Emploi
          <div style={{
            position: 'absolute',
            bottom: 0,
            left: '10%',
            width: '80%',
            height: '4px',
            background: 'linear-gradient(90deg, transparent, #2D8BBA 20%, #2D8BBA 80%, transparent)',
            borderRadius: '4px'
          }} />
        </h2>
        
        <div ref={partnersRef} className="partners-container" style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
          position: 'relative',
          minHeight: '180px',
          padding: '0 20px'
        }}>
          {/* Connection lines between partners */}
          {partners.slice(0, -1).map((_, index) => (
            <div 
              key={`line-${index}`} 
              className="connection-line"
              style={{
                position: 'absolute',
                top: '50%',
                left: `${(100 / (partners.length - 1)) * index + (100 / (partners.length * 2)) - 2}%`,
                width: '0%', // starts at 0 and animates to 100%
                height: '3px',
                background: 'linear-gradient(90deg, rgba(45,139,186,0.3), rgba(45,139,186,0.7))',
                borderRadius: '3px',
                opacity: 0,
                transition: 'width 1.5s ease, opacity 1.5s ease',
                zIndex: 0
              }}
            />
          ))}
          
          {partners.map((partner, index) => (
            <div
              key={index}
              className="partner-item"
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                width: isMobile ? '100%' : `${100 / partners.length}%`,
                maxWidth: '260px',
                opacity: isMobile ? 0 : 0,
                transform: isMobile ? 'translateX(-50%)' : 'translateY(30px)',
                transition: 'opacity 0.8s ease, transform 0.8s ease',
                zIndex: 2
              }}
            >
              {partner.clickable ? (
                <a 
                  href={partner.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  style={{
                    display: 'block',
                    textDecoration: 'none',
                    transition: 'all 0.4s ease'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.transform = 'scale(1.1) translateY(-10px)';
                    e.currentTarget.style.boxShadow = '0 12px 20px rgba(45,139,186,0.2)';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.transform = 'scale(1) translateY(0)';
                    e.currentTarget.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
                  }}
                >
                  <div style={{
                    width: 'clamp(150px, 18vw, 220px)',
                    height: 'clamp(100px, 12vw, 150px)',
                    backgroundColor: 'white',
                    borderRadius: '20px',
                    padding: '12px',
                    boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative',
                    overflow: 'hidden',
                    transition: 'all 0.4s ease'
                  }}>
                    {/* Decorative element */}
                    <div style={{
                      position: 'absolute',
                      top: '-10px',
                      right: '-10px',
                      width: '40px',
                      height: '40px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(45,139,186,0.1)',
                      zIndex: 0
                    }} />
                    
                    <div style={{ 
                      position: 'relative', 
                      width: '100%', 
                      height: '100%',
                      zIndex: 1 
                    }}>
                      <Image 
                        src={partner.logoPath} 
                        alt={partner.name} 
                        layout="fill"
                        objectFit="contain"
                        quality={100}
                      />
                    </div>
                  </div>
                  
                  <span style={{
                    display: 'block',
                    marginTop: '14px',
                    fontSize: 'clamp(14px, 1.5vw, 18px)',
                    fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
                    color: '#2D8BBA',
                    textAlign: 'center',
                    transform: 'translateY(0)',
                    transition: 'transform 0.3s ease'
                  }}>
                    {isMobile && partner.name === 'PLIE Nice Côte d\'Azur' ? 'PLIE Nice' : partner.name}
                  </span>
                </a>
              ) : (
                <div style={{ textAlign: 'center' }}>
                  <div style={{
                    width: 'clamp(150px, 18vw, 220px)',
                    height: 'clamp(100px, 12vw, 150px)',
                    backgroundColor: 'white',
                    borderRadius: '20px',
                    padding: '12px',
                    boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative',
                    overflow: 'hidden'
                  }}>
                    {/* Decorative element */}
                    <div style={{
                      position: 'absolute',
                      top: '-10px',
                      right: '-10px',
                      width: '40px',
                      height: '40px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(45,139,186,0.1)',
                      zIndex: 0
                    }} />
                    
                    <div style={{ 
                      position: 'relative', 
                      width: '100%', 
                      height: '100%',
                      zIndex: 1
                    }}>
                      <Image 
                        src={partner.logoPath} 
                        alt={partner.name} 
                        layout="fill"
                        objectFit="contain"
                        quality={100}
                      />
                    </div>
                  </div>
                  
                  <span style={{
                    display: 'block',
                    marginTop: '14px',
                    fontSize: 'clamp(14px, 1.5vw, 18px)',
                    fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
                    color: '#2D8BBA',
                    textAlign: 'center'
                  }}>
                    {isMobile && partner.name === 'PLIE Nice Côte d\'Azur' ? 'PLIE Nice' : partner.name}
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Slider dots for mobile */}
        {isMobile && (
          <div className="slider-dots">
            {partners.map((_, index) => (
              <div
                key={index}
                className={`slider-dot ${index === currentSlide ? 'active' : ''}`}
                onClick={() => setCurrentSlide(index)}
              />
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default Section5;
