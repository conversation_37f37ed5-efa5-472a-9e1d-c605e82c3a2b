import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

const Section2 = () => {
  return (
    <section id="success-story" style={{
      display: 'flex',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #2D8BBA 50%, #0021CD 100%)',
      position: 'relative'
    }}>
      {/* Left Side */}
      <div style={{
        flex: '1',
        padding: '60px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start'
      }}>
        <h2 style={{
          color: '#fff',
          fontSize: '2.5rem',
          fontWeight: '700',
          marginBottom: '5px',
          lineHeight: '1.2',
          fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif'
        }}>
          NOTRE SUCCESS STORY
        </h2>
        
      
        <div style={{
          position: 'relative',
          width: '70%',
          height: '200px',
          marginBottom: '40px',
          border:'2px solid #000',
          overflow: 'hidden'
        }}>
          <Image
            src="/assets/img/images/teamet.jpg"
            alt="Formation"
            layout="fill"
            objectFit="cover"
          />
        </div>
        <h4 style={{
          color: '#fff',
          fontSize: '1.5rem',
          fontWeight: '700',
          marginBottom: 'clamp(10px, 5vw, 10px)',
          lineHeight: '1.2',
          fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif'
        }}>
          NOTRE SUCCESS STORY
        </h4>
        <hr style={{
          width: '70%',
          height: '4px',
          backgroundColor: '#fff',
      
        }} />
        <h4 style={{
          color: '#fff',
          fontSize: '1.2rem',
          fontWeight: '700',
          marginBottom: '50px',
          lineHeight: '1.2',
          fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
        }}>
          2013... 2025
        </h4>
        <h4 style={{
          color: '#fff',
          fontSize: '1rem',
          fontWeight: '700',
          flex: '1',
          display: 'flex',
          justifyContent: 'flex-end',
          lineHeight: '1.2',
          fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
        }}>
          REMETTRE TON SCHÉMA AVEC LES ANNÉES
        </h4>
      </div>

      {/* Right Side */}
      <div style={{
        flex: '1',
        padding: '60px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start'
      }}>
        

        <p style={{
          color: '#fff',
          fontSize: 'clamp(16px, 2vw, 12px)',
          marginBottom: '30px',
          lineHeight: '1.8',
          fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
        }}>
          Cabinet de conseil en recrutement d'experts, cadres et
          managers (PME, ETI et groupes internationaux), ATLANTIS fait
          partie du gourpe ELES et se différencie par sa très grande
          implication dans la valorisation des parcours et carrières des
          candidats, et la co-création avec ses clients de solutions de
          recrutement innovantes (management de transition,
          intrapreneuriat, freelancing, conseil RH, RPO …).
        </p>
        <p style={{
          color: '#fff',
          fontSize: 'clamp(16px, 2vw, 12px)',
          marginBottom: '40px',
          lineHeight: '1.8',
          fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
        }}>
          Pour mener à bien notre mission de créer LA bonne rencontre
          entre une entreprise et une personne, nous développons une
          relation forte avec nos interlocuteurs. La qualité de cette
          relation et de notre accompagnement (physique + digital)
          nous permet d'apparaître en 2020 dans le top des
          classements des cabinets de recrutement en France !
        </p>

        <div style={{ display: 'flex', justifyContent: 'flex-end', width: '100%' }}>
          <Link href="/atlantis">
          <button className='btn'>
              Découvrir le groupe ELES
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default Section2;