import React from 'react';
import styles from '@/styles/SecondBanner.module.css';
import { motion } from 'framer-motion';

const SecondBanner = () => {
    return (
        <>
            <style jsx global>{`
                @media (max-width: 768px) {
                    #a-propos .mobile-title-text {
                        font-size: 3rem !important;
                    }

                    #a-propos .mobile-text-container {
                        transform: translateY(-60px) !important;
                    }
                }
            `}</style>
            <div id="a-propos" className={styles.bannerSection}>
            <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1.2 }}
                className={styles.backgroundImage}
                style={{
                    backgroundImage: `linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(255,255,255,1) 1%, rgba(255,255,255,0) 40%), url("/assets/img/images/women-man.png")`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center'
                }}
            />
            <motion.div 
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className={styles.bottomContent}
            >
                <div className={`${styles.bottomLeft} mobile-text-container`}>
                    <motion.p
                        className="mobile-title-text"
                        initial={{ opacity: 0, x: -50 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.8, delay: 0.8 }}
                        style={{ fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif', fontSize: '96px', color: '#FFFFFF', marginBottom: '-40px' }}
                    >
                        A PROPOS
                    </motion.p>
                    <motion.p
                        className="mobile-title-text"
                        initial={{ opacity: 0, x: -50 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.8, delay: 1.1 }}
                        style={{ fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif', fontSize: '96px', color: '#FFFFFF', marginTop: '-40px' }}
                    >
                        D'ATLANTIS
                    </motion.p>
                </div>
            </motion.div>
        </div>
        </>
    );
};

export default SecondBanner;