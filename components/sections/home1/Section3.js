import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

const FirstBanner = () => {
  return (
    <section id="notre-equipe" style={{
      backgroundImage: 'url("/assets/img/images/bg-blue.png")',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      minHeight: '100vh',
      position: 'relative',
      display: 'flex',
      alignItems: 'center'
    }}>
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)'
      }}></div>
      
      <div style={{ 
        position: 'relative', 
        zIndex: 1,
        width: '100%',
        padding: '60px 0'
      }}>
        <div className="container" style={{ 
          minHeight: '70vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          {/* Left Content */}
          <div style={{
            maxWidth: '600px'
          }}>
            <h1 style={{
              color: '#fff',
              fontSize: '64px',
              fontWeight: '700',
              marginBottom: '30px',
              fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
            }}>
              NOTRE<br />
              ÉQUIPE<br />
& ses valeurs
            </h1>
          </div>

          {/* Right Content */}
          <div style={{
            position: 'relative',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '30px'
          }}>
            <div style={{
              position: 'relative',
              width: '400px',
              height: '300px',
              borderRadius: '20px',
              overflow: 'hidden'
            }}>
              <Image
                src="/assets/img/images/pc-hand.png"
                alt="PC Hand"
                layout="fill"
                objectFit="cover"
                quality={100}
              />
            </div>
            <Link href="/offres">
              <button style={{
                background: '#fff',
                color: '#2D8BBA',
                padding: '15px 40px',
                border: '2px solid #2D8BBA',
                borderRadius: '9999px',
                fontSize: '1.2rem',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif'
              }}>
                DÉCOUVRER NOS OFFRES
              </button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FirstBanner;
