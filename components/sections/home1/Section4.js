import React, { useState, useEffect } from 'react';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement } from 'chart.js';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser } from '@fortawesome/free-solid-svg-icons';
import Image from 'next/image';
import { motion, useInView, useAnimation, AnimatePresence } from 'framer-motion';

ChartJS.register(CategoryScale, LinearScale, BarElement);

// Animated counter component using framer-motion
const AnimatedCounter = ({ value, symbol = "", duration = 2 }) => {
  const ref = React.useRef(null);
  const inView = useInView(ref, { once: false, amount: 0.5 });
  const [displayValue, setDisplayValue] = useState(0);
  const controls = useAnimation();
  
  useEffect(() => {
    if (inView) {
      let startValue = 0;
      const updateCounter = () => {
        if (startValue < value) {
          const increment = Math.ceil(value / (duration * 60));
          startValue = Math.min(startValue + increment, value);
          setDisplayValue(startValue);
          requestAnimationFrame(updateCounter);
        }
      };
      requestAnimationFrame(updateCounter);
      
      controls.start({ opacity: 1, y: 0 });
    } else {
      setDisplayValue(0);
      controls.start({ opacity: 0, y: 20 });
    }
  }, [inView, value, duration, controls]);
  
  return (
    <motion.span 
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={controls}
      transition={{ duration: 0.5 }}
    >
      {displayValue}{symbol}
    </motion.span>
  );
};

const Section4 = () => {
  const barRef = React.useRef(null);
  const inView = useInView(barRef, { once: false, amount: 0.5 });
  const [barProgress, setBarProgress] = useState(0);
  const [visibleUsers, setVisibleUsers] = useState(0);
  
  // Update bar chart progress when in view
  useEffect(() => {
    if (inView) {
      let progress = 0;
      const duration = 2000; // 2 seconds
      const startTime = Date.now();
      
      const updateProgress = () => {
        const elapsed = Date.now() - startTime;
        progress = Math.min(elapsed / duration, 1);
        setBarProgress(progress);
        
        // Update visible users count based on progress
        setVisibleUsers(Math.ceil(12 * progress));
        
        if (progress < 1) {
          requestAnimationFrame(updateProgress);
        }
      };
      
      requestAnimationFrame(updateProgress);
    } else {
      setBarProgress(0);
      setVisibleUsers(0);
    }
  }, [inView]);
  
  // Bar Chart Data with animation
  const barData = {
    labels: ['Entreprises'],
    datasets: [
      {
        data: [86 * barProgress],
        backgroundColor: '#FFFFFF',
        borderColor: '#FFFFFF',
        borderRadius: 20,
        barThickness: 20,
      },
      {
        data: [14],
        backgroundColor: 'rgba(255, 255, 255, 0.3)',
        borderColor: 'rgba(255, 255, 255, 0.3)',
        borderRadius: 20,
        barThickness: 20,
      }
    ]
  };

  // Donut Chart Data
  const donutData = {
    labels: ['Completed', 'Remaining'],
    datasets: [{
      data: [70, 30],
      backgroundColor: ['#000', '#fff'],
      borderWidth: 0,
    }]
  };

  const chartOptions = {
    plugins: {
      legend: {
        display: false
      }
    },
    maintainAspectRatio: false,
    responsive: true,
  };

  const barOptions = {
    ...chartOptions,
    indexAxis: 'y',
    scales: {
      x: {
        display: false,
        max: 100,
        beginAtZero: true,
        stacked: true
      },
      y: {
        display: false,
        stacked: true,
        reverse: true 
      }
    }
  };

  const donutOptions = {
    ...chartOptions,
    cutout: '75%',
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        enabled: false
      }
    }
  };

  const cardStyle = {
    border: 'none',
    borderRadius: '16px',
    padding: '25px',
    backgroundColor: '#2D8BBA',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '270px',
    width: '100%',
    maxWidth: '270px',
    minWidth: '250px',
    flex: '0 1 auto',
    transition: 'all 0.3s ease',
    boxShadow: '0 8px 20px rgba(45, 139, 186, 0.2)',
    position: 'relative',
    overflow: 'hidden'
  };

  // Create array of 12 users for pictogram
  const users = Array(12).fill(null);

  return (
    <>
      <style jsx>{`
        @media (max-width: 768px) {
          .cards-container {
            flex-direction: column !important;
            align-items: center !important;
          }
          .card-item {
            max-width: 300px !important;
            width: 90% !important;
            margin: 10px auto !important;
          }
        }
        @media (max-width: 480px) {
          .card-item {
            max-width: 100% !important;
            width: 95% !important;
          }
        }
      `}</style>
      <section id="quelque-chiffre" style={{
        backgroundColor: '#f8fafc',
        padding: '50px 0',
        minHeight: 'auto',
        marginBottom: '50px'
      }}>
      <div className="container">
        <motion.h2 
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: false }}
          style={{
            color: '#1a365d',
            fontSize: '2.5rem',
            textAlign: 'center',
            marginBottom: '60px',
            fontFamily: 'Helvetica World Bold, sans-serif',
            fontWeight: '700'
          }}
        >
          Quelques chiffres...
        </motion.h2>

        <div className="cards-container" style={{
          display: 'flex',
          flexWrap: 'wrap',
          justifyContent: 'center',
          gap: '20px',
          alignItems: 'stretch'
        }}>
          {/* Univers sectoriels Card */}
          <motion.div
            className="card-item"
            style={cardStyle}
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: false }}
            whileHover={{
              y: -10,
              scale: 1.02,
              boxShadow: '0 20px 25px -5px rgba(45, 139, 186, 0.2), 0 10px 10px -5px rgba(45, 139, 186, 0.1)',
              borderColor: 'rgba(45, 139, 186, 0.5)'
            }}
          >
            <motion.div 
              className="relative w-[70%] mb-6"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <Image 
                src="/assets/img/images/activite2.png"
                alt="Activity chart"
                width={200}
                height={150}
                quality={100}
                className="rounded-[20px]"
              />
            </motion.div>
            <motion.p 
              style={{
                color: '#000',
                textAlign: 'center',
                fontSize: '1.1rem',
                fontFamily: 'sans-serif',
                margin: 0
              }}
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <span style={{ 
                fontWeight: '700', 
                fontSize: '2.2rem',
                display: 'block',
                marginBottom: '10px',
                color: '#fff'
              }}>
                +<AnimatedCounter value={10} />
              </span>
              <span style={{ fontSize: '1.1rem',color: '#fff', opacity: 0.9 }}>univers sectoriels</span>
            </motion.p>
          </motion.div>

          {/* Bar Chart Card */}
          <motion.div
            ref={barRef}
            className="card-item"
            style={cardStyle}
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: false }}
            whileHover={{
              y: -10,
              scale: 1.02,
              boxShadow: '0 20px 25px -5px rgba(45, 139, 186, 0.2), 0 10px 10px -5px rgba(45, 139, 186, 0.1)',
              borderColor: 'rgba(45, 139, 186, 0.5)'
            }}
          >
            <motion.div 
              style={{ 
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '15px',
                marginBottom: '25px',
              }}
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div style={{ height: '140px', width: '140px', position: 'relative' }}>
                <Bar data={barData} options={barOptions} />
              </div>
              <motion.div 
                style={{
                  color: '#fff',
                  fontSize: '1.8rem',
                  fontWeight: 'bold',
                  padding: '5px 0',
                }}
                whileHover={{ scale: 1.1 }}
                transition={{ duration: 0.3 }}
              >
                <AnimatedCounter value={86} symbol="%" />
              </motion.div>
            </motion.div>
            <motion.p 
              style={{
                color: '#fff',
                textAlign: 'center',
                fontSize: '1rem',
                fontFamily: 'Helvetica World, sans-serif',
                margin: '10px 0 0 0',
                lineHeight: '1.5'
              }}
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <AnimatedCounter value={10} /> ans d'expérience
              dans le recrutement
            </motion.p>
          </motion.div>
          
          {/* Pictogram Card */}
          <motion.div
            className="card-item"
            style={cardStyle}
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: false }}
            whileHover={{
              y: -10,
              scale: 1.02,
              boxShadow: '0 20px 25px -5px rgba(45, 139, 186, 0.2), 0 10px 10px -5px rgba(45, 139, 186, 0.1)',
              borderColor: 'rgba(45, 139, 186, 0.5)'
            }}
          >
            <motion.div 
              style={{
                display: 'flex',
                flexWrap: 'wrap',
                justifyContent: 'center',
                gap: '10px',
                marginTop: '20px',
                marginBottom: '25px',
                flex: 1,
                alignItems: 'center'
              }}
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              {users.map((_, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0.2, scale: 0.8 }}
                  animate={{ 
                    opacity: index < visibleUsers ? 1 : 0.2,
                    scale: index < visibleUsers ? 1 : 0.8
                  }}
                  whileHover={{ 
                    scale: 1.2,
                    rotate: 5
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <FontAwesomeIcon 
                    icon={faUser} 
                    style={{
                      fontSize: '1.8rem',
                      color: index < visibleUsers ? (index >= users.length - 3 ? '#2D8BBA' : '#000') : 'rgba(0, 0, 0, 0.2)',
                    }}
                  />
                </motion.div>
              ))}
            </motion.div>
            <motion.div 
              style={{
                color: '#fff',
                textAlign: 'center',
                fontSize: '1rem',
                fontFamily: 'Helvetica World, sans-serif',
                margin: '10px 0 0 0',
                lineHeight: '1.5'
              }}
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <span style={{ 
                fontWeight: '700', 
                fontSize: '2.2rem',
                display: 'block',
                marginBottom: '10px',
                color: '#fff'
              }}>
                +<AnimatedCounter value={500} />
              </span>
              <span style={{ fontSize: '1.1rem', opacity: 0.9 }}>recrutements par an</span>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
    </>
  );
};

export default Section4;