import React from 'react';
import Link from 'next/link';

const Section9 = () => {
  return (
    <section style={{
      backgroundImage: 'url("/images/temporels/avis.png")',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      height: '500px',
      position: 'relative',
      display: 'flex',
      alignItems: 'flex-start',
      justifyContent: 'center',
      padding: '30px'
    }}>
      {/* Dark overlay */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)'
      }} />

      {/* Button container - positioned at 30% from top */}
      <div style={{
        position: 'relative',
        zIndex: 1,
        marginTop: '100px' // 30% of 500px
      }}>
        <Link href="#">
          <button style={{
            backgroundColor: '#2D8BBA',
            color: '#fff',
            padding: '15px 30px',
            border: 'none',
            borderRadius: '30px',
            fontSize: '1.2rem',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            textTransform: 'uppercase',
            fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            ':hover': {
              backgroundColor: '#2d70a3',
              transform: 'translateY(-2px)'
            }
          }}>
            DÉCOUVREZ LES AVIS GOOGLE
          </button>
        </Link>
      </div>
    </section>
  );
};

export default Section9;