import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';

const Section3 = () => {
  const [buttonHovered, setButtonHovered] = React.useState(false);

  return (
    <section id="creer-compte" style={{
      position: 'relative',
      height: '70vh',
      width: '100%',
      overflow: 'hidden'
    }}>
      {/* Background image */}
      <motion.div 
        initial={{ scale: 1.1, opacity: 0.8 }}
        whileInView={{ scale: 1, opacity: 1 }}
        viewport={{ once: false }}
        transition={{ duration: 1.2 }}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundImage: `url("/assets/img/images/mans.jpg")`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          zIndex: 1
        }} 
      />

      {/* Dark Overlay */}
      <motion.div 
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 0.5 }}
        viewport={{ once: false }}
        transition={{ duration: 0.8 }}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundColor: 'rgba(0, 0, 0, 1)',
          zIndex: 2
        }} 
      />

      {/* Content Container */}
      <div style={{
        position: 'relative',
        zIndex: 3,
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        {/* Centered Content Wrapper */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: false }}
          transition={{ duration: 0.8 }}
          style={{
            width: '90%',
            maxWidth: '800px',
            textAlign: 'center',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '30px'
          }}
        >
          {/* Heading */}
          <motion.h2 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: false }}
            transition={{ duration: 0.7, delay: 0.2 }}
            style={{
              color: '#fff',
              fontSize: 'clamp(16px, 4vw, 32px)',
              fontWeight: '700',
              lineHeight: '1.5',
              fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
              letterSpacing: '1px',
              wordSpacing: '8px',
              textAlign: 'center'
            }}
          >
            VOUS RECHERCHEZ UN CABINET POUR VOUS ACCOMPAGNER SUR TOUS VOS BESOINS EN RECRUTEMENT ?
          </motion.h2>
          
          {/* Subheading */}
          <motion.p 
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: false }}
            transition={{ duration: 0.7, delay: 0.4 }}
            style={{
              color: '#fff',
              fontSize: 'clamp(12px, 1.4vw, 24px)',
              fontWeight: '700',
              lineHeight: '1.4',
              fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
              textAlign: 'center',
              marginBottom: '10px'
            }}
          >
            VOUS ÊTES AU BON ENDROIT
          </motion.p>
          
          {/* Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: false }}
            transition={{ duration: 0.7, delay: 0.6 }}
          >
            <Link href="/signup">
              <motion.button
                whileHover={{ 
                  scale: 1.05, 
                  y: -5,
                  boxShadow: '0 8px 15px rgba(0, 0, 0, 0.3)'
                }}
                whileTap={{ scale: 0.98 }}
                onMouseEnter={() => setButtonHovered(true)}
                onMouseLeave={() => setButtonHovered(false)}
                style={{
                  background: '#2D8BBA',
                  color: '#fff',
                  padding: '15px 40px',
                  border: '2px solid #2D8BBA',
                  borderRadius: '12px',
                  fontSize: 'clamp(14px, 1.5vw, 16px)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                  marginTop: '20px'
                }}
              >
                CRÉEZ VOTRE COMPTE
              </motion.button>
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Section3;
