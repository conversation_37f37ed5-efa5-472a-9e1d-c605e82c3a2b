import React from 'react';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBuilding } from '@fortawesome/free-solid-svg-icons';
import { motion } from 'framer-motion';
import styles from '@/styles/Section8.module.css';

const Section8 = () => {
  return (
    <>
      <style jsx global>{`
        @media (max-width: 768px) {
          #group-eles .mobile-main-title {
            font-size: 2.5rem !important;
          }

          /* Make card images bigger */
          #group-eles .mobile-card-image {
            width: 120px !important;
            height: 120px !important;
            min-width: 120px !important;
            min-height: 120px !important;
          }

          /* Increase margin between last card and footer */
          #group-eles {
            margin-bottom: 60px !important;
            padding-bottom: 40px !important;
          }

          /* Reduce margin between paragraph and first card */
          #group-eles .mobile-cards-container {
            margin-top: 0px !important;
            padding-top: 0 !important;
          }

          #group-eles .mobile-first-card {
            margin-top: 0 !important;
            padding-top: 0 !important;
          }

          /* Also reduce paragraph bottom margin */
          #group-eles .mobile-description {
            margin-bottom: 5px !important;
            padding-bottom: 0 !important;
          }

          /* Reduce right side top padding */
          #group-eles .mobile-right-side {
            padding-top: 0px !important;
            margin-top: 0 !important;
          }
        }
      `}</style>
      <section id="group-eles" className={styles.section8}>
      {/* Left Side - 40% */}
      <motion.div 
        initial={{ opacity: 0, x: -50 }}
        whileInView={{ opacity: 1, x: 0 }}
        viewport={{ once: false }}
        transition={{ duration: 0.8 }}
        className={styles.leftSide}
      >
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: false }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className={`${styles.mainTitle} mobile-main-title`}
        >
          GROUPE ELES
        </motion.h2>

        <motion.h3 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: false }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className={styles.subTitle}
        >
          EXPERTISE<br />
          SAVOIR-FAIRE<br />
          HUMANITÉ
        </motion.h3>

        <motion.p
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: false }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className={`${styles.description} mobile-description`}
        >
          Le groupe ELES spécialisé dans
          le recrutement et l'intérim de
          Marseille à Monaco propose
          une expertise de recruteurs
          passionnés depuis plus de 20
          ans.
        </motion.p>
      </motion.div>

      {/* Right Side - 60% */}
      <motion.div
        initial={{ opacity: 0, x: 50 }}
        whileInView={{ opacity: 1, x: 0 }}
        viewport={{ once: false }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className={`${styles.rightSide} mobile-right-side`}
      >
        {/* Cards Container */}
        <div className={styles.cardsContainer}>
          {/* Card 1 */}
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: false }}
            transition={{ duration: 0.7, delay: 0.4 }}
            className={styles.card}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
              className={`${styles.cardImage} mobile-card-image`}
            >
              <Image
                src="/images/temporels/group1.jpg"
                alt="Réseau d'agences"
                layout="fill"
                objectFit="cover"
              />
            </motion.div>
            <div style={{flex: 1, display: 'flex', flexDirection: 'column', gap: '15px'}}>
              <div className={styles.cardIconTitle}>
                <FontAwesomeIcon
                  icon={faBuilding}
                  style={{ fontSize: '1.8rem', color: '#2D8BBA' }}
                />
                <motion.h4 
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: false }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                  className={styles.cardTitle}
                >
                  Réseau d'agences intérim
                </motion.h4>
              </div>
              <motion.p 
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: false }}
                transition={{ duration: 0.5, delay: 0.7 }}
                className={styles.cardText}
              >
                Nous sommes spécialisés dans les secteurs<br />
                du BTP, de l'industrie, du tertiaire, de<br />
                l'événementiel, de la restauration, de<br />
                l'hôtellerie et du nautisme.
              </motion.p>
            </div>
          </motion.div>

          {/* Blue Line */}
          <motion.div 
            initial={{ opacity: 0, scaleX: 0 }}
            whileInView={{ opacity: 1, scaleX: 1 }}
            viewport={{ once: false }}
            transition={{ duration: 0.5 }}
            className={styles.blueLine}
          />

          {/* Card 2 */}
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: false }}
            transition={{ duration: 0.7, delay: 0.6 }}
            className={styles.card}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
              className={`${styles.cardImage} mobile-card-image`}
            >
              <Image
                src="/images/temporels/group2.jpg"
                alt="Agence de recrutement Monaco"
                layout="fill"
                objectFit="cover"
              />
            </motion.div>
            <div style={{flex: 1, display: 'flex', flexDirection: 'column', gap: '15px'}}>
              <div className={styles.cardIconTitle}>
                <FontAwesomeIcon
                  icon={faBuilding}
                  style={{ fontSize: '1.8rem', color: '#2D8BBA' }}
                />
                <motion.h4 
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: false }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                  className={styles.cardTitle}
                >
                  Agence de recrutement Monaco
                </motion.h4>
              </div>
              <motion.p 
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: false }}
                transition={{ duration: 0.5, delay: 0.9 }}
                className={styles.cardText}
              >
                MBRInterim est une agence de recrutement<br />
                implantée au coeur de Monaco. Découvrez<br />
                nos offres d'emploi en CDI, CDD et nos<br />
                missions interim.
              </motion.p>
            </div>
          </motion.div>

          {/* Blue Line */}
          <motion.div 
            initial={{ opacity: 0, scaleX: 0 }}
            whileInView={{ opacity: 1, scaleX: 1 }}
            viewport={{ once: false }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className={styles.blueLine}
          />

          {/* Card 3 */}
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: false }}
            transition={{ duration: 0.7, delay: 0.8 }}
            className={styles.card}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
              className={`${styles.cardImage} mobile-card-image`}
            >
              <Image
                src="/images/temporels/group3.jpg"
                alt="Formation et Coaching"
                layout="fill"
                objectFit="cover"
              />
            </motion.div>
            <div style={{flex: 1, display: 'flex', flexDirection: 'column', gap: '15px'}}>
              <div className={styles.cardIconTitle}>
                <FontAwesomeIcon
                  icon={faBuilding}
                  style={{ fontSize: '1.8rem', color: '#2D8BBA' }}
                />
                <motion.h4 
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: false }}
                  transition={{ duration: 0.5, delay: 1.0 }}
                  className={styles.cardTitle}
                >
                  Formation et Coaching
                </motion.h4>
              </div>
              <motion.p 
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: false }}
                transition={{ duration: 0.5, delay: 1.1 }}
                className={styles.cardText}
              >
                Rejoignez le groupe ELES. Nous recherchons des<br />
                personnes brillantes et dynamiques qui partagent<br />
                notre engagement : faire de l'humain le coeur de<br />
                nos solutions de recrutement.
              </motion.p>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </section>
    </>
  );
};

export default Section8;