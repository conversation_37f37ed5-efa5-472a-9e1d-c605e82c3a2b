import React from 'react';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faGlobe } from '@fortawesome/free-solid-svg-icons';

const Section10 = () => {
  const newsCards = [
    { id: 2, title: 'LES RÉMUNÉRATIONS ', subtitle: 'INFOS CCI' },
    { id: 1, title: 'UNE ENTREPRISE QUI DÉCHIRE EN PACA', subtitle: 'RESTAURANT...' },
    { id: 3, title: 'STRATÉGIE INTERNE', subtitle: 'RSE...' }
  ];

  return (
    <section style={{
      backgroundColor: '#fff',
      padding: '60px 0',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center'
    }}>
      <h2 style={{
        color: '#2D8BBA',
        fontSize: '2.5rem',
        fontWeight: '700',
        marginBottom: '40px',
        textAlign: 'center',
        fontSize: '4.5rem',
        fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
      }}>
        NEWS COTÉ EMPLOI...
      </h2>

      {/* Cards Container */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        gap: '0',
        width: '100%',
        maxWidth: '1200px',
        marginBottom: '40px'
      }}>
        {newsCards.map((card) => (
          <div key={card.id} style={{
            position: 'relative',
            width: '400px',
            height: '500px',
            backgroundColor: '#fff',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
          }}>
            {/* Image Container */}
            <div style={{
              position: 'relative',
              width: '100%',
              height: '100%'
            }}>
              <Image
                src="/images/temporels/batiments.jpg"
                alt="News"
                layout="fill"
                objectFit="cover"
              />
            </div>

            {/* Text Container */}
            <div style={{
              position: 'absolute',
              bottom: '30px',
              left: '50%',
              transform: 'translateX(-50%)',
              backgroundColor: '#fff',
              padding: '15px',
              textAlign: 'center',
              width: '80%',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
              borderRadius: '4px'
            }}>
              <h3 style={{
                fontSize: '1.1rem',
                fontWeight: '700',
                marginBottom: '5px',
                lineHeight: '1.2'
              }}>
                {card.title}
              </h3>
              <p style={{
                fontSize: '0.8rem',
                color: '#666',
                margin: 0
              }}>
                {card.subtitle}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation Button */}
      <button style={{
        backgroundColor: '#2D8BBA',
        color: '#fff',
        padding: '15px 30px',
        border: 'none',
        borderRadius: '30px',
        fontSize: '1.1rem',
        fontWeight: '600',
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        gap: '10px',
        transition: 'all 0.3s ease',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
        ':hover': {
          backgroundColor: '#2d70a3',
          transform: 'translateY(-2px)'
        }
      }}>
        VOIR LES ACTUS...
        <FontAwesomeIcon icon={faGlobe} style={{ fontSize: '1.1rem' }} />
      </button>
    </section>
  );
};

export default Section10;