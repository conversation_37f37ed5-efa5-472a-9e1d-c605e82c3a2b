import React from 'react';
import Link from 'next/link';

const Section5 = () => {
  return (
    <section style={{
      backgroundImage: 'url("/images/temporels/hands.jpg")',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      minHeight: '80vh',
      position: 'relative',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)'
      }}></div>
      
      <div style={{ 
        position: 'relative',
        zIndex: 1,
        width: '100%',
        padding: '40px 0',
        display: 'flex',
        flexDirection: 'column'
      }}>
        <div style={{
          width: '100%',
          backgroundColor: 'rgba(0, 0, 0, 0.3)',
          padding: '40px 0'
        }}>
          <h1 style={{
            color: '#fff',
            fontSize: '8rem',
            fontWeight: '700',
            textAlign: 'center',
            margin: 0,
            width: '100%',
            letterSpacing: '4px'
          }}>
            ATLANTIS CONSEIL
          </h1>
        </div>

        <div style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'flex-end',
          paddingRight: '80px',
          marginTop: '40px'
        }}>
          <Link href="/success-story">
            <button style={{
              background: 'transparent',
              color: '#fff',
              padding: '15px 30px',
              border: '2px solid #fff',
              borderRadius: '30px',
              fontSize: '1.1rem',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              ':hover': {
                background: 'rgba(255, 255, 255, 0.1)'
              }
            }}>
              DÉCOUVREZ NOTRE SUCCESS STORY
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default Section5;