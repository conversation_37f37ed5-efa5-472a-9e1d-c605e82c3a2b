import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEnvelope, faPhone } from '@fortawesome/free-solid-svg-icons';
import { motion } from 'framer-motion';
import styles from './Section2.module.css';

const Section2 = () => {
  const [contactHovered, setContactHovered] = React.useState(false);
  const [questionHovered, setQuestionHovered] = React.useState(false);
  const [isExpanded, setIsExpanded] = React.useState(false);
  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const fullText = "Que vous ayez besoin d'une ressource experte pour quelques semaines ou plusieurs années, chez Atlantis nous sommes convaincus qu'un cabinet de recrutement innovant doit pouvoir répondre à tous les besoins de ses clients. Nos solutions en matière de recrutement permettent de répondre à vos enjeux en termes de durée et de profil recherché ! Quelque soit votre secteur d'activité nous pouvons vous apporter la solution.";

  const shortText = "Que vous ayez besoin d'une ressource experte pour quelques semaines ou plusieurs années, chez Atlantis nous sommes convaincus qu'un cabinet de recrutement innovant doit pouvoir répondre à tous les besoins de ses clients...";
  return (
    <>
      <style jsx global>{`
        @media (max-width: 768px) {
          /* Reset all container padding/margins */
          #chasseur-tete .container {
            padding-left: 20px !important;
            padding-right: 20px !important;
            margin-left: auto !important;
            margin-right: auto !important;
          }

          #chasseur-tete .row {
            margin-left: 0 !important;
            margin-right: 0 !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
          }

          #chasseur-tete .col-lg-6 {
            padding-left: 0 !important;
            padding-right: 0 !important;
            margin-left: auto !important;
            margin-right: auto !important;
          }

          /* Button centering with aggressive reset */
          #chasseur-tete .mobile-button-container {
            justify-content: center !important;
            margin: 20px auto 0 auto !important;
            display: flex !important;
            align-items: center !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
            width: 100% !important;
          }

          #chasseur-tete .mobile-button-wrapper {
            display: flex !important;
            justify-content: center !important;
            padding: 0 !important;
            margin: 0 auto !important;
            width: 100% !important;
            text-align: center !important;
          }

          #chasseur-tete div.mobile-button-wrapper {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            padding: 0 !important;
            margin: 0 auto !important;
            left: 0 !important;
            right: 0 !important;
          }

          /* Image centering with aggressive reset */
          #chasseur-tete .mobile-image-container {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            padding: 20px 0 !important;
            flex-direction: column !important;
            margin-left: auto !important;
            margin-right: auto !important;
            width: 100% !important;
          }

          #chasseur-tete .mobile-image {
            margin: 0 auto !important;
            display: block !important;
            text-align: center !important;
            left: 0 !important;
            right: 0 !important;
          }

          #chasseur-tete div.mobile-image {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin: 0 auto !important;
            width: 100% !important;
          }

          /* Override any existing column styles */
          #chasseur-tete .col-lg-6.mobile-image-container {
            justify-content: center !important;
            align-items: center !important;
            text-align: center !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
            margin-left: auto !important;
            margin-right: auto !important;
          }

          /* Force center any motion divs */
          #chasseur-tete .mobile-button-container > div {
            margin-left: auto !important;
            margin-right: auto !important;
            text-align: center !important;
          }

          #chasseur-tete .mobile-image-container > div {
            margin-left: auto !important;
            margin-right: auto !important;
            text-align: center !important;
          }

          /* Hide image on mobile */
          #chasseur-tete .mobile-image-container {
            display: none !important;
          }

          /* Reduce margin between button and next section */
          #chasseur-tete {
            padding-bottom: 1px !important;
            margin-bottom: 0 !important;
          }

          #chasseur-tete .mobile-button-container {
            margin-bottom: 0 !important;
          }

          #chasseur-tete .section-padding {
            padding-bottom: 20px !important;
          }
        }
      `}</style>
      <section id="chasseur-tete" className="section-padding" style={{
        padding: '40px 0',
        background: '#fff'
      }}>
      <div className="container" style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 15px'
      }}>
        <div className="row" style={{
          display: 'flex',
          flexWrap: 'wrap',
        }}>
          {/* Left Column */}
          <motion.div 
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: false }}
            transition={{ duration: 0.8 }}
            className="col-lg-6" 
            style={{
              padding: '0 15px',
              marginBottom: '30px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between'
            }}
          >
            <div className="content-wrapper">
              <motion.h2 
                initial={{ opacity: 0, y: -20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: false }}
                transition={{ duration: 0.6, delay: 0.2 }}
                style={{
                  color: '#2D8BBA',
                  fontSize: 'clamp(32px, 4vw, 48px)',
                  fontWeight: '700',
                  marginTop: '20px',
                  marginBottom: '25px',
                  lineHeight: '1.2',
                  fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif'
                }}
              >
                CDD ? CDI ?
              </motion.h2>
              <motion.div
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: false }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <motion.p
                  style={{
                    fontSize: 'clamp(16px, 2vw, 20px)',
                    lineHeight: '1.8',
                    color: '#666',
                    marginBottom: isMobile ? '15px' : '30px',
                    fontFamily: 'GlacialIndifference-Regular'
                  }}
                  animate={{
                    height: isMobile && !isExpanded ? 'auto' : 'auto'
                  }}
                  transition={{ duration: 0.3, ease: 'easeInOut' }}
                >
                  {isMobile && !isExpanded ? shortText : fullText}
                  {isMobile && (
                    <motion.span
                      onClick={toggleExpanded}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      style={{
                        color: '#2D8BBA',
                        cursor: 'pointer',
                        fontWeight: '500',
                        marginLeft: '5px',
                        textDecoration: 'underline',
                        fontSize: 'inherit',
                        fontFamily: 'inherit',
                        transition: 'all 0.3s ease'
                      }}
                    >
                      <br/>
                      {isExpanded ? 'Voir moins' : 'Voir plus'}
                    </motion.span>
                  )}
                </motion.p>
              </motion.div>
            </div>

            {/* Left-side "Nous contacter" Button */}
            <motion.div
              className="mobile-button-container"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: false }}
              transition={{ duration: 0.6, delay: 0.6 }}
              style={{
                display: 'flex',
                justifyContent: 'flex-end',
                marginTop: '20px'
              }}
            >
              <div className="col-12 mobile-button-wrapper" style={{
                display: 'flex',
                justifyContent: 'flex-end',
                paddingRight: '15px',
                marginTop: 'clamp(-30px, -4vh, -20px)'
              }}>
                <Link href="/contact">
                  <motion.button
                    whileHover={{ scale: 1.05, y: -5 }}
                    whileTap={{ scale: 0.98 }}
                    onMouseEnter={() => setQuestionHovered(true)}
                    onMouseLeave={() => setQuestionHovered(false)}
                    style={{
                      background: '#2D8BBA',
                      color: '#fff',
                      padding: '15px 40px',
                      border: '2px solid #2D8BBA',
                      borderRadius: '9999px',
                      fontSize: 'clamp(14px, 1.5vw, 16px)',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                      fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif'
                    }}
                  >
                    Nous contacter
                    <FontAwesomeIcon className='ms-2' icon={faPhone} />
                    <FontAwesomeIcon className='ms-2' icon={faEnvelope} />
                  </motion.button>
                </Link>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Column with Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: false }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className={`mobile-image-container col-lg-6`}
            style={{
              padding: '0 15px',
              display: 'flex',
              justifyContent: 'flex-end',
              flexDirection: 'column',
              alignItems: 'flex-end'
            }}
          >
            <motion.div
              className="mobile-image"
              whileHover={{ scale: 1.03 }}
              transition={{ duration: 0.3 }}
            >
              <Image
                src="/assets/img/images/female-recruiter.jpg"
                alt="Accueil 2"
                width={540}
                className="col-lg-6"
                height={400}
                style={{
                  borderRadius: '20px',
                  width: 'clamp(300px, 80%, 540px)',
                  height: 'clamp(320px, 65vh, 540px)',
                  objectFit: 'cover',

                }}
              />
            </motion.div>
          </motion.div>
        </div>


      </div>
    </section>
    </>
  );
};

export default Section2;
