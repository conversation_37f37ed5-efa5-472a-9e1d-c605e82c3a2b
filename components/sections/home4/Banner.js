import Link from "next/link"
import styles from '../../../styles/Banner.module.css'
import { useEffect, useState } from 'react'

export default function Banner() {
    const [displayText, setDisplayText] = useState('');
    const [isDeleting, setIsDeleting] = useState(false);
    const texts = [
        "Créer des ponts entre ambitions et opportunités.",
        "Construire l'avenir des ressources humaines.",
        "Valoriser les talents d'aujourd'hui et de demain."
    ];
    const [textIndex, setTextIndex] = useState(0);
    const [isTypingComplete, setIsTypingComplete] = useState(false);

    useEffect(() => {
        const typingDelay = 50; // Delay between each character while typing
        const deletingDelay = 30; // Delay between each character while deleting
        const completePauseDelay = 2000; // Delay after completing typing before deleting
        const emptyPauseDelay = 500; // Delay after deleting before starting next word

        const typeText = () => {
            const currentText = texts[textIndex];
            
            if (!isDeleting) {
                if (displayText.length < currentText.length) {
                    setDisplayText(currentText.slice(0, displayText.length + 1));
                    setIsTypingComplete(false);
                } else {
                    setIsTypingComplete(true);
                    setTimeout(() => {
                        setIsDeleting(true);
                    }, completePauseDelay);
                }
            } else {
                if (displayText.length > 0) {
                    setDisplayText(displayText.slice(0, -1));
                } else {
                    setIsDeleting(false);
                    setTextIndex((prevIndex) => (prevIndex + 1) % texts.length);
                    setTimeout(() => {
                        setIsTypingComplete(false);
                    }, emptyPauseDelay);
                }
            }
        };

        const timer = setTimeout(
            typeText,
            isDeleting ? deletingDelay : typingDelay
        );

        return () => clearTimeout(timer);
    }, [displayText, isDeleting, textIndex, texts]);

    return (
        <section className={styles.bannerSection}>
            <div className={styles.backgroundPattern}></div>
            <div className={styles.container}>
                <div className="row align-items-center justify-content-between">
                    <div className="col-lg-5">
                        <div className={styles.content}>
                            <h2 className={styles.title}>
                                <span className={styles.typingText}>{displayText}</span>
                                <span className={`${styles.cursor} ${isTypingComplete ? styles.cursorBlink : ''}`}>|</span>
                            </h2>
                           
                            <Link href="/vous-recruter#recruterCandidat" className={styles.button}>
                                Commencer
                                <svg className="ms-2" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z" fill="currentColor"/>
                                </svg>
                            </Link>
                        </div>
                    </div>
                    <div className="col-lg-6">
                        <div className={`${styles.imageContainer} ${styles.showImage}`}>
                            <div className={styles.imageOverlay}></div>
                            <img 
                                src="/assets/img/banner/entreprise11.jpg" 
                                alt="Banner" 
                                className={styles.mainImage}
                            />
                            <img 
                                src="/assets/img/banner/h5_banner_shape01.png" 
                                alt="" 
                                className={`${styles.shape} ${styles.shapeOne}`} 
                            />
                            <img 
                                src="/assets/img/banner/h5_banner_shape02.png" 
                                alt="" 
                                className={`${styles.shape} ${styles.shapeTwo}`} 
                            />
                            <img 
                                src="/assets/img/banner/h5_banner_shape03.png" 
                                alt="" 
                                className={`${styles.shape} ${styles.shapeThree}`} 
                            />
                        </div>
                    </div>
                </div>
                <div id="recruterCandidat"></div>
            </div>
        </section>
    )
}
