import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Bar } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, CategoryScale, LinearScale, BarElement } from 'chart.js';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser } from '@fortawesome/free-solid-svg-icons';
import { motion, useInView, animate } from 'framer-motion';

ChartJS.register(ArcElement, CategoryScale, LinearScale, BarElement);

// Counter component that animates from 0 to target value
const Counter = ({ value, duration = 2, onUpdate = () => {} }) => {
  const [count, setCount] = useState(0);
  const ref = React.useRef(null);
  const isInView = useInView(ref, { once: false, amount: 0.5 });
  
  useEffect(() => {
    let startTimestamp = null;
    let animationFrameId = null;
    
    const step = (timestamp) => {
      if (!startTimestamp) startTimestamp = timestamp;
      const progress = Math.min((timestamp - startTimestamp) / (duration * 1000), 1);
      const currentCount = Math.floor(progress * value);
      setCount(currentCount);
      
      // Call the onUpdate callback with the progress value (0 to 1)
      onUpdate(progress);
      
      if (progress < 1) {
        animationFrameId = requestAnimationFrame(step);
      } else {
        setCount(value);
        onUpdate(1); // Ensure we end with full progress
      }
    };
    
    if (isInView) {
      setCount(0); // Reset to 0 when coming into view
      onUpdate(0); // Reset the chart progress
      animationFrameId = requestAnimationFrame(step);
    }
    
    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [isInView, value, duration, onUpdate]);
  
  return <span ref={ref}>{count}%</span>;
};

const Section6 = () => {
  // State for chart animations
  const [gaugeProgress, setGaugeProgress] = useState(0);
  const [barProgress, setBarProgress] = useState(0);
  const [donutProgress, setDonutProgress] = useState(0);
  
  // Gauge Chart Data with animation
  const gaugeData = {
    labels: ['Completed', 'Remaining'],
    datasets: [{
      data: [28 * gaugeProgress, 72],
      backgroundColor: ['#000', '#fff'],
      borderWidth: 0,
      circumference: 180,
      rotation: 270,
    }]
  };

  // Bar Chart Data with animation
  const barData = {
    labels: ['Entreprises'],
    datasets: [
      {
        data: [67 * barProgress],
        backgroundColor: '#000',
        borderColor: '#000',
        borderWidth: 1,
        borderRadius: {
          topLeft: 5,
          topRight: 0,
          bottomLeft: 5,
          bottomRight: 0
        },
        barThickness: 20,
      },
      {
        data: [33 * (1 - barProgress) + 33 * barProgress],
        backgroundColor: '#fff',
        borderColor: '#fff',
        borderWidth: 1,
        borderRadius: {
          topLeft: 0,
          topRight: 5,
          bottomLeft: 0,
          bottomRight: 5
        },
        barThickness: 20,
      }
    ]
  };

  // Donut Chart Data with animation
  const donutData = {
    labels: ['Completed', 'Remaining'],
    datasets: [{
      data: [70 * donutProgress, 30],
      backgroundColor: ['#000', '#fff'],
      borderWidth: 0,
    }]
  };

  const chartOptions = {
    plugins: {
      legend: {
        display: false
      }
    },
    maintainAspectRatio: false,
    responsive: true,
  };

  const barOptions = {
    ...chartOptions,
    indexAxis: 'y',
    scales: {
      x: {
        display: false,
        max: 100,
        beginAtZero: true,
        stacked: true
      },
      y: {
        display: false,
        stacked: true
      }
    }
  };

  const donutOptions = {
    ...chartOptions,
    cutout: '75%',
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        enabled: false
      }
    }
  };

  const cardStyle = {
    border: '1px solid rgba(255, 255, 255, 0.3)',
    borderRadius: '15px',
    padding: '25px',
    backgroundColor: 'rgba(45, 128, 184, 0.3)',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    height: '270px',
    width: '270px'
  };

  // Create array of 9 users for pictogram
  const users = Array(9).fill(null);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      }
    }
  };

  const cardVariants = {
    hidden: { 
      y: 50, 
      opacity: 0 
    },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  const titleVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const numberVariants = {
    hidden: { scale: 0.5, opacity: 0 },
    visible: { 
      scale: 1, 
      opacity: 1,
      transition: {
        delay: 0.5,
        type: 'spring',
        stiffness: 120
      }
    }
  };

  // Handler for the pictogram animation
  const [userProgress, setUserProgress] = useState(0);
  const visibleUsers = Math.ceil(users.length * userProgress);

  return (
    <section id="quelque-chiffre" style={{
      backgroundColor: '#2D8BBA',
      padding: '60px 0',
      minHeight: '60vh',
      overflow: 'hidden'
    }}>
      <div className="container">
        <motion.h2 
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          variants={titleVariants}
          style={{
            color: '#fff',
            fontSize: '3rem',
            textAlign: 'center',
            marginBottom: '60px',
            fontFamily: 'sans-serif'
          }}
        >
          Quelques chiffres...
        </motion.h2>

        <motion.div 
          initial="hidden"
          whileInView="visible"
          viewport={{ once: false, amount: 0.1 }}
          variants={containerVariants}
          style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '30px',
            flexWrap: 'wrap'
          }}
        >
          {/* Gauge Chart Card */}
          <motion.div variants={cardVariants} style={cardStyle}>
            <div style={{ height: '140px', width: '70%', position: 'relative', marginBottom: '25px' }}>
              <Doughnut data={gaugeData} options={chartOptions} />
              <motion.div 
                variants={numberVariants}
                style={{
                  position: 'absolute',
                  top: '70%',
                  left: '30%',
                  transform: 'translate(-50%, -50%)',
                  textAlign: 'center',
                  color: '#fff',
                  fontSize: '1.8rem',
                  fontWeight: 'bold'
                }}
              >
                <Counter value={28} onUpdate={setGaugeProgress} />
              </motion.div>
            </div>
            <p style={{
              color: '#fff',
              textAlign: 'center',
              fontSize: '1.1rem',
              fontFamily: 'sans-serif',
              margin: 0
            }}>
              Nombre de nos offres d'emploi
            </p>
          </motion.div>

          {/* Pictogram Card */}
          <motion.div variants={cardVariants} style={cardStyle}>
            <div style={{
              display: 'flex',
              flexWrap: 'wrap',
              justifyContent: 'center',
              gap: '10px',
              marginTop: '20px',
              marginBottom: '25px',
              flex: 1,
              alignItems: 'center'
            }}>
              {users.map((_, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ 
                    opacity: index < visibleUsers ? 1 : 0.2,
                    scale: index < visibleUsers ? 1 : 0.8,
                    color: index < visibleUsers ? '#000' : 'rgba(0, 0, 0, 0.2)'
                  }}
                  transition={{ 
                    duration: 0.4,
                    delay: index * 0.05
                  }}
                >
                  <FontAwesomeIcon 
                    icon={faUser} 
                    style={{
                      fontSize: '1.8rem',
                      color: index < visibleUsers ? '#000' : 'rgba(0, 0, 0, 0.2)'
                    }}
                  />
                </motion.div>
              ))}
            </div>
            <p style={{
              color: '#fff',
              textAlign: 'center',
              fontSize: '1.1rem',
              fontFamily: 'sans-serif',
              margin: 0
            }}>
              <Counter value={90} onUpdate={setUserProgress} />
              <span style={{ fontSize: '0.9rem', marginLeft: '5px' }}>Candidats recrutés</span>
            </p>
          </motion.div>

          {/* Bar Chart Card */}
          <motion.div variants={cardVariants} style={cardStyle}>
            <div style={{ 
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '15px',
              marginBottom: '25px',
            }}>
              <div style={{ height: '140px', width: '140px', position: 'relative' }}>
                <Bar data={barData} options={barOptions} />
              </div>
              <motion.div 
                variants={numberVariants}
                style={{
                  color: '#fff',
                  fontSize: '1.8rem',
                  fontWeight: 'bold',
                  padding: '5px 0',
                }}
              >
                <Counter value={67} onUpdate={setBarProgress} />
              </motion.div>
            </div>
            <p style={{
              color: '#fff',
              textAlign: 'center',
              fontSize: '1.1rem',
              fontFamily: 'sans-serif',
              margin: 0
            }}>
              Nombre d'entreprises partenaires
            </p>
          </motion.div>

          {/* Donut Chart Card */}
          <motion.div variants={cardVariants} style={cardStyle}>
            <div style={{ 
              height: '140px', 
              width: '70%',
              position: 'relative',
              marginBottom: '25px'
            }}>
              <Doughnut data={donutData} options={donutOptions} />
              <motion.div 
                variants={numberVariants}
                style={{
                  position: 'absolute',
                  top: '30%',
                  left: '30%',
                  transform: 'translate(-50%, -50%)',
                  textAlign: 'center',
                  color: '#fff',
                  fontSize: '1.8rem',
                  fontWeight: 'bold'
                }}
              >
                <Counter value={70} onUpdate={setDonutProgress} />
              </motion.div>
            </div>
            <p style={{
              color: '#fff',
              textAlign: 'center',
              fontSize: '1.1rem',
              fontFamily: 'sans-serif',
              margin: 0
            }}>
              Nombre de recrutements réussis
            </p>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Section6;