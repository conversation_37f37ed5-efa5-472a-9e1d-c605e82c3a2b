import React from 'react';
import styles from '@/styles/SecondBanner.module.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { motion } from 'framer-motion';
import { useRouter } from 'next/router'; 
const Section1 = () => {
    const router = useRouter();
    return (
        <>
            <style jsx global>{`
                @media (max-width: 768px) {
                    /* Override position static from CSS module */
                    #recrutement .mobile-title {
                        position: absolute !important;
                        bottom: 140px !important;
                        left: 50% !important;
                        transform: translateX(-50%) !important;
                        margin-bottom: 0 !important;
                        white-space: nowrap !important;
                    }

                    #recrutement .mobile-button {
                        font-size: 0.85rem !important;
                        padding: 10px 20px !important;
                        height: 40px !important;
                        max-width: 280px !important;
                        position: absolute !important;
                        bottom: 80px !important;
                        left: 50% !important;
                        transform: translateX(-50%) !important;
                        margin: 0 !important;
                    }

                    #recrutement .mobile-arrow {
                        width: 28px !important;
                        height: 28px !important;
                    }

                    #recrutement .mobile-arrow-icon {
                        font-size: 13px !important;
                    }

                    /* Override existing CSS module styles with higher specificity */
                    #recrutement button.mobile-button {
                        font-size: 0.85rem !important;
                        padding: 10px 20px !important;
                        height: 50px !important;
                        position: absolute !important;
                        bottom: 140px !important;
                        left: 50% !important;
                        transform: translateX(-50%) !important;
                        margin: 0 !important;
                    }

                    #recrutement p.mobile-title {
                        position: absolute !important;
                        bottom: 210px !important;
                        left: 50% !important;
                        transform: translateX(-50%) !important;
                        margin-bottom: 0 !important;
                        white-space: nowrap !important;
                    }

                    #recrutement div.mobile-arrow {
                        width: 28px !important;
                        height: 28px !important;
                    }
                }
            `}</style>
            <div id="recrutement" className={styles.bannerSection}>
            <motion.div 
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: false }}
                transition={{ duration: 1.2 }}
                className={styles.backgroundImage}
                style={{
                    backgroundImage: `linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(255,255,255,1) 1%, rgba(255,255,255,0) 40%), url("/assets/img/images/handman.png")`,
                    backgroundSize: 'fill',
                    backgroundPosition: 'center'
                }}
            />
            <motion.div 
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: false }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className={styles.bottomContent}
            >
                <div className={styles.bottomRight}>
                    <motion.p 
                        initial={{ opacity: 0, x: -40 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: false }}
                        transition={{ duration: 0.6, delay: 0.5 }}
                        className={`${styles.bannerTitle} mobile-title`}
                    >
                        VOUS RECRUTEZ ?
                    </motion.p>
                    <motion.button 
                        initial={{ opacity: 0, y: 40 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: false }}
                        transition={{ duration: 0.6, delay: 0.7 }}
                        whileHover={{ 
                            scale: 1.05, 
                            y: -5,
                            boxShadow: '0 8px 15px rgba(0, 0, 0, 0.2)'
                        }}
                       onClick={() => router.push('/profile/job-match')}
                        whileTap={{ scale: 0.98 }}
                        className={`${styles.bannerButton} mobile-button`}
                    >
                        TROUVER LE PROFIL QU'IL VOUS FAUT !
                        <motion.div
                            whileHover={{ rotate: 10 }}
                            className={`${styles.arrowCircle} mobile-arrow`}
                        >
                            <FontAwesomeIcon
                                icon={faArrowRight}
                                className="mobile-arrow-icon"
                                style={{ color: '#000', fontSize: '20px' }}
                            />
                        </motion.div>
                    </motion.button>
                </div>
            </motion.div>
        </div>
        </>
    );
};

export default Section1;
