import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEnvelope, faPhone } from '@fortawesome/free-solid-svg-icons';

const Section7 = () => {
  const categories = [
    { name: 'BÂTIMENT', offers: 1245 },
    { name: 'ADMINISTRATION', offers: 890 },
    { name: 'MÉDICAL', offers: 2309 },
    { name: 'CHR', offers: 4560 },
    { name: 'TERTIAIRE', offers: 4560 },
    { name: 'START UP', offers: 155 }
  ];

  return (
    <section style={{
      backgroundImage: 'url("/images/temporels/clients.jpg")',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      minHeight: '100vh',
      position: 'relative',
      padding: '60px 20px'
    }}>
      {/* Dark overlay */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.6)'
      }} />

      {/* Content */}
      <div style={{
        position: 'relative',
        maxWidth: '1200px',
        margin: '0 auto',
        zIndex: 1
      }}>
        <h2 style={{
          color: '#fff',
          fontSize: '3.5rem',
          fontWeight: '700',
          textAlign: 'center',
          marginBottom: '60px',
          fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif'
        }}>
          NOS CLIENTS RECRUTENT
        </h2>

        {/* Categories Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(3, 1fr)',
          gap: '30px',
          marginBottom: '60px',
          maxWidth: '900px',
          margin: '0 auto 60px'
        }}>
          {categories.map((category, index) => (
            <div key={index} style={{
              border: '1px solid rgba(255, 255, 255, 0.3)',
              borderRadius: '15px',
              padding: '30px',
              backgroundColor: 'transparent',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              textAlign: 'center',
              width: '250px',
              height: '250px',
              margin: '0 auto'
            }}>
              <h3 style={{
                color: '#fff',
                fontSize: '1.5rem',
                marginBottom: '15px',
                minHeight: category.subtitle ? '60px' : '30px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
              }}>
                {category.name}
                {category.subtitle && (
                  <div style={{
                    fontSize: '1rem',
                    opacity: 0.8,
                    marginTop: '5px'
                  }}>
                    {category.subtitle}
                  </div>
                )}
              </h3>
              <div style={{
                color: '#fff',
                fontSize: '3.5rem',
                fontWeight: '700',
                marginBottom: '5px',
                lineHeight: '1'
              }}>
                {category.offers}
              </div>
              <div style={{
                color: '#fff',
                fontSize: '1.1rem',
                opacity: 0.8,
                fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
              }}>
                offres
              </div>
            </div>
          ))}
        </div>

        {/* CTA Button */}
        <div style={{
          display: 'flex',
          justifyContent: 'center'
        }}>
          <button style={{
            background: 'transparent',
            color: '#fff',
            padding: '15px 30px',
            border: '2px solid #fff',
            borderRadius: '30px',
            fontSize: '1.2rem',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '15px',
            transition: 'all 0.3s ease',
            ':hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)'
            }
          }}>
            Vous recrutez ?
            <div style={{ display: 'flex', gap: '10px' }}>
              <FontAwesomeIcon icon={faPhone} style={{ fontSize: '1.2rem' }} />
              <FontAwesomeIcon icon={faEnvelope} style={{ fontSize: '1.2rem' }} />
            </div>
          </button>
        </div>
      </div>
    </section>
  );
};

export default Section7;