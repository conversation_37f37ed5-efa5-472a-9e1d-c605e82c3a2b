import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBriefcase } from '@fortawesome/free-solid-svg-icons';

const Section4 = () => {
  return (
    <section id="engagement" style={{
      display: 'flex',
      minHeight: '100vh',
      backgroundColor: '#fff'
    }}>
      {/* Left Side */}
      <div style={{
        flex: '1',
        padding: '60px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start'
      }}>
        <h2 style={{
          color: '#2D8BBA',
          fontSize: '3rem',
          fontWeight: '700',
          marginBottom: '20px'
        }}>
          ATLANTIS CONSEIL
        </h2>
        
        <h3 style={{
          color: '#000',
          fontSize: '1.8rem',
          marginBottom: '40px'
        }}>
          Recrute dans toute la France et à l'étranger
        </h3>

        <div style={{
          position: 'relative',
          width: '100%',
          height: '400px',
          marginBottom: '40px'
        }}>
          <Image
            src="/images/home/<USER>"
            alt="Map"
            layout="fill"
            objectFit="contain"
          />
        </div>

        <div style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'flex-end'
        }}>
          <Link href="/offres">
            <button style={{
              background: '#2D8BBA',
              color: '#fff',
              padding: '15px 30px',
              border: 'none',
              borderRadius: '30px',
              fontSize: '1.1rem',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              gap: '10px'
            }}>
             
              NOS OFFRES D'EMPLOI
              <FontAwesomeIcon icon={faBriefcase} />
            </button>
          </Link>
        </div>
      </div>

      {/* Right Side */}
      <div style={{
        flex: '1',
        backgroundImage: 'url("/images/temporels/work.jpg")',
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}>
      </div>
    </section>
  );
};

export default Section4;