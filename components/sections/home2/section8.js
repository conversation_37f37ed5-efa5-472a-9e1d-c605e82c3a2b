import React from 'react';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBuilding } from '@fortawesome/free-solid-svg-icons';

const Section8 = () => {
  return (
    <section id="mbr" style={{
      display: 'flex',
      minHeight: '60vh',
      backgroundColor: '#fff',
      padding: '30px 0'
    }}>
      {/* Left Side - 40% */}
      <div style={{
        flex: '0 0 40%',
        padding: '30px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'stretch',
        width: '100%'
      }}>
        <h2 style={{
          color: '#2D8BBA',
          fontSize: '4rem',
          fontWeight: '700',
          marginBottom: '15px',
          lineHeight: '1.2',
          textAlign: 'left',
          width: '100%',
          fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif'
        }}>
          GROUPE ELES
        </h2>

        <h3 style={{
          fontSize: '3rem',
          marginBottom: '20px',
          lineHeight: '1.3',
          color: '#2D8BBA',
          textAlign: 'left',
          width: '100%',
          fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
        }}>
          EXPERTISE<br />
          SAVOIR-FAIRE<br />
          HUMANITÉ
        </h3>

        <p style={{
          color: '#333',
          fontSize: '1.8rem',
          lineHeight: '1.5',
          textAlign: 'left',
          width: '100%'
        }}>
          Le groupe ELES spécialisé dans
          le recrutement et l'intérim de
          Marseille à Monaco propose
          une expertise de recruteurs
          passionnés depuis plus de 20
          ans.
        </p>
      </div>

      {/* Right Side - 60% */}
      <div style={{
        flex: '0 0 60%',
        padding: '30px',
        display: 'flex',
        flexDirection: 'column',
        gap: '20px'
      }}>
        {/* Blue Line */}
        <div style={{
          height: '3px',
          backgroundColor: '#2D8BBA',
          width: '100%',
          marginBottom: '15px'
        }} />

        {/* Cards Container */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '20px'
        }}>
          {/* Card 1 */}
          <div style={{
            display: 'flex',
            gap: '20px',
            alignItems: 'flex-start'
          }}>
            <div style={{
              position: 'relative',
              width: '150px',
              height: '150px',
              borderRadius: '10px',
              overflow: 'hidden'
            }}>
              <Image
                src="/images/temporels/group1.jpg"
                alt="Réseau d'agences"
                layout="fill"
                objectFit="cover"
              />
            </div>
            <div style={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              gap: '15px'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '15px'
              }}>
                <FontAwesomeIcon
                  icon={faBuilding}
                  style={{
                    fontSize: '1.8rem',
                    color: '#2D8BBA'
                  }}
                />
                <h4 style={{
                  fontSize: '1.3rem',
                  color: '#333',
                  margin: '0'
                }}>
                  Réseau d'agences intérim
                </h4>
              </div>
              <p style={{
                color: '#666',
                fontSize: '1rem',
                lineHeight: '1.5'
              }}>
                Nous sommes spécialisés dans les secteurs<br />
                du BTP, de l'industrie, du tertiaire, de<br />
                l'événementiel, de la restauration, de<br />
                l'hôtellerie et du nautisme.
              </p>
            </div>
          </div>

          {/* Blue Line */}
          <div style={{
            height: '2px',
            backgroundColor: '#2D8BBA',
            width: '100%',
            margin: '5px 0'
          }} />

          {/* Card 2 */}
          <div style={{
            display: 'flex',
            gap: '20px',
            alignItems: 'flex-start'
          }}>
            <div style={{
              position: 'relative',
              width: '150px',
              height: '150px',
              borderRadius: '10px',
              overflow: 'hidden'
            }}>
              <Image
                src="/images/temporels/group2.jpg"
                alt="Agence de recrutement Monaco"
                layout="fill"
                objectFit="cover"
              />
            </div>
            <div style={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              gap: '15px'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '15px'
              }}>
                <FontAwesomeIcon
                  icon={faBuilding}
                  style={{
                    fontSize: '1.8rem',
                    color: '#2D8BBA'
                  }}
                />
                <h4 style={{
                  fontSize: '1.3rem',
                  color: '#333',
                  margin: '0'
                }}>
                  Agence de recrutement Monaco
                </h4>
              </div>
              <p style={{
                color: '#666',
                fontSize: '1rem',
                lineHeight: '1.5'
              }}>
                MBRInterim est une agence de recrutement<br />
                implantée au coeur de Monaco. Découvrez<br />
                nos offres d'emploi en CDI, CDD et nos<br />
                missions interim.
              </p>
            </div>
          </div>

          {/* Blue Line */}
          <div style={{
            height: '2px',
            backgroundColor: '#2D8BBA',
            width: '100%',
            margin: '5px 0'
          }} />

          {/* Card 3 */}
          <div style={{
            display: 'flex',
            gap: '20px',
            alignItems: 'flex-start'
          }}>
            <div style={{
              position: 'relative',
              width: '150px',
              height: '150px',
              borderRadius: '10px',
              overflow: 'hidden'
            }}>
              <Image
                src="/images/temporels/group3.jpg"
                alt="Formation et Coaching"
                layout="fill"
                objectFit="cover"
              />
            </div>
            <div style={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              gap: '15px'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '15px'
              }}>
                <FontAwesomeIcon
                  icon={faBuilding}
                  style={{
                    fontSize: '1.8rem',
                    color: '#2D8BBA'
                  }}
                />
                <h4 style={{
                  fontSize: '1.3rem',
                  color: '#333',
                  margin: '0'
                }}>
                  Formation, Conseil et Coaching Professionnel
                </h4>
              </div>
              <p style={{
                color: '#666',
                fontSize: '1rem',
                lineHeight: '1.5'
              }}>
                Découvrez nos formations MERAKI en<br />
                communication, management, qualité de vie<br />
                au travail et relation. Formations. Gestion du<br />
                stress, communication, prévention ...
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section8;