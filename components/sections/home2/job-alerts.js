import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft, faBell, faPlus, faEdit, faTrash, faChartBar } from '@fortawesome/free-solid-svg-icons';
import Layout from '@/components/layout/Layout';
import styles from '@/styles/Profile.module.css';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import axios from 'axios';

export default function JobAlerts() {
  const router = useRouter();
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingAlert, setEditingAlert] = useState(null);
  const [statistics, setStatistics] = useState(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalAlerts: 0
  });
  const [formData, setFormData] = useState({
    title: '',
    keywords: '',
    location: '',
    frequency: 'immediate'
  });

  // Get auth token
  const getAuthToken = () => {
    return localStorage.getItem('token');
  };

  // Get auth headers with fresh token
  const getAuthHeaders = () => {
    const token = getAuthToken();
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  };

  // API base configuration
  const apiConfig = {
    baseURL: 'http://82.165.144.72:5000/api'
  };

  useEffect(() => {
    const token = getAuthToken();
    if (!token) {
      router.push('/signin');
      return;
    }
    
    fetchAlerts();
    fetchStatistics();
  }, []);

  const fetchAlerts = async (page = 1) => {
    try {
      setLoading(true);
      const response = await axios.get(`${apiConfig.baseURL}/job-alerts?page=${page}&limit=10`, {
        headers: getAuthHeaders()
      });
      
      console.log('API Response:', response.data); // Debug log
      
      if (response.data) {
        // Ensure keywords is always an array
        const processedAlerts = (response.data.alerts || []).map(alert => ({
          ...alert,
          keywords: Array.isArray(alert.keywords) ? alert.keywords : 
                   typeof alert.keywords === 'string' ? [alert.keywords] : []
        }));
        
        setAlerts(processedAlerts);
        setPagination(response.data.pagination || {
          currentPage: 1,
          totalPages: 1,
          totalAlerts: 0
        });
      }
    } catch (error) {
      console.error('Error fetching alerts:', error);
      if (error.response?.status === 401) {
        localStorage.removeItem('token');
        router.push('/signin');
        return;
      }
      toast.error('Erreur lors du chargement des alertes');
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await axios.get(`${apiConfig.baseURL}/job-alerts/stats`, {
        headers: getAuthHeaders()
      });
      
      if (response.data) {
        setStatistics(response.data.statistics);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.title || !formData.keywords || !formData.location) {
      toast.error('Veuillez remplir tous les champs obligatoires');
      return;
    }

    try {
      const keywordsArray = formData.keywords.split(',').map(k => k.trim()).filter(k => k);
      
      const alertData = {
        title: formData.title,
        keywords: keywordsArray,
        location: formData.location,
        frequency: formData.frequency
      };

      if (editingAlert) {
        // Update existing alert
        const response = await axios.put(
          `${apiConfig.baseURL}/job-alerts/${editingAlert._id}`,
          alertData,
          { headers: getAuthHeaders() }
        );
        
        if (response.data) {
          toast.success('Alerte mise à jour avec succès !');
          fetchAlerts(pagination.currentPage);
        }
      } else {
        // Create new alert
        const response = await axios.post(
          `${apiConfig.baseURL}/job-alerts`,
          alertData,
          { headers: getAuthHeaders() }
        );
        
        if (response.data) {
          toast.success('Alerte créée avec succès !');
          fetchAlerts(1); // Go to first page to see new alert
        }
      }
      
      // Reset form
      setFormData({ title: '', keywords: '', location: '', frequency: 'immediate' });
      setShowForm(false);
      setEditingAlert(null);
      fetchStatistics(); // Refresh statistics
      
    } catch (error) {
      console.error('Error saving alert:', error);
      if (error.response?.status === 400 && error.response?.data?.message?.includes('maximum')) {
        toast.error('Vous avez atteint la limite maximale de 10 alertes actives');
      } else {
        toast.error('Erreur lors de la sauvegarde de l\'alerte');
      }
    }
  };

  const toggleAlert = async (alertId) => {
    try {
      const response = await axios.patch(
        `${apiConfig.baseURL}/job-alerts/${alertId}/toggle`,
        {},
        { headers: getAuthHeaders() }
      );
      
      if (response.data) {
        toast.success(response.data.message);
        fetchAlerts(pagination.currentPage);
        fetchStatistics();
      }
    } catch (error) {
      console.error('Error toggling alert:', error);
      toast.error('Erreur lors de la modification de l\'alerte');
    }
  };

  const deleteAlert = async (alertId) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette alerte ?')) {
      try {
        const response = await axios.delete(
          `${apiConfig.baseURL}/job-alerts/${alertId}`,
          { headers: getAuthHeaders() }
        );
        
        if (response.data) {
          toast.success('Alerte supprimée avec succès !');
          fetchAlerts(pagination.currentPage);
          fetchStatistics();
        }
      } catch (error) {
        console.error('Error deleting alert:', error);
        toast.error('Erreur lors de la suppression de l\'alerte');
      }
    }
  };

  const editAlert = (alert) => {
    setEditingAlert(alert);
    setFormData({
      title: alert.title,
      keywords: Array.isArray(alert.keywords) ? alert.keywords.join(', ') : alert.keywords || '',
      location: alert.location,
      frequency: alert.frequency
    });
    setShowForm(true);
  };

  const cancelEdit = () => {
    setEditingAlert(null);
    setFormData({ title: '', keywords: '', location: '', frequency: 'immediate' });
    setShowForm(false);
  };

  const getFrequencyLabel = (frequency) => {
    switch (frequency) {
      case 'immediate': return 'Immédiate';
      case 'daily': return 'Quotidienne';
      case 'weekly': return 'Hebdomadaire';
      default: return frequency;
    }
  };

  return (
    <Layout headerStyle={2} footerStyle={3}>
      <style jsx>{`
        @media (max-width: 767.98px) {
          .alert-card {
            flex-direction: column !important;
            align-items: stretch !important;
          }
          .alert-content {
            margin-bottom: 16px !important;
          }
          .alert-actions {
            margin-left: 0 !important;
            justify-content: center !important;
            flex-wrap: wrap !important;
            gap: 8px !important;
          }
          .alert-header {
            flex-direction: column !important;
            align-items: flex-start !important;
            gap: 8px !important;
          }
          .alert-title {
            margin-bottom: 8px !important;
          }
          .alert-status {
            align-self: flex-start !important;
          }
          .alert-stats {
            flex-direction: column !important;
            gap: 8px !important;
          }
        }
      `}</style>
      <ToastContainer position="top-right" autoClose={3000} />
      <div className="container" style={{ marginTop: '150px', marginBottom: '120px', minHeight: '70vh' }}>
        <div style={{
          background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
          borderRadius: '24px',
          padding: '3px',
          marginBottom: '30px',
          boxShadow: '0 25px 50px rgba(45, 139, 186, 0.15)'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
            borderRadius: '21px',
            padding: '50px',
            boxShadow: '0 30px 60px rgba(0,0,0,0.08)'
          }}>
            {/* Mobile buttons row */}
            <div className="d-md-none d-flex justify-content-between align-items-center mb-3">
              <button
                onClick={() => router.back()}
                className="btn-link p-2 d-flex align-items-center justify-content-center"
                style={{
                  background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                  border: 'none',
                  borderRadius: '12px',
                  color: 'white',
                  padding: '12px',
                  textDecoration: 'none',
                  fontSize: '16px',
                  fontWeight: '600',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 8px 25px rgba(45, 139, 186, 0.3)',
                  minWidth: '48px',
                  minHeight: '48px'
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = 'translateY(-2px)';
                  e.target.style.boxShadow = '0 12px 35px rgba(45, 139, 186, 0.4)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 8px 25px rgba(45, 139, 186, 0.3)';
                }}
              >
                <FontAwesomeIcon icon={faArrowLeft} />
              </button>

              <button
                onClick={() => {
                  if (showForm && editingAlert) {
                    cancelEdit();
                  } else {
                    setShowForm(!showForm);
                  }
                }}
                className="btn-link p-2 d-flex align-items-center justify-content-center"
                style={{
                  background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                  border: 'none',
                  borderRadius: '12px',
                  color: 'white',
                  padding: '12px',
                  textDecoration: 'none',
                  fontSize: '16px',
                  fontWeight: '600',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 8px 20px rgba(45, 139, 186, 0.3)',
                  minWidth: '48px',
                  minHeight: '48px'
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = 'translateY(-2px)';
                  e.target.style.boxShadow = '0 12px 25px rgba(45, 139, 186, 0.4)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 8px 20px rgba(45, 139, 186, 0.3)';
                }}
              >
                <FontAwesomeIcon icon={faPlus} />
              </button>
            </div>

            {/* Desktop layout */}
            <div className="d-none d-md-block">
              <button
                onClick={() => router.back()}
                className="btn-link p-2 mb-4 d-flex align-items-center"
                style={{
                  background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                  border: 'none',
                  borderRadius: '12px',
                  color: 'white',
                  padding: '14px 28px',
                  textDecoration: 'none',
                  fontSize: '16px',
                  fontWeight: '600',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 8px 25px rgba(45, 139, 186, 0.3)'
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = 'translateY(-2px)';
                  e.target.style.boxShadow = '0 12px 35px rgba(45, 139, 186, 0.4)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 8px 25px rgba(45, 139, 186, 0.3)';
                }}
              >
                <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
                Retour au profil
              </button>

              <div className="d-flex justify-content-between align-items-center mb-4">
                <div>
                  <h1 style={{
                    fontSize: '36px',
                    fontWeight: '800',
                    background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    marginBottom: '12px',
                    letterSpacing: '-0.5px'
                  }}>
                    <FontAwesomeIcon icon={faBell} className="me-3" style={{ color: '#2D8BBA' }} />
                    Mes Alertes Emploi
                  </h1>
                  <p style={{
                    color: '#64748b',
                    fontSize: '18px',
                    marginBottom: '0',
                    fontWeight: '500'
                  }}>
                    Recevez des alertes par email pour les nouvelles offres correspondant à vos critères
                  </p>
                  {statistics && (
                    <div className="mt-4 d-flex gap-6 flex-wrap">
                      <div style={{
                        background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                        padding: '12px 20px',
                        borderRadius: '16px',
                        border: '2px solid rgba(45, 139, 186, 0.1)',
                        boxShadow: '0 4px 15px rgba(45, 139, 186, 0.1)'
                      }}>
                        <span style={{ color: '#2D8BBA', fontWeight: '700', fontSize: '16px' }}>
                          <FontAwesomeIcon icon={faChartBar} className="me-2" />
                          {statistics.totalAlerts} alertes • {statistics.activeAlerts} actives • {statistics.totalNotifications} notifications
                        </span>
                      </div>
                    </div>
                  )}
                </div>
                <button
                  onClick={() => {
                    if (showForm && editingAlert) {
                      cancelEdit();
                    } else {
                      setShowForm(!showForm);
                    }
                  }}
                  style={{
                    background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                    border: 'none',
                    borderRadius: '12px',
                    color: 'white',
                    padding: '12px 24px',
                    fontSize: '16px',
                    fontWeight: '600',
                    boxShadow: '0 8px 20px rgba(45, 139, 186, 0.3)',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-2px)';
                    e.target.style.boxShadow = '0 12px 25px rgba(45, 139, 186, 0.4)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0)';
                    e.target.style.boxShadow = '0 8px 20px rgba(45, 139, 186, 0.3)';
                  }}
                >
                  <FontAwesomeIcon icon={faPlus} className="me-2" />
                  {showForm ? (editingAlert ? 'Annuler modification' : 'Annuler') : 'Nouvelle alerte'}
                </button>
              </div>
            </div>

            {/* Mobile title section */}
            <div className="d-md-none mb-4">
              <h1 style={{
                fontSize: '28px',
                fontWeight: '800',
                background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                marginBottom: '12px',
                letterSpacing: '-0.5px',
                textAlign: 'center'
              }}>
                <FontAwesomeIcon icon={faBell} className="me-3" style={{ color: '#2D8BBA' }} />
                Mes Alertes Emploi
              </h1>
              <p style={{
                color: '#64748b',
                fontSize: '16px',
                marginBottom: '0',
                fontWeight: '500',
                textAlign: 'center'
              }}>
                Recevez des alertes par email pour les nouvelles offres correspondant à vos critères
              </p>
              {statistics && (
                <div className="mt-4 d-flex justify-content-center">
                  <div style={{
                    background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                    padding: '12px 20px',
                    borderRadius: '16px',
                    border: '2px solid rgba(45, 139, 186, 0.1)',
                    boxShadow: '0 4px 15px rgba(45, 139, 186, 0.1)',
                    textAlign: 'center'
                  }}>
                    <span style={{ color: '#2D8BBA', fontWeight: '700', fontSize: '14px' }}>
                      <FontAwesomeIcon icon={faChartBar} className="me-2" />
                      {statistics.totalAlerts} alertes • {statistics.activeAlerts} actives • {statistics.totalNotifications} notifications
                    </span>
                  </div>
                </div>
              )}
            </div>
          
          {showForm && (
            <div style={{
              background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
              borderRadius: '20px',
              padding: '40px',
              marginBottom: '30px',
              boxShadow: '0 15px 35px rgba(45, 139, 186, 0.1)',
              border: '2px solid rgba(45, 139, 186, 0.1)'
            }}>
              <div>
                <h5 style={{
                  fontSize: '24px',
                  fontWeight: '700',
                  color: '#2D8BBA',
                  marginBottom: '24px'
                }}>
                  {editingAlert ? 'Modifier l\'alerte' : 'Créer une nouvelle alerte'}
                </h5>
                <form onSubmit={handleSubmit}>
                  <div className="row mb-4">
                    <div className="col-md-12 mb-4">
                      <label htmlFor="title" style={{
                        display: 'block',
                        marginBottom: '8px',
                        fontWeight: '600',
                        color: '#374151',
                        fontSize: '16px'
                      }}>Titre de l'alerte *</label>
                      <input
                        type="text"
                        style={{
                          width: '100%',
                          padding: '14px 18px',
                          border: '2px solid #e2e8f0',
                          borderRadius: '12px',
                          fontSize: '16px',
                          transition: 'all 0.3s ease',
                          outline: 'none'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = '#2D8BBA';
                          e.target.style.boxShadow = '0 0 0 4px rgba(45, 139, 186, 0.1)';
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = '#e2e8f0';
                          e.target.style.boxShadow = 'none';
                        }}
                        id="title"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        placeholder="Ex: Développeur JavaScript Senior"
                        required
                        maxLength={100}
                      />
                    </div>
                    <div className="col-md-5 mb-4">
                      <label htmlFor="keywords" style={{
                        display: 'block',
                        marginBottom: '8px',
                        fontWeight: '600',
                        color: '#374151',
                        fontSize: '16px'
                      }}>Mots-clés * (séparés par des virgules)</label>
                      <input
                        type="text"
                        style={{
                          width: '100%',
                          padding: '14px 18px',
                          border: '2px solid #e2e8f0',
                          borderRadius: '12px',
                          fontSize: '16px',
                          transition: 'all 0.3s ease',
                          outline: 'none'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = '#2D8BBA';
                          e.target.style.boxShadow = '0 0 0 4px rgba(45, 139, 186, 0.1)';
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = '#e2e8f0';
                          e.target.style.boxShadow = 'none';
                        }}
                        id="keywords"
                        name="keywords"
                        value={formData.keywords}
                        onChange={handleInputChange}
                        placeholder="Ex: javascript, Métier, Secteur, Mot-clé... react, node.js"
                        required
                      />
                      <small style={{ color: '#6b7280', fontSize: '14px', marginTop: '4px', display: 'block' }}>
                        Séparez les mots-clés par des virgules
                      </small>
                    </div>
                    <div className="col-md-4 mb-4">
                      <label htmlFor="location" style={{
                        display: 'block',
                        marginBottom: '8px',
                        fontWeight: '600',
                        color: '#374151',
                        fontSize: '16px'
                      }}>Localisation *</label>
                      <input
                        type="text"
                        style={{
                          width: '100%',
                          padding: '14px 18px',
                          border: '2px solid #e2e8f0',
                          borderRadius: '12px',
                          fontSize: '16px',
                          transition: 'all 0.3s ease',
                          outline: 'none'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = '#2D8BBA';
                          e.target.style.boxShadow = '0 0 0 4px rgba(45, 139, 186, 0.1)';
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = '#e2e8f0';
                          e.target.style.boxShadow = 'none';
                        }}
                        id="location"
                        name="location"
                        value={formData.location}
                        onChange={handleInputChange}
                        placeholder="Ex: Monaco, Nice, etc."
                        required
                        maxLength={100}
                      />
                    </div>
                    <div className="col-md-3">
                      <label htmlFor="frequency" style={{
                        display: 'block',
                        marginBottom: '8px',
                        fontWeight: '600',
                        color: '#374151',
                        fontSize: '16px'
                      }}>Fréquence</label>
                      <select
                        style={{
                          width: '100%',
                          padding: '14px 18px',
                          border: '2px solid #e2e8f0',
                          borderRadius: '12px',
                          fontSize: '16px',
                          transition: 'all 0.3s ease',
                          outline: 'none',
                          background: 'white'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = '#2D8BBA';
                          e.target.style.boxShadow = '0 0 0 4px rgba(45, 139, 186, 0.1)';
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = '#e2e8f0';
                          e.target.style.boxShadow = 'none';
                        }}
                        id="frequency"
                        name="frequency"
                        value={formData.frequency}
                        onChange={handleInputChange}
                      >
                        <option value="immediate">Immédiate</option>
                        <option value="daily">Quotidienne</option>
                        <option value="weekly">Hebdomadaire</option>
                      </select>
                    </div>
                  </div>
                  <div className="d-flex justify-content-end gap-3">
                    {editingAlert && (
                      <button
                        type="button"
                        style={{
                          background: 'white',
                          border: '2px solid #6c757d',
                          borderRadius: '12px',
                          color: '#6c757d',
                          padding: '12px 24px',
                          fontSize: '16px',
                          fontWeight: '600',
                          cursor: 'pointer',
                          transition: 'all 0.3s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.background = '#6c757d';
                          e.target.style.color = 'white';
                          e.target.style.transform = 'translateY(-1px)';
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.background = 'white';
                          e.target.style.color = '#6c757d';
                          e.target.style.transform = 'translateY(0)';
                        }}
                        onClick={cancelEdit}
                      >
                        Annuler
                      </button>
                    )}
                    <button
                      type="submit"
                      style={{
                        background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                        border: 'none',
                        borderRadius: '12px',
                        color: 'white',
                        padding: '12px 32px',
                        fontSize: '16px',
                        fontWeight: '600',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        boxShadow: '0 8px 20px rgba(45, 139, 186, 0.3)'
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.transform = 'translateY(-2px)';
                        e.target.style.boxShadow = '0 12px 25px rgba(45, 139, 186, 0.4)';
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.transform = 'translateY(0)';
                        e.target.style.boxShadow = '0 8px 20px rgba(45, 139, 186, 0.3)';
                      }}
                    >
                      {editingAlert ? 'Mettre à jour l\'alerte' : 'Enregistrer l\'alerte'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
          
          {loading ? (
            <div className="text-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Chargement...</span>
              </div>
            </div>
          ) : alerts.length === 0 ? (
            <div style={{
              background: 'white',
              borderRadius: '20px',
              padding: '60px 40px',
              textAlign: 'center',
              boxShadow: '0 15px 35px rgba(0, 0, 0, 0.08)',
              border: '1px solid #e5e7eb'
            }}>
              <div style={{
                background: 'linear-gradient(135deg, rgba(45, 139, 186, 0.1) 0%, rgba(26, 95, 122, 0.1) 100%)',
                borderRadius: '50%',
                width: '80px',
                height: '80px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 24px auto'
              }}>
                <FontAwesomeIcon icon={faBell} style={{ fontSize: '32px', color: '#2D8BBA' }} />
              </div>
              <h5 style={{
                fontSize: '24px',
                fontWeight: '700',
                color: '#374151',
                marginBottom: '12px'
              }}>Aucune alerte configurée</h5>
              <p style={{
                color: '#6b7280',
                fontSize: '16px',
                marginBottom: '32px',
                maxWidth: '400px',
                margin: '0 auto 32px auto'
              }}>
                Vous n'avez pas encore configuré d'alerte emploi.
              </p>
              <button
                onClick={() => setShowForm(true)}
                style={{
                  background: 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)',
                  border: 'none',
                  borderRadius: '12px',
                  color: 'white',
                  padding: '16px 32px',
                  fontSize: '16px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 8px 20px rgba(45, 139, 186, 0.3)'
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = 'translateY(-2px)';
                  e.target.style.boxShadow = '0 12px 25px rgba(45, 139, 186, 0.4)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 8px 20px rgba(45, 139, 186, 0.3)';
                }}
              >
                <FontAwesomeIcon icon={faPlus} className="me-2" />
                Créer une alerte
              </button>
            </div>
          ) : (
            <div>
              <div style={{
                background: 'white',
                borderRadius: '20px',
                padding: '0',
                boxShadow: '0 15px 35px rgba(0, 0, 0, 0.08)',
                border: '1px solid #e5e7eb',
                overflow: 'hidden'
              }}>
                {alerts.map((alert, index) => (
                  <div key={alert._id} style={{
                    padding: '24px 32px',
                    borderBottom: index < alerts.length - 1 ? '1px solid #e5e7eb' : 'none',
                    background: alert.isActive 
                      ? 'linear-gradient(135deg, rgba(45, 139, 186, 0.02) 0%, rgba(26, 95, 122, 0.02) 100%)'
                      : '#ffffff',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = 'linear-gradient(135deg, rgba(45, 139, 186, 0.05) 0%, rgba(26, 95, 122, 0.05) 100%)';
                    e.currentTarget.style.transform = 'translateY(-1px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = alert.isActive 
                      ? 'linear-gradient(135deg, rgba(45, 139, 186, 0.02) 0%, rgba(26, 95, 122, 0.02) 100%)'
                      : '#ffffff';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}>
                    <div className="d-flex justify-content-between align-items-start alert-card">
                      <div style={{ flex: '1' }} className="alert-content">
                        <div className="d-flex justify-content-between align-items-center mb-3 alert-header">
                          <h6 style={{
                            fontSize: '18px',
                            fontWeight: '700',
                            color: '#374151',
                            margin: '0'
                          }} className="alert-title">{alert.title}</h6>
                          <span style={{
                            background: alert.isActive
                              ? 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
                              : '#6c757d',
                            color: 'white',
                            padding: '6px 12px',
                            borderRadius: '12px',
                            fontSize: '12px',
                            fontWeight: '600'
                          }} className="alert-status">
                            {alert.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                        <p style={{
                          margin: '0 0 12px 0',
                          color: '#6b7280',
                          fontSize: '14px'
                        }}>
                          <strong style={{ color: '#374151' }}>Mots-clés:</strong> {Array.isArray(alert.keywords) ? alert.keywords.join(', ') : alert.keywords || 'Aucun mot-clé'}
                        </p>
                        <div style={{
                          fontSize: '14px',
                          color: '#6b7280',
                          marginBottom: '16px'
                        }}>
                          <FontAwesomeIcon icon={faBell} className="me-2" style={{ color: '#2D8BBA' }} />
                          {alert.location} • {getFrequencyLabel(alert.frequency)}
                        </div>
                        <div style={{
                          display: 'flex',
                          gap: '24px',
                          fontSize: '13px',
                          color: '#6b7280'
                        }} className="alert-stats">
                          <span style={{
                            background: '#f3f4f6',
                            padding: '4px 8px',
                            borderRadius: '8px'
                          }}>
                            <FontAwesomeIcon icon={faBell} className="me-1" style={{ color: '#2D8BBA' }} />
                            {alert.totalNotificationsSent} notifications
                          </span>
                          <span style={{
                            background: '#f3f4f6',
                            padding: '4px 8px',
                            borderRadius: '8px'
                          }}>
                            <FontAwesomeIcon icon={faChartBar} className="me-1" style={{ color: '#10b981' }} />
                            {alert.matchedPostsCount} correspondances
                          </span>
                          {alert.lastNotificationSent && (
                            <span style={{
                              background: '#f3f4f6',
                              padding: '4px 8px',
                              borderRadius: '8px'
                            }}>
                              Dernière: {new Date(alert.lastNotificationSent).toLocaleDateString('fr-FR')}
                            </span>
                          )}
                        </div>
                      </div>
                      <div style={{ marginLeft: '24px' }} className="alert-actions">
                        <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
                          <div style={{
                            background: '#f8fafc',
                            borderRadius: '12px',
                            padding: '8px 12px',
                            border: '1px solid #e2e8f0'
                          }}>
                            <input
                              type="checkbox"
                              id={`alert-${alert._id}`}
                              checked={alert.isActive}
                              onChange={() => toggleAlert(alert._id)}
                              style={{
                                width: '18px',
                                height: '18px',
                                accentColor: '#2D8BBA',
                                cursor: 'pointer'
                              }}
                            />
                          </div>
                          <button 
                            style={{
                              background: 'white',
                              border: '2px solid #2D8BBA',
                              borderRadius: '10px',
                              color: '#2D8BBA',
                              padding: '10px 12px',
                              fontSize: '14px',
                              cursor: 'pointer',
                              transition: 'all 0.3s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.target.style.background = '#2D8BBA';
                              e.target.style.color = 'white';
                              e.target.style.transform = 'translateY(-2px)';
                              e.target.style.boxShadow = '0 4px 12px rgba(45, 139, 186, 0.3)';
                            }}
                            onMouseLeave={(e) => {
                              e.target.style.background = 'white';
                              e.target.style.color = '#2D8BBA';
                              e.target.style.transform = 'translateY(0)';
                              e.target.style.boxShadow = 'none';
                            }}
                            onClick={() => editAlert(alert)}
                            title="Modifier"
                          >
                            <FontAwesomeIcon icon={faEdit} />
                          </button>
                          <button 
                            style={{
                              background: '#ef4444',
                              border: '2px solid #dc2626',
                              borderRadius: '10px',
                              color: 'white',
                              padding: '10px 12px',
                              fontSize: '14px',
                              cursor: 'pointer',
                              transition: 'all 0.3s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.target.style.background = '#dc2626';
                              e.target.style.transform = 'translateY(-2px)';
                              e.target.style.boxShadow = '0 4px 12px rgba(220, 38, 38, 0.3)';
                            }}
                            onMouseLeave={(e) => {
                              e.target.style.background = '#ef4444';
                              e.target.style.transform = 'translateY(0)';
                              e.target.style.boxShadow = 'none';
                            }}
                            onClick={() => deleteAlert(alert._id)}
                            title="Supprimer"
                          >
                            <FontAwesomeIcon icon={faTrash} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Enhanced Pagination */}
              {pagination.totalPages > 1 && (
                <div className="d-flex justify-content-center mt-5">
                  <nav>
                    <ul className="pagination" style={{ gap: '8px' }}>
                      <li className={`page-item ${!pagination.hasPrev ? 'disabled' : ''}`}>
                        <button 
                          style={{
                            background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                            border: '2px solid #e2e8f0',
                            borderRadius: '12px',
                            padding: '12px 20px',
                            fontWeight: '600',
                            color: '#475569',
                            transition: 'all 0.3s ease',
                            cursor: !pagination.hasPrev ? 'not-allowed' : 'pointer'
                          }}
                          onClick={() => fetchAlerts(pagination.currentPage - 1)}
                          disabled={!pagination.hasPrev}
                          onMouseEnter={(e) => {
                            if (!e.target.disabled) {
                              e.target.style.background = 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)';
                              e.target.style.color = 'white';
                              e.target.style.borderColor = '#2D8BBA';
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (!e.target.disabled) {
                              e.target.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';
                              e.target.style.color = '#475569';
                              e.target.style.borderColor = '#e2e8f0';
                            }
                          }}
                        >
                          Précédent
                        </button>
                      </li>
                      {[...Array(pagination.totalPages)].map((_, index) => (
                        <li key={index + 1} className={`page-item ${pagination.currentPage === index + 1 ? 'active' : ''}`}>
                          <button 
                            style={{
                              background: pagination.currentPage === index + 1 
                                ? 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)' 
                                : 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                              border: '2px solid',
                              borderColor: pagination.currentPage === index + 1 ? '#2D8BBA' : '#e2e8f0',
                              borderRadius: '12px',
                              padding: '12px 16px',
                              fontWeight: '600',
                              color: pagination.currentPage === index + 1 ? 'white' : '#475569',
                              transition: 'all 0.3s ease',
                              minWidth: '48px',
                              cursor: 'pointer'
                            }}
                            onClick={() => fetchAlerts(index + 1)}
                            onMouseEnter={(e) => {
                              if (pagination.currentPage !== index + 1) {
                                e.target.style.background = 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)';
                                e.target.style.color = 'white';
                                e.target.style.borderColor = '#2D8BBA';
                              }
                            }}
                            onMouseLeave={(e) => {
                              if (pagination.currentPage !== index + 1) {
                                e.target.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';
                                e.target.style.color = '#475569';
                                e.target.style.borderColor = '#e2e8f0';
                              }
                            }}
                          >
                            {index + 1}
                          </button>
                        </li>
                      ))}
                      <li className={`page-item ${!pagination.hasNext ? 'disabled' : ''}`}>
                        <button 
                          style={{
                            background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                            border: '2px solid #e2e8f0',
                            borderRadius: '12px',
                            padding: '12px 20px',
                            fontWeight: '600',
                            color: '#475569',
                            transition: 'all 0.3s ease',
                            cursor: !pagination.hasNext ? 'not-allowed' : 'pointer'
                          }}
                          onClick={() => fetchAlerts(pagination.currentPage + 1)}
                          disabled={!pagination.hasNext}
                          onMouseEnter={(e) => {
                            if (!e.target.disabled) {
                              e.target.style.background = 'linear-gradient(135deg, #2D8BBA 0%, #1a5f7a 100%)';
                              e.target.style.color = 'white';
                              e.target.style.borderColor = '#2D8BBA';
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (!e.target.disabled) {
                              e.target.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';
                              e.target.style.color = '#475569';
                              e.target.style.borderColor = '#e2e8f0';
                            }
                          }}
                        >
                          Suivant
                        </button>
                      </li>
                    </ul>
                  </nav>
                </div>
              )}
            </div>
          )}
          </div>
        </div>
      </div>
    </Layout>
  );
}
