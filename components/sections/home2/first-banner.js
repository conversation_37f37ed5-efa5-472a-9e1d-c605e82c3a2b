import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import styles from '@/styles/FirstBanner.module.css';

const FirstBanner = () => {
  return (
    <section 
      className={styles.bannerSection}
      style={{
        backgroundImage: 'url("/assets/img/images/bloc2.png")',
      }}
    >
      <motion.div 
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: false }}
        transition={{ duration: 1 }}
        className={styles.overlay}
      ></motion.div>

      <div className={styles.content}>
        <div className={`container ${styles.container}`}>
          {/* Left Content */}
          <motion.div 
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: false }}
            transition={{ duration: 0.8 }}
            className={styles.leftContent}
          >
            <motion.h1 
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: false }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className={styles.title}
            >
              ÊTES-VOUS <br/>
              PRÊT ?
            </motion.h1>
            <Link href="/offres">
              <motion.div 
                className={`${styles.button} inline-flex items-center justify-center px-5 py-2 transition-all duration-300`}
              >
                <span>Offres d&apos;emploi</span>
                <i className="fas fa-arrow-right px-2 ml-2"></i>
              </motion.div>
            </Link>
          </motion.div>

          {/* Right Content */}
          <motion.div 
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: false }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className={styles.rightContent}
          >
            <motion.p 
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: false }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className={styles.description}
            >
              Chez Atlantis, nous valorisons nos talents qui font bouger les lignes. Que vous soyez agents de maitrise, cadre confirmé ou
              en poste de management, nous vous accompagnons dans chaque étape de votre recherche d&apos;emploi.
            </motion.p>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default FirstBanner;
