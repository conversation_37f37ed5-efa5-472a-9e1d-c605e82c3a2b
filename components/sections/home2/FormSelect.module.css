.formColumn {
    padding: 0 8px;
}

.formColumn:first-child {
    padding-left: 0;
}

.formColumn:last-child {
    padding-right: 0;
}

.inputWrapper {
    height: 60px;
    display: flex;
    align-items: center;
    background: #fff;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
    border-radius: 8px;
}

.inputWrapper:hover {
    border-color: #2D8BBA;
}

.iconContainer {
    width: 48px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.icon {
    color: #2D8BBA;
    font-size: 18px;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.inputWrapper:hover .icon {
    opacity: 1;
}

.input {
    flex: 1;
    height: 100%;
    border: none;
    background: transparent;
    padding: 0 16px;
    font-size: 14px;
    color: #495057;
    outline: none;
}

.input::placeholder {
    color: #adb5bd;
}

.select {
    flex: 1;
    height: 100%;
}

.selectControl {
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
    min-height: 58px !important;
}

.searchButton {
    height: 60px;
    width: auto;
    border: none;
    background: linear-gradient(135deg, #2D8BBA 0%, #2d7aa8 100%);
    color: white;
    font-weight: 500;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    letter-spacing: 0.3px;
    gap: 10px;
    text-transform: uppercase;
    padding: 0 24px;
}

.searchButton::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2d7aa8 0%, #2D8BBA 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.searchButton:hover::before {
    opacity: 1;
}

.searchButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(52, 133, 182, 0.25);
}

.searchButton:active {
    transform: translateY(1px);
}

.searchButton span {
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.searchButtonIcon {
    font-size: 16px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.searchButtonText {
    position: relative;
    z-index: 1;
    margin-left: 8px;
    font-weight: 500;
    display: none; /* Hidden by default (desktop) */
}

.searchButton:hover .searchButtonIcon {
    transform: translateX(4px);
}

@media (max-width: 768px) {
    .formColumn {
        padding: 0;
    }

    .formColumn + .formColumn {
        margin-top: 12px;
    }

    .inputWrapper {
        border-radius: 8px;
    }

    .searchButtonWrapper {
        display: flex;
        justify-content: center;
        margin-top: 12px;
    }

    .searchButton {
        border-radius: 8px;
        width: auto;
        min-width: 150px;
        padding: 0 20px;
    }

    .searchButtonText {
        display: inline; /* Show text on mobile */
    }

    .searchButton:hover .searchButtonIcon {
        transform: none; /* Remove icon animation on mobile */
    }
}
