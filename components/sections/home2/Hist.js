import React from 'react';
import styles from '@/styles/Historique.module.css';

const Hist = () => {
  return (
    <section id="historique" className={styles.historique}>
      <div className="container">
      
        <h2 className={styles.mainTitle}>Notre histoire, une promesse d'avenir</h2>

        <div className={styles.timelineLine}></div>

        {/* Row 1: Image Left, Text Right */}
        <div className={styles.row}>
          <div className={styles.image}>
            <img
              src="/images/home/<USER>"
              alt="History Image 1"
            />
          </div>
          <div className={styles.textCard}>
            <p>
              <h5>Une vision durable du Recrutement</h5>
            <strong>Atlantis Conseil</strong> Atlantis Conseil voit le jour en 2014 avec une ambition claire :
proposer à nos partenaires une approche durable, innovante et humaine du recrutement.
Une vision fondée sur la performance et la confiance.
            </p>
          </div>
          <div className={styles.timeline}>
            <div className={styles.timelineDot}></div>
            <span className={styles.yearLeft}>2014</span>
          </div>
        </div>

        {/* Row 2: Text Left, Image Right */}
        <div className={`${styles.row} ${styles.reverse}`}>
          <div className={styles.image}>
            <img
              src="/images/home/<USER>"
              alt="History Image 2"
            />
          </div>
          <div className={styles.textCard}>
            <p>
              <h5>Un cap franchi, des résultats probants</h5>
              En 2019, nous fêtons nos 5 ans avec des résultats au plus haut :
excellence de la qualité perçue, fidélisation durable des talents, et des partenariats renforcés.
Une étape clé dans notre développement.
            </p>
          </div>
          <div className={styles.timeline}>
            <div className={styles.timelineDot}></div>
            <span className={styles.yearRight}>2019</span>
          </div>
        </div>

        {/* Row 3: Image Left, Text Right */}
        <div className={styles.row}>
          <div className={styles.image}>
            <img
              src="/images/home/<USER>"
              alt="History Image 3"
            />
          </div>
          <div className={styles.textCard}>
            <p>
              <h5>Rebond et consolidation post-crise</h5>
              Face aux défis post-Covid, Atlantis Conseil accélère sa transformation.
De nouveaux talents intègrent nos équipes,
consolidant notre capacité à offrir un accompagnement structuré, agile et centré sur l'humain.
            </p>
          </div>
          <div className={styles.timeline}>
            <div className={styles.timelineDot}></div>
            <span className={styles.yearLeft}>202</span>
          </div>
        </div>

        {/* Row 4: Text Left, Image Right */}
        <div className={`${styles.row} ${styles.reverse}`}>
          <div className={styles.image}>
            <img
              src="/images/home/<USER>"
              alt="History Image 4"
            />
          </div>
          <div className={styles.textCard}>
            <p>
              <h5>Une dynamique de groupe tournée vers l'avenir</h5>
            <strong> Atlantis Conseil</strong> s'inscrit désormais dans le <strong> Groupe ELES</strong>,
aux côtés de <strong> Talent Job PACA</strong> et <strong> MERAKI</strong>.
Ensemble, nous pilotons trois expertises complémentaires au service de l'emploi :
La durabilité de l'emploi, <strong>la flexibilité</strong> et <strong> la performance</strong> et <strong> cohésion d'équipe</strong>.
            </p>
          </div>
          <div className={styles.timeline}>
            <div className={styles.timelineDot}></div>
            <span className={styles.yearRight}>2026</span>
          </div>
        </div>
      </div>

      <style jsx>{`
        @font-face {
          font-family: 'HouschkaHead2020';
          src: url('/fonts/HouschkaHead2020.woff2') format('woff2');
          font-weight: normal;
          font-style: normal;
        }

        .${styles.historique} {
          background: linear-gradient(180deg, #fff 0%, #3485B6 100%);
          padding: 100px 0;
          position: relative;
          overflow: hidden;
        }

        .${styles.mainTitle} {
          font-family: 'HouschkaHead2020', serif;
          font-size: 2.5rem;
          color: #3485B6;
          margin-bottom: 50px;
          text-align: center;
        }

        .${styles.customTitle} {
          font-family: 'Playfair Display', serif;
          font-size: 2.5rem;
          color: #3485B6;
          text-align: center;
          margin-bottom: 20px;
        }

        .${styles.timelineLine} {
          position: absolute;
          left: 50%;
          top: 200px;
          bottom: 100px;
          width: 2px;
          background: #3485B6;
          transform: translateX(-50%);
          border-radius: 10px;
        }

        .${styles.row} {
          display: flex;
          align-items: center;
          margin: 50px 0;
          position: relative;
          padding: 0 100px;
          gap: 180px;
        }

        .${styles.image} {
          flex: 0 0 300px;
          max-width: 300px;
          perspective: 1000px;
        }

        .${styles.image} img {
          width: 100%;
          height: auto;
          border-radius: 15px;
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          transition: transform 0.6s ease-in-out;
          transform-style: preserve-3d;
          backface-visibility: visible;
        }

        .${styles.image}:hover img {
          transform: rotateY(180deg);
        }

        .${styles.textCard} {
          flex: 0 0 350px;
          padding: 30px;
          background: rgba(52, 133, 182, 0.85);
          backdrop-filter: blur(10px);
          border-radius: 15px;
          position: relative;
          z-index: 2;
          margin: 0 80px;
          border: 1px solid rgba(255, 255, 255, 0.3);
          box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }

        .${styles.row}.${styles.reverse} .${styles.textCard} {
          margin: 0 80px;
        }

        .${styles.textCard} p {
          margin: 0;
          font-size: 16px;
          line-height: 1.8;
          color: #fff;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          font-weight: 500;
        }

        .${styles.textCard}:hover {
          background: rgba(52, 133, 182, 0.95);
          transform: translateY(-5px);
          transition: all 0.3s ease;
          box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.45);
        }

        .${styles.timeline} {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          z-index: 3;
          width: 40px;
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .${styles.timelineDot} {
          width: 20px;
          height: 20px;
          background: #3485B6;
          border-radius: 50%;
          margin: 10px auto;
          border: 3px solid #fff;
        }

        .${styles.yearLeft}, .${styles.yearRight} {
          position: absolute;
          color: #fff;
          font-weight: bold;
          font-size: 1.2rem;
          white-space: nowrap;
          top: 50%;
          transform: translateY(-50%);
        }

        .${styles.yearLeft} {
          right: calc(100% + 50px);
        }

        .${styles.yearRight} {
          left: calc(100% + 50px);
        }

        @media (max-width: 768px) {
          .${styles.row} {
            flex-direction: column;
            padding: 0 20px;
            margin: 30px 0;
            gap: 20px;
          }

          .${styles.row}.${styles.reverse} {
            flex-direction: column;
          }

          .${styles.image} {
            max-width: 100%;
            flex: 0 0 auto;
          }

          .${styles.textCard}, .${styles.row}.${styles.reverse} .${styles.textCard} {
            width: 100%;
            flex: 0 0 auto;
            padding: 15px;
            margin: 0;
          }

          .${styles.textCard} p {
            font-size: 14px;
            text-align: center;
          }

          .${styles.timelineLine} {
            left: 30px;
          }

          .${styles.timeline} {
            left: 30px !important;
            transform: none;
          }

          .${styles.yearLeft}, .${styles.yearRight} {
            left: 45px !important;
            right: auto;
          }
        }
      `}</style>
    </section>
  );
};

export default Hist;
