import React, { useState, useEffect } from 'react';
import Link from "next/link";
import Image from "next/image";
import axios from 'axios';

const PremiumOffers = () => {
  const [popularPosts, setPopularPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPopularPosts = async () => {
      try {
        const token = localStorage.getItem('token');

        if (!token) {
          // If no token, show fallback data
          setPopularPosts([]);
          setLoading(false);
          return;
        }

        const config = {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };

        const response = await axios.get("http://82.165.144.72:5000/api/posts/popular/top-3", config);
        setPopularPosts(response.data.popularPosts || []);
      } catch (err) {
        console.error("Failed to fetch popular posts:", err);
        setError("Erreur lors du chargement des offres populaires");
        // Set fallback data on error
        setPopularPosts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPopularPosts();
  }, []);

  return (
    <>
      <style jsx global>{`
        @media (max-width: 768px) {
          .row.g-4.mobile-cards-container {
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
            width: 100% !important;
            padding: 0 !important;
            margin: 0 !important;
          }

          .col-lg-4.col-md-6.col-12.mobile-card-wrapper {
            width: 100% !important;
            max-width: 350px !important;
            margin: 0 auto 20px auto !important;
            padding: 0 !important;
            display: flex !important;
            justify-content: center !important;
            flex: none !important;
          }

          .mobile-card-wrapper .job-card {
            width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
          }

          .mobile-card-wrapper .job-card .p-4 {
            padding: 1rem !important;
          }

          .row.mt-4 {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            display: flex !important;
            justify-content: center !important;
          }

          .row.mt-4 .text-center {
            padding: 0 !important;
            margin: 0 auto !important;
            width: 100% !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
          }
        }
      `}</style>
      <section className="section pt-5 pb-5" style={{ backgroundColor: '#f8f9fa' }}>
      <div className="container custom-container">
        <div className="row">
          <div className="col-12 mb-4">
            <h3 className="heading fw-semibold" style={{
              color: '#2D8BBA',
              display: 'inline-block',
              position: 'relative',
              paddingLeft: '15px',
              borderLeft: '4px solid #2D8BBA'
            }}>
              Nos offres 
            </h3>
            <p style={{ color: '#666', fontSize: '14px', marginTop: '8px', marginLeft: '15px' }}>
              Les 3 offres d'emploi les plus demandées par nos candidats
            </p>
          </div>
        </div>

        {loading ? (
          <div className="row">
            <div className="col-12 text-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Chargement...</span>
              </div>
              <p className="mt-2 text-muted">Chargement des offres populaires...</p>
            </div>
          </div>
        ) : error ? (
          <div className="row">
            <div className="col-12 text-center py-5">
              <div className="d-flex flex-column align-items-center justify-content-center" style={{ minHeight: "200px" }}>
                <div style={{
                  width: "80px",
                  height: "80px",
                  borderRadius: "50%",
                  backgroundColor: "rgba(45, 139, 186, 0.1)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  marginBottom: "20px"
                }}>
                  <i className="fas fa-user-lock" style={{ fontSize: "30px", color: "#2D8BBA" }}></i>
                </div>
                <h4 style={{ color: "#2D8BBA", marginBottom: "15px" }}>Connectez-vous pour voir les offres populaires</h4>
                <p className="text-muted" style={{ maxWidth: "400px", margin: "0 auto 25px", textAlign: "center" }}>
                  Accédez aux offres d'emploi les plus populaires en vous connectant à votre compte.
                </p>
                <Link href="/signin">
                  <div className="inline-flex items-center justify-center px-5 py-2 transition-all duration-300"
                    style={{
                      backgroundColor: "#2D8BBA",
                      color: "white",
                      fontSize: "0.95rem",
                      fontWeight: "500",
                      cursor: "pointer",
                      border: "2px solid #2D8BBA",
                      boxShadow: "0 2px 4px rgba(52, 133, 182, 0.1)",
                      minWidth: "130px",
                      maxWidth: "fit-content",
                      margin: "0 auto",
                      borderRadius: "25px"
                    }}
                    onMouseOver={(e) => {
                      e.currentTarget.style.backgroundColor = "transparent";
                      e.currentTarget.style.color = "#2D8BBA";
                      e.currentTarget.style.transform = "translateY(-1px)";
                    }}
                    onMouseOut={(e) => {
                      e.currentTarget.style.backgroundColor = "#2D8BBA";
                      e.currentTarget.style.color = "white";
                      e.currentTarget.style.transform = "translateY(0)";
                    }}>
                    <span>Se connecter</span>
                    <i className="fas fa-sign-in-alt px-2 ml-2"></i>
                  </div>
                </Link>
              </div>
            </div>
          </div>
        ) : popularPosts.length === 0 ? (
          <div className="row">
            <div className="col-12 text-center py-5">
              <div className="alert alert-info" role="alert">
                <i className="fas fa-info-circle me-2"></i>
                Aucune offre populaire disponible pour le moment.
              </div>
            </div>
          </div>
        ) : (
          <div className="row g-4 mobile-cards-container">
            {popularPosts.map((item, index) => (
            <div className="col-lg-4 col-md-6 col-12 mobile-card-wrapper" key={index}>
              <div className="job-card bg-white rounded-lg overflow-hidden border border-gray-200 hover:border-blue-500 transition-all duration-300"
                style={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                }}>
                {/* Circular image at the top */}
                <div className="w-full text-center pt-4">
                  <div style={{ 
                    width: '80px', 
                    height: '80px', 
                    margin: '0 auto',
                    position: 'relative',
                    borderRadius: '50%',
                    overflow: 'hidden',
                    border: '3px solid #2D8BBA'
                  }}>
                    <Image
                      src={item.recruiterPhoto || item.recruiterDetails?.photoUrl || "/images/Slider/sabrina.png"}
                      layout="fill"
                      objectFit="cover"
                      alt="Recruiter Image"
                      className="transition-transform duration-300 hover:scale-105"
                    />
                  </div>
                  <div className="mt-2" style={{ color: '#2D8BBA', fontWeight: '500', fontSize: '0.95rem' }}>
                    {item.recruiterName || item.recruiterDetails?.name || "Recruteur"}
                  </div>
                </div>
                
                <div className="p-4">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1 text-center">
                      <h4 className="text-xl font-semibold mb-2" style={{ color: '#2D8BBA' }}>
                        {item.contract}
                      </h4>
                      <p className="text-gray-600 mb-2" style={{ fontSize: '0.95rem' }}>
                        {item.agence}
                      </p>
                      {/* Popularity Badge */}
                      <div style={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        backgroundColor: '#ff6b35',
                        color: 'white',
                        padding: '4px 8px',
                        borderRadius: '12px',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        marginTop: '4px'
                      }}>
                        🔥 #{item.popularityRank} • {item.tauxApplications} candidatures
                      </div>
                    </div>
                  </div>

                  {/* Location Tags */}
                  <div className="flex flex-wrap text-center gap-2 mb-4 justify-center">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm"
                      style={{ 
                        backgroundColor: 'rgba(52, 133, 182, 0.1)', 
                        color: '#2D8BBA',
                        fontWeight: '500'
                      }}>
                      <i className="fas fa-map-marker-alt px-2 mr-2"></i>
                      {item.ville}
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm"
                      style={{ 
                        backgroundColor: 'rgba(52, 133, 182, 0.1)', 
                        color: '#2D8BBA',
                        fontWeight: '500'
                      }}>
                      {item.region}
                    </span>
                  </div>

                  {/* Description */}
                  <div className="mb-4" style={{ flex: '1 1 auto' }}>
                    <p className="text-gray-600" style={{
                      display: '-webkit-box',
                      WebkitBoxOrient: 'vertical',
                      WebkitLineClamp: 3,
                      overflow: 'hidden',
                      lineHeight: '1.6',
                      fontSize: '0.95rem'
                    }}>
                      {item.descriptionDuPoste}
                    </p>
                  </div>

                  {/* Action Button */}
                  <div className="text-center">
                    <Link href={`/offre/${item.uuid || item._id}`}>
                      <div className="inline-flex items-center justify-center px-5 py-2 transition-all duration-300"
                        style={{
                          backgroundColor: '#2D8BBA',
                          color: 'white',
                          fontSize: '0.95rem',
                          fontWeight: '500',
                          cursor: 'pointer',
                          border: '2px solid #2D8BBA',
                          boxShadow: '0 2px 4px rgba(52, 133, 182, 0.1)',
                          minWidth: '130px',
                          maxWidth: 'fit-content',
                          margin: '0 auto',
                          borderRadius: '25px'
                        }}
                        onMouseOver={(e) => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                          e.currentTarget.style.color = '#2D8BBA';
                          e.currentTarget.style.transform = 'translateY(-1px)';
                        }}
                        onMouseOut={(e) => {
                          e.currentTarget.style.backgroundColor = '#2D8BBA';
                          e.currentTarget.style.color = 'white';
                          e.currentTarget.style.transform = 'translateY(0)';
                        }}>
                        <span>Voir l'offre</span>
                        <i className="fas fa-arrow-right px-2 ml-2"></i>
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
            ))}
          </div>
        )}

        <div className="row mt-4">
        <div className="text-center">
                    <Link href={`/offres`}>
                      <div className="inline-flex items-center justify-center px-5 py-2 transition-all duration-300"
                        style={{
                          backgroundColor: '#2D8BBA',
                          color: 'white',
                          fontSize: '0.95rem',
                          fontWeight: '500',
                          cursor: 'pointer',
                          border: '2px solid #2D8BBA',
                          boxShadow: '0 2px 4px rgba(52, 133, 182, 0.1)',
                          minWidth: '130px',
                          maxWidth: 'fit-content',
                          margin: '0 auto',
                          borderRadius: '25px'
                        }}
                        onMouseOver={(e) => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                          e.currentTarget.style.color = '#2D8BBA';
                          e.currentTarget.style.transform = 'translateY(-1px)';
                        }}
                        onMouseOut={(e) => {
                          e.currentTarget.style.backgroundColor = '#2D8BBA';
                          e.currentTarget.style.color = 'white';
                          e.currentTarget.style.transform = 'translateY(0)';
                        }}>
                        <span>Voir toutes les offres</span>
                        <i className="fas fa-arrow-right px-2 ml-2"></i>
                      </div>
                    </Link>
                  </div>
        </div>
      </div>
    </section>
    </>
  );
};

export default PremiumOffers;
