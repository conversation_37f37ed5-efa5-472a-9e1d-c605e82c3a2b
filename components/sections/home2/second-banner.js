import React from 'react';
import styles from '@/styles/SecondBanner.module.css';
import { motion } from 'framer-motion';

const SecondBanner = () => {
    return (
        <>
            <style jsx global>{`
                @media (max-width: 768px) {
                    .mobile-banner-text {
                        font-size: 2rem !important;
                        line-height: 1.3 !important;
                        text-align: center !important;
                        padding: 0 20px !important;
                        margin: 0 auto !important;
                        max-width: 100% !important;
                        word-wrap: break-word !important;
                        overflow-wrap: break-word !important;
                    }

                    .mobile-banner-span {
                        font-size: 3rem !important;
                        display: block !important;
                        margin-bottom: 10px !important;
                        line-height: 1.2 !important;
                    }

                    .mobile-banner-container {
                        padding: 50px 15px !important;
                        text-align: center !important;
                        width: 100% !important;
                        max-width: 100% !important;
                    }
                }
            `}</style>
            <div className={styles.bannerSection}>
            <motion.div 
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 1.2 }}
                viewport={{ once: false }}
                className={styles.backgroundImage}
                style={{
                    backgroundImage: `linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(255,255,255,1) 1%, rgba(255,255,255,0) 40%), url("/images/home/<USER>")`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center'
                }}
            />
            <motion.div 
                initial={{ opacity: 0, y: 100 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                viewport={{ once: false }}
                className={styles.bottomContent}
            >
                <div className="mobile-banner-container">
                    {/* <p style={{ fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif', fontSize: '96px', color: '#FFFFFF', marginBottom: '-40px' }}>RECRUTER :</p> */}
                    <motion.p 
                        initial={{ opacity: 0, x: -50 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.8, delay: 0.8 }}
                        viewport={{ once: false }}
                        style={{ 
                            fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif', 
                            fontSize: '46px', 
                            color: '#FFFFFF',
                            lineHeight: '1.2',
                            margin: '0',
                            padding: '0'
                        }}
                        className={`${styles.responsiveText} mobile-banner-text`}
                    >
                       <span style={{ 
                           color: '#2D8BBA', 
                           fontSize: '5.5rem', 
                           fontWeight: 'bold',
                           display: 'inline-block'
                       }} 
                       className={`${styles.responsiveSpan} mobile-banner-span`}
                       > RECRUTER</span> C'EST NOTRE MÉTIER
                    </motion.p>
                </div>
                {/* <div className={styles.bottomRight}>
                    <button className="btn">
                        TROUVER LE JOB DE VOS RÊVES
                    </button>
                </div> */}
            </motion.div>
        </div>
        </>
    );
};

export default SecondBanner;