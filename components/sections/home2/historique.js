import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import styles from '@/styles/Historique.module.css';

const Historique = () => {
  const imageCards = [
    {
      image: '/images/home/<USER>',
      text: 'PRÉPARER\nSON ENTRETIEN'
    },
    {
      image: '/images/home/<USER>',
      text: 'RÉDIGER SON CV'
    },
    {
      image: '/images/home/<USER>',
      text: 'OPTIMISER\nSON PROFIL LINKED IN'
    },
    {
      image: '/images/home/<USER>',
      text: 'VALORISER\nSES COMPÉTENCES'
    }
  ];

  return (
    <section 
      className={styles.historiqueSection}
      style={{
        backgroundColor: '#fff',
        padding: '20px 0'
      }}
    >
      <div className={`container ${styles.container}`}>
        {/* Header Text */}
        <motion.div 
          initial={{ opacity: 0, y: -30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: false }}
          transition={{ duration: 0.8 }}
          className={styles.headerSection}
          style={{ textAlign: 'center', marginBottom: '50px' }}
        >
          <motion.h2 
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: false }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className={styles.mainTitle}
            style={{
              color: '#2D8BBA',
              fontSize: '2.5rem',
              fontWeight: '700',
              marginBottom: '10px',
              fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif'
            }}
          >
            NOUS ACCOMPAGNONS NOS CANDIDATS
          </motion.h2>
          <motion.h3 
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: false }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className={styles.subTitle}
            style={{
              color: '#000',
              fontSize: '1.8rem',
              marginBottom: '10px',
              fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif'
            }}
          >
            Process, engagement, valeurs...
          </motion.h3>
          <motion.p 
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: false }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className={styles.description}
            style={{
              fontSize: '1.2rem',
              color: '#666',
              maxWidth: '800px',
              margin: '0 auto'
            }}
          >
            Découvrez nos actions pour vous trouver le job que vous cherchez !
          </motion.p>
        </motion.div>

        {/* Image Cards */}
        <div 
          className={styles.cardsContainer}
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            gap: '20px',
            marginBottom: '40px'
          }}
        >
          {imageCards.map((card, index) => (
            <motion.div 
              key={index} 
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: false }}
              transition={{ duration: 0.7, delay: 0.2 + (index * 0.15) }}
              whileHover={{ y: -10 }}
              className={styles.cardItem}
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
              
              }}
            >
              <motion.div 
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
                className={styles.imageContainer}
                style={{ marginBottom: '20px', position: 'relative', width: '100%', aspectRatio: '1' }}
              >
                <Image
                  src={card.image}
                  alt={card.text}
                  layout="fill"
                  objectFit="cover"
                />
              </motion.div>
              <motion.div 
                whileHover={{ 
                  backgroundColor: '#2D8BBA',
                  borderColor: '#2D8BBA'
                }}
                className={styles.textContainer}
                style={{
                  background: '#fff',
                  border: '2px solid #2D8BBA',
                  borderRadius: '50px',
                  padding: '15px 20px',
                  textAlign: 'center',
                  width: '100%',
                  minHeight: '80px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer'
                }}
              >
                <motion.p 
                  className={styles.cardText}
                  style={{
                    color: '#2D8BBA',
                    fontWeight: '600',
                    margin: 0,
                    whiteSpace: 'pre-line',
                    transition: 'color 0.3s ease'
                  }}
                >
                  {card.text}
                </motion.p>
              </motion.div>
            </motion.div>
          ))}
        </div>

        {/* Timeline Line */}
        {/* <div style={{
          width: '100%',
          height: '2px',
          backgroundColor: '#2D8BBA',
          position: 'relative',
          margin: '40px 0'
        }}>
          {[0, 1, 2, 3].map((index) => (
            <div
              key={index}
              style={{
                position: 'absolute',
                left: `${(index * 25) + 12.5}%`,
                top: '50%',
                transform: 'translate(-50%, -50%)',
                width: '20px',
                height: '20px',
                borderRadius: '50%',
                backgroundColor: '#2D8BBA'
              }}
            />
          ))}
        </div> */}

        {/* Action Button */}
        {/* <div style={{
          display: 'flex',
          justifyContent: 'flex-end',
          marginTop: '40px'
        }}>
          <Link href="/nos-actions">
            <button style={{
              background: '#2D8BBA',
              color: '#fff',
              padding: '15px 30px',
              border: 'none',
              borderRadius: '30px',
              fontSize: '1.1rem',
              cursor: 'pointer',
              transition: 'all 0.3s ease'
            }}>
              NOS ACTIONS POUR VOUS
            </button>
          </Link>
        </div> */}
      </div>
    </section>
  );
};

export default Historique;
