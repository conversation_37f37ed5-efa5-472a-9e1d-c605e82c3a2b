import React, { useEffect, useRef, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import styles from '@/styles/Section6.module.css';

const Section6 = () => {
  const [animationPlayed, setAnimationPlayed] = useState(false);
  const sectionRef = useRef(null);

  // Generate random particle positions for the background
  const generateParticles = (count = 40) => {
    const particles = [];
    for (let i = 0; i < count; i++) {
      particles.push({
        top: `${Math.random() * 100}%`,
        left: `${Math.random() * 100}%`,
        size: Math.random() * 5 + 3,
        opacity: Math.random() * 0.5 + 0.1,
        delay: Math.random() * 10
      });
    }
    return particles;
  };

  const particles = generateParticles();

  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !animationPlayed) {
          setAnimationPlayed(true);
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.3 });

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, [animationPlayed]);

  return (
    <>
      <style jsx global>{`
        .image-container-margin {
          margin-bottom: 100px !important;
        }
      `}</style>
      <section
        id="accompagnement"
        ref={sectionRef}
        className={styles.section6}
      >
      {/* Particle Background */}
      {particles.map((particle, i) => (
        <div 
          key={`particle-${i}`} 
          className={styles.particle} 
          style={{
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            top: particle.top,
            left: particle.left,
            opacity: particle.opacity,
            animation: `float 10s ease-in-out ${particle.delay}s infinite alternate`
          }} 
        />
      ))}

      {/* Left Side */}
      <div className={styles.leftSide}>
        <h2 className={styles.mainTitle}>
          LA FORMATION
        </h2>

        <h3 className={styles.subTitle}>
          CONSEIL ET COACHING
        </h3>
        <h3 className={styles.subTitleLast}>
          PROFESSIONNEL
        </h3>

        <div className={`${styles.imageContainer} image-container-margin`}>
          <Image
            src="/images/temporels/team.jpg"
            alt="Formation"
            layout="fill"
            objectFit="cover"
          />
        </div>
      </div>

      {/* Right Side */}
      <div className={styles.rightSide} style={{
        flex: '1',
        padding: '60px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        position: 'relative',
        zIndex: 1
      }}>
        <h3 className={styles.rightTitle}>
          UN ACCOMPAGNEMENT À 360°
        </h3>

        <p className={styles.description}>
          MERAKI est un organisme de Conseil, de
          Coaching et de Formation qui a pour
          vocation de développer le potentiel des
          professionnels sur 4 piliers :
        </p>

        <ul className={styles.list} style={{
          listStyle: 'none',
          padding: 0,
          marginBottom: '40px',
          fontFamily: 'GlacialIndifference-Regular'
        }}>
          {[
            "L'identité et la posture professionnelle",
            "Le développement des compétences",
            "La performance professionnelle",
            "Le bien-être au travail (Qualité de vie au travail / Gestion du stress)"
          ].map((item, index) => (
            <li key={index} className={styles.listItem} style={{
              color: '#333',
              fontSize: '1.1rem',
              marginBottom: '15px',
              paddingLeft: '20px',
              position: 'relative',
              fontFamily: 'GlacialIndifference-Regular'
            }}>
              <span style={{
                position: 'absolute',
                left: 0,
                content: '""',
                width: '8px',
                height: '8px',
                backgroundColor: '#2D8BBA',
                borderRadius: '50%',
                top: '8px'
              }}></span>
              {item}
            </li>
          ))}
        </ul>

        <Link href="group-eles">
          <button className={styles.button}>
            EN SAVOIR PLUS
          </button>
        </Link>
      </div>
    </section>
    </>
  );
};

export default Section6;