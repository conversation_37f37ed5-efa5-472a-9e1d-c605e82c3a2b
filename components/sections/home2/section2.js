import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEnvelope, faPhone } from '@fortawesome/free-solid-svg-icons';
import { motion } from 'framer-motion';

const Section2 = () => {
  return (
    <>
      <style jsx global>{`
        @media (min-width: 769px) {
          .desktop-button-container {
            margin-top: -10px !important;
          }
        }

        @media (max-width: 768px) {
          .mobile-button-wrapper {
            display: flex !important;
            justify-content: flex-start !important;
            align-items: flex-start !important;
            width: 100% !important;
            margin: 20px 0 0 0 !important;
            padding: 0 !important;
          }

          .desktop-button-container {
            margin: 0 !important;
            padding: 0 !important;
            width: auto !important;
            display: flex !important;
            justify-content: flex-start !important;
            align-items: flex-start !important;
          }

          .desktop-button-container a {
            margin: 0 !important;
          }

          .mobile-title-smaller {
            font-size: 1.5rem !important;
            line-height: 1.3 !important;
          }

          .mobile-hide-image {
            display: none !important;
          }
        }
      `}</style>
      <section id="recrutement" className="section-padding" style={{
        padding: '20px 0',
        background: '#fff'
      }}>
      <div className="container" style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 15px'
      }}>
        <div className="row" style={{
          display: 'flex',
          flexWrap: 'wrap',
        }}>
          {/* Left Column */}
          <motion.div 
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: false }}
            transition={{ duration: 0.8 }}
            className="col-lg-6" 
            style={{
              padding: '0 15px',
              marginBottom: '30px'
            }}
          >
            <div className="content-wrapper">
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: false }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="mobile-title-smaller"
                style={{
                  color: '#2D8BBA',
                  fontSize: '2rem',
                  fontWeight: '700',
                  marginBottom: '25px',
                  lineHeight: '1.2',
                  fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif'
                }}
              >
                « Recruter, c'est faire preuve d'intuition et savoir déceler les talents authentiques »
              </motion.h2>
              <motion.p 
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: false }}
                transition={{ duration: 0.6, delay: 0.4 }}
                style={{
                  fontSize: '1.1rem',
                  lineHeight: '1.6',
                  color: '#666',
                  marginBottom: '30px',
                  fontFamily: 'GlacialIndifference-Regular'
                }}
              >
                Atlantis Conseil est un cabinet humain qui accompagne
                les candidats dans leurs recherches d'emploi, qui
                assiste les entreprises dans leurs recrutements.
              </motion.p>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: false }}
                transition={{ duration: 0.5, delay: 0.6 }}
                className="mobile-button-wrapper"
                style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  marginTop: '20px'
                }}
              >
                <div className="col-12 desktop-button-container" style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  marginTop: '20px',
                  paddingRight: '15px'
                }}>
                  <a 
                    href="/contact"
                    style={{
                      backgroundColor: '#2D8BBA',
                      color: 'white',
                      fontSize: '0.95rem',
                      fontWeight: '500',
                      cursor: 'pointer',
                      border: '2px solid #2D8BBA',
                      boxShadow: '0 2px 4px rgba(52, 133, 182, 0.1)',
                      minWidth: '130px',
                      padding: '10px 20px',
                      margin: '0 auto',
                      borderRadius: '25px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '8px',
                      textDecoration: 'none',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseOver={(e) => {
                      e.currentTarget.style.transform = 'translateY(-5px)';
                      e.currentTarget.style.backgroundColor = 'transparent';
                      e.currentTarget.style.color = '#2D8BBA';
                    }}
                    onMouseOut={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.backgroundColor = '#2D8BBA';
                      e.currentTarget.style.color = 'white';
                    }}
                  >
                    Contacter
                    <i className="fas fa-arrow-right"></i>
                  </a>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Right Column */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: false }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="col-lg-6 mobile-hide-image"
            style={{
              padding: '0 15px',
              display: 'flex',
              justifyContent: 'flex-end'
            }}
          >
            <motion.div
              whileHover={{ scale: 1.03 }}
              transition={{ duration: 0.3 }}
            >
              <Image
                src="/assets/img/images/accueil2.png"
                alt="Accueil 2"
                width={350}
                height={300}
                style={{
                  borderRadius: '10px',
                  width: 'auto',
                  height: 'auto',
                  maxWidth: '100%'
                }}
              />
            </motion.div>
          </motion.div>
        </div>

        {/* Bottom Images Row */}
        {/* <div className="row" style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: '40px'
        }}>
          <div className="col-lg-4" style={{ padding: '0 15px' }}>
            <Image
              src="/assets/img/images/accueil3.png"
              alt="Accueil 3"
              width={400}
              height={300}
              style={{
                borderRadius: '10px',
                width: '100%',
                height: 'auto'
              }}
            />
          </div>
          <div className="col-lg-4" style={{ padding: '0 15px' }}>
            <Image
              src="/assets/img/images/accueil4.png"
              alt="Accueil 4"
              width={400}
              height={300}
              style={{
                borderRadius: '10px',
                width: '100%',
                height: 'auto'
              }}
            />
          </div>
          <div className="col-lg-4" style={{ padding: '0 15px' }}>
            <Image
              src="/assets/img/images/accueil5.png"
              alt="Accueil 5"
              width={400}
              height={300}
              style={{
                borderRadius: '10px',
                width: '100%',
                height: 'auto'
              }}
            />
          </div>
        </div> */}


      </div>
    </section>
    </>
  );
};

export default Section2;