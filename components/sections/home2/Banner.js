import Link from "next/link"
import { useState, useEffect, useRef } from "react";
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';

export default function Banner() {
  const router = useRouter();
    const [activeIndex, setActiveIndex] = useState(0);
    const [regions, setRegions] = useState([]);
    const [keyword, setKeyword] = useState('');
  const [region, setRegion] = useState('');
    const [posts, setPosts] = useState([]);
    const [totalPages, setTotalPages] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [loading, setLoading] = useState(false);
    const [searchKeyword, setSearchKeyword] = useState("");
    const contactRef = useRef(null); 
    const handleOnClick = (index) => {
        setActiveIndex(index);
        fetchPosts(index, searchKeyword);
    };

    const handleSearch = (event) => {
        event.preventDefault();
        fetchPosts(activeIndex, searchKeyword);
    };
    const handleSubmit = (e) => {
      e.preventDefault();
      if (region && keyword) {
        router.push(`/offres?region=${region}&keyword=${keyword}`);
      }
    };
    const fetchRegions = async () => {
        try {
            const res = await fetch('http://82.165.144.72:5000/api/postss/regions');
            const data = await res.json();
            setRegions(data);
            if (data.length > 0) {
                setActiveIndex(1);
            }
        } catch (error) {
            console.error("Failed to fetch regions:", error);
        }
    };

    const fetchPosts = async (regionIndex, keyword) => {
        setLoading(true);
        try {
            const region = regions[regionIndex - 1];
            const query = new URLSearchParams({ region, page: currentPage, limit: 10, keyword }).toString();
            const res = await fetch(`http://82.165.144.72:5000/api/postss/by-region?${query}`);
            const data = await res.json();
            setPosts(data.posts);
            setTotalPages(data.totalPages);
            setLoading(false);
        } catch (error) {
            console.error("Failed to fetch posts:", error);
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchRegions();
    }, []);
    
    useEffect(() => {
        if (regions.length > 0) {
            fetchPosts(activeIndex, searchKeyword);
        }
    }, [activeIndex, currentPage, searchKeyword]);

    const scrollToContact = (company) => {
        localStorage.setItem("selectedCompany", company); 
        if (contactRef.current) {
            contactRef.current.scrollIntoView({ behavior: "smooth" });
        }

    };
    return (
      <>
        <section className="banner-area-three mt-16" style={{
          position: 'relative',
          minHeight: '80vh',
          display: 'flex',
          alignItems: 'center',
          background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
          overflow: 'hidden',
          padding: '60px 0'
        }}>
          {/* Background decorative elements */}
          <motion.div 
            initial={{ opacity: 0, scale: 0 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: false }}
            transition={{ duration: 1.2 }}
            style={{
              position: 'absolute',
              top: '10%',
              right: '5%',
              width: '300px',
              height: '300px',
              borderRadius: '50%',
              background: 'radial-gradient(circle, rgba(45,139,186,0.15) 0%, rgba(45,139,186,0) 70%)',
              zIndex: '0'
            }} 
          />

          <motion.div 
            initial={{ opacity: 0, scale: 0 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: false }}
            transition={{ duration: 1.2, delay: 0.3 }}
            style={{
              position: 'absolute',
              bottom: '15%',
              left: '8%',
              width: '250px',
              height: '250px',
              borderRadius: '50%',
              background: 'radial-gradient(circle, rgba(45,139,186,0.1) 0%, rgba(45,139,186,0) 70%)',
              zIndex: '0'
            }}
          />

          <div className="container" style={{ position: 'relative', zIndex: '1' }}>
            <div className="row justify-content-center">
              <div className="col-lg-12">
                <div className="banner-content-wrapper" style={{
                  maxWidth: '1200px',
                  margin: '0 auto'
                }}>
                  <div className="row align-items-center">
                    {/* Left column with quote */}
                    <motion.div 
                      initial={{ opacity: 0, x: -50 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: false }}
                      transition={{ duration: 0.8 }}
                      className="col-lg-6 mb-5 mb-lg-0"
                    >
                      <div className="quote-container position-relative" style={{ padding: '0 20px' }}>
                        <motion.span 
                          initial={{ opacity: 0 }}
                          whileInView={{ opacity: 0.2 }}
                          viewport={{ once: false }}
                          transition={{ duration: 0.8, delay: 0.6 }}
                          className="quote-mark" 
                          style={{
                            position: 'absolute',
                            top: '-40px',
                            left: '-10px',
                            fontSize: '100px',
                            lineHeight: '1',
                            color: '#2D8BBA',
                            opacity: '0.2',
                            fontFamily: 'serif'
                          }}
                        >"</motion.span>
                        
                        <motion.h2
                          initial={{ opacity: 0, y: 30 }}
                          whileInView={{ opacity: 1, y: 0 }}
                          viewport={{ once: false }}
                          transition={{ duration: 0.8, delay: 0.3 }}
                          className="title mobile-first-text"
                          style={{
                            color: '#2D8BBA',
                            fontSize: 'clamp(32px, 4vw, 48px)',
                            fontWeight: 'bold',
                            marginBottom: '30px',
                            lineHeight: '1.2',
                            fontFamily: 'Helvetica World Bold, sans-serif'
                          }}
                        >
                          Recruter, c'est être intuitif et savoir reconnaître les vrais talents
                        </motion.h2>
                        
                        <motion.span 
                          initial={{ opacity: 0 }}
                          whileInView={{ opacity: 0.2 }}
                          viewport={{ once: false }}
                          transition={{ duration: 0.8, delay: 0.6 }}
                          className="quote-mark" 
                          style={{
                            position: 'absolute',
                            bottom: '-60px',
                            right: '0',
                            fontSize: '100px',
                            lineHeight: '1',
                            color: '#2D8BBA',
                            opacity: '0.2',
                            fontFamily: 'serif',
                            transform: 'rotate(180deg)'
                          }}
                        >"</motion.span>
                        
                        <motion.p 
                          initial={{ opacity: 0 }}
                          whileInView={{ opacity: 1 }}
                          viewport={{ once: false }}
                          transition={{ duration: 0.8, delay: 0.5 }}
                          style={{
                            fontSize: 'clamp(16px, 2vw, 20px)',
                            lineHeight: '1.8',
                            color: '#555',
                            maxWidth: '540px'
                          }}
                        >
                          Découvrez les meilleures opportunités d'emploi adaptées à vos compétences et à vos ambitions professionnelles.
                        </motion.p>
                      </div>
                    </motion.div>
                    
                    {/* Right column with search form */}
                    <motion.div
                      initial={{ opacity: 0, x: 50 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: false }}
                      transition={{ duration: 0.8, delay: 0.2 }}
                      className="col-lg-6 mobile-form-container"
                    >
                      <motion.div 
                        initial={{ y: 20 }}
                        whileInView={{ y: 0 }}
                        viewport={{ once: false }}
                        transition={{ 
                          duration: 0.5,
                          delay: 0.5 
                        }}
                        whileHover={{ 
                          y: -5,
                          boxShadow: '0 20px 40px rgba(45, 139, 186, 0.15)' 
                        }}
                        className="search-card" 
                        style={{
                          background: 'rgba(255, 255, 255, 0.9)',
                          borderRadius: '20px',
                          padding: '40px',
                          boxShadow: '0 15px 35px rgba(45, 139, 186, 0.1)',
                          border: '1px solid rgba(255, 255, 255, 0.5)',
                          backdropFilter: 'blur(10px)',
                          transition: 'all 0.3s ease'
                        }}
                      >
                        <motion.h3 
                          initial={{ opacity: 0 }}
                          whileInView={{ opacity: 1 }}
                          viewport={{ once: false }}
                          transition={{ duration: 0.5, delay: 0.7 }}
                          style={{
                            textAlign: 'center',
                            color: '#2D8BBA',
                            fontSize: 'clamp(20px, 3vw, 28px)',
                            fontWeight: 'bold',
                            marginBottom: '30px',
                            fontFamily: 'Helvetica World Bold, sans-serif'
                          }}
                        >
                          Trouvez votre prochain poste
                        </motion.h3>
                        
                        <motion.form 
                          initial={{ opacity: 0 }}
                          whileInView={{ opacity: 1 }}
                          viewport={{ once: false }}
                          transition={{ duration: 0.5, delay: 0.9 }}
                          className="search-form" 
                          onSubmit={handleSubmit}
                        >
                          <div className="mb-4">
                            <label className="form-label mb-2" style={{
                              color: '#555', 
                              fontWeight: '500',
                              fontSize: '16px'
                            }}>
                              Métier, Secteur, Mot-clé...
                            </label>
                            <div className="input-group" style={{
                              display: 'flex',
                              alignItems: 'stretch',
                              borderRadius: '10px',
                              overflow: 'hidden',
                              boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                              border: '1px solid #e0e0e0',
                              transition: 'all 0.3s ease'
                            }}>
                              <span style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                padding: '0 16px',
                                background: '#f8f9fa',
                                borderRight: '1px solid #e0e0e0'
                              }}>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#2D8BBA" className="bi bi-search" viewBox="0 0 16 16">
                                  <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
                                </svg>
                              </span>
                              <motion.input
                                whileFocus={{ 
                                  scale: 1.005,
                                  boxShadow: '0 0 0 2px rgba(45, 139, 186, 0.1)'
                                }}
                                type="text"
                                className="small-placeholder"
                                placeholder="Métier, Secteur, Mot-clé..."
                                value={keyword}
                                onChange={(e) => setKeyword(e.target.value)}
                                style={{
                                  flex: '1',
                                  padding: '12px 16px',
                                  fontSize: '14px',
                                  border: 'none',
                                  outline: 'none',
                                  color: '#333',
                                  background: '#fff',
                                  width: '100%',
                                  height: '48px'
                                }}
                              />
                            </div>
                          </div>
                          
                          <div className="mb-4">
                            <label className="form-label mb-2" style={{
                              color: '#555', 
                              fontWeight: '500',
                              fontSize: '16px'
                            }}>
                              Région, ville...
                            </label>
                            <div className="input-group" style={{
                              display: 'flex',
                              alignItems: 'stretch',
                              borderRadius: '10px',
                              overflow: 'hidden',
                              boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                              border: '1px solid #e0e0e0',
                              transition: 'all 0.3s ease'
                            }}>
                              <span style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                padding: '0 16px',
                                background: '#f8f9fa',
                                borderRight: '1px solid #e0e0e0'
                              }}>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#2D8BBA" className="bi bi-geo-alt" viewBox="0 0 16 16">
                                  <path d="M12.166 8.94c-.524 1.062-1.234 2.12-1.96 3.07A31.493 31.493 0 0 1 8 14.58a31.481 31.481 0 0 1-2.206-2.57c-.726-.95-1.436-2.008-1.96-3.07C3.304 7.867 3 6.862 3 6a5 5 0 0 1 10 0c0 .862-.305 1.867-.834 2.94zM8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10z"/>
                                  <path d="M8 8a2 2 0 1 1 0-4 2 2 0 0 1 0 4zm0 1a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                                </svg>
                              </span>
                              <motion.select
                                whileFocus={{ 
                                  scale: 1.005,
                                  boxShadow: '0 0 0 2px rgba(45, 139, 186, 0.1)'
                                }}
                                className="small-placeholder"
                                value={region}
                                onChange={(e) => setRegion(e.target.value)}
                                style={{
                                  flex: '1',
                                  padding: '12px 16px',
                                  fontSize: '14px',
                                  border: 'none',
                                  outline: 'none',
                                  color: '#333',
                                  background: '#fff',
                                  width: '100%',
                                  height: '48px',
                                  appearance: 'auto',
                                  cursor: 'pointer'
                                }}
                              >
                                <option value="" style={{ color: '#aaa', fontSize: '12px' }}>Sélectionnez une région</option>
                                {regions.map((regionItem, index) => (
                                  <option key={index} value={regionItem}>
                                    {regionItem}
                                  </option>
                                ))}
                              </motion.select>
                            </div>
                          </div>
                          
                          <motion.button 
                            whileHover={{ scale: 1.03 }}
                            whileTap={{ scale: 0.97 }}
                            type="submit"
                            className="btn"
                            style={{
                              backgroundColor: '#2D8BBA',
                              color: 'white',
                              padding: '15px 40px',
                              borderRadius: '9999px',
                              fontWeight: 'bold',
                              fontSize: '16px',
                              border: 'none',
                              boxShadow: '0 4px 14px rgba(45, 139, 186, 0.25)',
                              transition: 'all 0.3s ease',
                              position: 'relative',
                              overflow: 'hidden',
                              width: 'auto',
                              minWidth: '120px',
                              margin: '0 auto',
                              display: 'block'
                            }}
                          >
                            Rechercher
                          </motion.button>
                        </motion.form>
                      </motion.div>
                    </motion.div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Decorative shapes */}
          <div className="banner-shape-wrap-three">
            <img 
              src="/assets/img/banner/h3_banner_shape01.png" 
              alt="" 
              style={{
                position: 'absolute',
                top: '10%',
                right: '10%',
                maxWidth: '120px',
                opacity: '0.6',
                zIndex: '0'
              }}
            />
            <img 
              src="/assets/img/banner/h3_banner_shape02.png" 
              alt="" 
              style={{
                position: 'absolute',
                bottom: '5%',
                left: '10%',
                maxWidth: '150px',
                opacity: '0.5',
                zIndex: '0'
              }}
            />
          </div>
        </section>

        {/* Update the placeholder styling with more specific selectors */}
        <style jsx global>{`
          .small-placeholder::placeholder {
            color: #aaa !important;
            font-size: 14px !important;
            font-weight: normal !important;
            opacity: 0.7 !important;
            letter-spacing: 0.2px !important;
          }

          .input-group:hover {
            border-color: #bbd6e8 !important;
            box-shadow: 0 3px 10px rgba(45, 139, 186, 0.08) !important;
          }

          /* Tablet responsive styles (768px to 1024px) */
          @media (min-width: 768px) and (max-width: 1024px) {
            .banner-area-three {
              padding: 40px 0 !important;
              min-height: 70vh !important;
            }

            .banner-content-wrapper {
              max-width: 100% !important;
              padding: 0 20px !important;
            }

            .quote-container {
              padding: 0 10px !important;
              margin-bottom: 30px !important;
            }

            .quote-container .title {
              font-size: clamp(28px, 3.5vw, 36px) !important;
              margin-bottom: 20px !important;
            }

            .quote-container p {
              font-size: clamp(14px, 1.8vw, 18px) !important;
              line-height: 1.6 !important;
            }

            .search-card {
              padding: 30px !important;
              border-radius: 15px !important;
            }

            .search-card h3 {
              font-size: clamp(18px, 2.5vw, 24px) !important;
              margin-bottom: 25px !important;
            }

            .quote-mark {
              font-size: 80px !important;
            }
          }

          /* Mobile responsive styles (max-width: 767px) */
          @media (max-width: 767px) {
            .banner-area-three {
              padding: 30px 0 !important;
              min-height: auto !important;
              background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
            }

            .banner-content-wrapper {
              max-width: 100% !important;
              padding: 0 15px !important;
            }

            .banner-area-three .container {
              padding-left: 15px !important;
              padding-right: 15px !important;
            }

            .banner-area-three .row {
              margin: 0 !important;
            }

            .banner-area-three .col-lg-6 {
              padding-left: 15px !important;
              padding-right: 15px !important;
            }

            /* Quote section mobile styles */
            .quote-container {
              padding: 0 10px !important;
              margin-bottom: 40px !important;
              text-align: center !important;
            }

            .quote-container .title {
              font-size: clamp(24px, 6vw, 32px) !important;
              margin-bottom: 20px !important;
              line-height: 1.3 !important;
              text-align: center !important;
            }

            .mobile-first-text {
              margin-top: 40px !important;
            }

            .mobile-form-container {
              margin-top: -25px !important;
              margin-bottom: 25px !important;
            }

            .quote-container p {
              font-size: clamp(14px, 4vw, 18px) !important;
              line-height: 1.6 !important;
              text-align: center !important;
              margin: 0 auto !important;
              max-width: 100% !important;
            }

            .quote-mark {
              font-size: 60px !important;
            }

            .quote-mark:first-of-type {
              top: -20px !important;
              left: 10px !important;
            }

            .quote-mark:last-of-type {
              bottom: -30px !important;
              right: 10px !important;
            }

            /* Search card mobile styles */
            .search-card {
              padding: 25px 20px !important;
              border-radius: 15px !important;
              margin: 0 10px !important;
              background: rgba(255, 255, 255, 0.95) !important;
              box-shadow: 0 10px 25px rgba(45, 139, 186, 0.15) !important;
            }

            .search-card h3 {
              font-size: clamp(18px, 5vw, 24px) !important;
              margin-bottom: 25px !important;
              text-align: center !important;
            }

            /* Form elements mobile styles */
            .search-form .mb-4 {
              margin-bottom: 20px !important;
            }

            .search-form .form-label {
              font-size: 14px !important;
              margin-bottom: 8px !important;
            }

            .input-group {
              border-radius: 8px !important;
              margin-bottom: 15px !important;
            }

            .input-group span {
              padding: 0 12px !important;
            }

            .input-group input,
            .input-group select {
              padding: 10px 12px !important;
              font-size: 14px !important;
              height: 44px !important;
            }

            .search-form .btn {
              padding: 12px 30px !important;
              font-size: 14px !important;
              width: 100% !important;
              margin: 20px 0 0 0 !important;
              border-radius: 25px !important;
            }

            /* Background decorative elements mobile */
            .banner-area-three > div:first-child {
              width: 200px !important;
              height: 200px !important;
              top: 5% !important;
              right: -50px !important;
            }

            .banner-area-three > div:nth-child(2) {
              width: 150px !important;
              height: 150px !important;
              bottom: 5% !important;
              left: -50px !important;
            }

            /* Hide decorative shapes on mobile */
            .banner-shape-wrap-three {
              display: none !important;
            }
          }

          /* Extra small mobile devices (max-width: 480px) */
          @media (max-width: 480px) {
            .banner-area-three {
              padding: 20px 0 !important;
            }

            .quote-container .title {
              font-size: clamp(20px, 7vw, 28px) !important;
              line-height: 1.2 !important;
            }

            .quote-container p {
              font-size: clamp(13px, 4.5vw, 16px) !important;
            }

            .search-card {
              padding: 20px 15px !important;
              margin: 0 5px !important;
            }

            .search-card h3 {
              font-size: clamp(16px, 6vw, 20px) !important;
              margin-bottom: 20px !important;
            }

            .input-group input,
            .input-group select {
              padding: 8px 10px !important;
              font-size: 13px !important;
              height: 40px !important;
            }

            .input-group span {
              padding: 0 10px !important;
            }

            .search-form .btn {
              padding: 10px 25px !important;
              font-size: 13px !important;
            }

            .quote-mark {
              font-size: 50px !important;
            }
          }
        `}</style>
      </>
    );
}
