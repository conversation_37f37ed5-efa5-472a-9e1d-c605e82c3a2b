import React, { useState, useEffect } from 'react';
import { FiSearch, FiMapPin } from 'react-icons/fi';
import Select from 'react-select';
import { useRouter } from 'next/router';
import styles from './FormSelect.module.css';

export default function FormSelect() {
    const [regions, setRegions] = useState([]);
    const [activeIndex, setActiveIndex] = useState(null);
    const [searchKeyword, setSearchKeyword] = useState("");
    const [region, setRegion] = useState(null);
    const router = useRouter();

    const fetchRegions = async () => {
        try {
            const res = await fetch('http://82.165.144.72:5000/api/postss/regions');
            const data = await res.json();
            setRegions(data.map((region) => ({ value: region, label: region })));
            if (data.length > 0) {
                setActiveIndex(1);
            }
        } catch (error) {
            console.error("Failed to fetch regions:", error);
        }
    };

    const handleSearch = (event) => {
        event.preventDefault();
        if (region && searchKeyword) {
            router.push(`/offres?region=${region.value}&keyword=${searchKeyword}`);
        }
    };

    useEffect(() => {
        fetchRegions();
    }, []);

    const customStyles = {
        control: (provided) => ({
            ...provided,
            minHeight: '60px',
            border: 'none',
            boxShadow: 'none',
            '&:hover': {
                border: 'none',
            },
        }),
        container: (provided) => ({
            ...provided,
            width: '100%',
        }),
        menu: (provided) => ({
            ...provided,
            marginTop: '4px',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden',
        }),
        option: (provided, state) => ({
            ...provided,
            backgroundColor: state.isSelected ? '#2D8BBA' : state.isFocused ? '#e6f3f9' : 'white',
            color: state.isSelected ? 'white' : '#495057',
            fontSize: '14px',
            cursor: 'pointer',
            '&:hover': {
                backgroundColor: state.isSelected ? '#2D8BBA' : '#e6f3f9',
            },
        }),
        placeholder: (provided) => ({
            ...provided,
            color: '#adb5bd',
            fontSize: '14px',
        }),
        singleValue: (provided) => ({
            ...provided,
            color: '#495057',
            fontSize: '14px',
        }),
        valueContainer: (provided) => ({
            ...provided,
            padding: '0 16px',
        }),
    };

    return (
        <form className="card-body text-start p-0" onSubmit={handleSearch}>
            <div className="registration-form text-dark text-start">
                <div className="row g-0">
                    {/* Search Keyword Field */}
                    <div className={`col-lg-6 col-md-5 ${styles.formColumn}`}>
                        <div className={styles.inputWrapper}>
                            <div className={styles.iconContainer}>
                                <FiSearch className={styles.icon} />
                            </div>
                            <input
                                type="text"
                                className={styles.input}
                                placeholder="Mot Clé..."
                                value={searchKeyword}
                                onChange={(e) => setSearchKeyword(e.target.value)}
                            />
                        </div>
                    </div>

                    {/* Location Field */}
                    <div className={`col-lg-5 col-md-5 ${styles.formColumn}`}>
                        <div className={styles.inputWrapper}>
                            <div className={styles.iconContainer}>
                                <FiMapPin className={styles.icon} />
                            </div>
                            <Select
                                options={regions}
                                value={region}
                                onChange={setRegion}
                                placeholder="Ville"
                                className={styles.select}
                                classNames={{
                                    control: () => styles.selectControl
                                }}
                                styles={customStyles}
                                theme={(theme) => ({
                                    ...theme,
                                    colors: {
                                        ...theme.colors,
                                        primary: '#2D8BBA',
                                        primary25: '#e6f3f9',
                                    },
                                })}
                            />
                        </div>
                    </div>

                    {/* Submit Button */}
                    <div className={`col-lg-1 ${styles.formColumn} ${styles.searchButtonWrapper}`}>
                        <button type="submit" className={styles.searchButton}>
                            <FiSearch className={styles.searchButtonIcon} />
                            <span className={styles.searchButtonText}>Rechercher</span>
                        </button>
                    </div>
                </div>
            </div>
        </form>
    );
}
