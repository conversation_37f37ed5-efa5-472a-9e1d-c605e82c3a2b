import React from 'react';
import styles from '@/styles/SecondBanner.module.css';

const Section1 = () => {
    return (
        <>
            <style jsx>{`
                @media (max-width: 768px) {
                    .mobile-top-content {
                        top: 30% !important;
                        left: 50% !important;
                        transform: translate(-50%, -50%) !important;
                        text-align: center !important;
                        width: 90% !important;
                        padding: 0 20px !important;
                    }

                    .mobile-text {
                        font-size: 2.3rem !important;
                        text-align: center !important;
                        line-height: 1.3 !important;
                        white-space: normal !important;
                        word-wrap: break-word !important;
                        overflow-wrap: break-word !important;
                    }

                    .mobile-text span {
                        font-size: 3.5rem !important;
                        display: block !important;
                        margin-bottom: 10px !important;
                    }
                }
            `}</style>
            <div id="eles" className={styles.bannerSection}>
            <div 
                className={styles.backgroundImage}
                style={{
                    backgroundImage: `linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(255,255,255,1) 1%, rgba(255,255,255,0) 40%), url("/assets/img/images/els-team.png")`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center'
                }}
            />
            <div className={`${styles.topContent} mobile-top-content`}>
                <p className={`${styles.leftAlignedText} mobile-text`} style={{ whiteSpace: 'nowrap' }}>
                    <span style={{ color: '#2D8BBA', fontSize: '4.5rem', fontWeight: 'bold' }}>ELES</span> UN GROUPE D'EXPERTS...
                </p>
            </div>
        </div>
        </>
    );
};

export default Section1;
