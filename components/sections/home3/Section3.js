import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';

const Section3 = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);
  const items = [
    {
      image: '/assets/img/images/meraki.png',
      title: 'MERAKI FORMATIONS',
      logo: '/assets/img/logo/logov2.png',
      description: 'Découvrez nos formations MERAKI en communication, management, qualité de vie au travail et relation. Formations. Gestion du stress, communication, prévention ...',
      link: 'https://meraki-formation.com',
      linkText: 'Visiter le site'
    },
    {
      image: '/assets/img/images/logo_bichrome_rouge.png',
      title: 'Agence d\'intérim',
      logo: '/assets/img/logo/logov2.png',
      description: 'Nous sommes spécialisés dans les secteurs du BTP, de l\'industrie, du tertiaire, de l\'événementiel, de la restauration, de l\'hôtellerie et du nautisme.',
      link: 'https://mbrinterim.com/nos-offres/',
      linkText: 'Voir les offres'
    },
    {
      image: '/assets/img/images/logo-talents_job-paca.jpg',
      title: 'Agence de recrutement',
      logo: '/assets/img/logo/logov2.png',
      description: 'MBRInterim est une agence de recrutement implantée au coeur de Monaco. Découvrez nos offres d\'emploi en CDI, CDD et nos missions interim.',
      link: 'https://talentjobpaca.fr',
      linkText: 'Explorer les opportunités'
    },
  ];

  return (
    <section id="group-eles" className="mobile-section" style={{
      background: '#ffffff',
      padding: '80px 0'
    }}>
      <div className="container mobile-container" style={{
        maxWidth: '1400px',
        margin: '0 auto',
        padding: '0 15px',
        marginBottom: '100px'
      }}>
        <motion.div 
          initial={{ opacity: 0, y: -30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: false }}
          transition={{ duration: 0.8 }}
          className="section-title text-center mb-5"
        >
          <motion.h2 
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: false }}
            transition={{ duration: 0.6, delay: 0.2 }}
            style={{
              color: '#2D8BBA',
              fontSize: 'clamp(28px, 5vw, 42px)',
              fontWeight: 'bold',
              marginBottom: '20px',
              fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif'
            }}
          >
            GROUPE ELES
          </motion.h2>
          <motion.p 
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: false }}
            transition={{ duration: 0.6, delay: 0.4 }}
            style={{
              fontSize: '18px',
              lineHeight: '1.6',
              color: '#606C72',
              maxWidth: '800px',
              margin: '0 auto'
            }}
          >
            Découvrez nos entités spécialisées pour répondre à tous vos besoins.
          </motion.p>
        </motion.div>

        <div style={{ marginTop: '50px' }}>
          {items.map((item, index) => (
            <motion.div
              key={index}
              className="mobile-card"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: false }}
              transition={{ duration: 0.7, delay: 0.2 + (index * 0.2) }}
              style={{
                display: 'flex',
                flexDirection: isMobile ? 'column' : 'row',
                alignItems: 'center',
                textAlign: isMobile ? 'center' : 'left',
                marginBottom: index === items.length - 1 ? '0' : (isMobile ? '30px' : '50px'),
                padding: isMobile ? '20px' : '30px',
                borderRadius: '20px',
                boxShadow: '0 5px 20px rgba(0,0,0,0.05)',
                backgroundColor: '#f9f9f9',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                cursor: 'pointer'
              }}
              whileHover={{
                y: -5,
                boxShadow: '0 10px 30px rgba(0,0,0,0.1)'
              }}
            >
              <motion.div
                className="mobile-image-container"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: false }}
                transition={{ duration: 0.5, delay: 0.4 + (index * 0.2) }}
                style={{
                  flexShrink: 0,
                  width: isMobile ? '150px' : '200px',
                  height: isMobile ? '150px' : '200px',
                  borderRadius: '20px',
                  overflow: 'hidden',
                  position: 'relative',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '1px solid #eaeaea',
                  margin: isMobile ? '0 auto 20px auto' : '0'
                }}
              >
                <img 
                  src={item.image} 
                  alt={item.title} 
                  style={{ 
                    width: 'auto',
                    height: 'auto',
                    objectFit: 'cover'
                  }}
                />
              </motion.div>

              <div className="mobile-content" style={{
                marginLeft: isMobile ? '0' : '30px',
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: isMobile ? 'center' : 'flex-start'
              }}>
                <motion.h3
                  className="mobile-title"
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: false }}
                  transition={{ duration: 0.5, delay: 0.6 + (index * 0.2) }}
                  style={{
                    color: '#2D8BBA',
                    fontSize: isMobile ? '20px' : '24px',
                    fontWeight: 'bold',
                    marginBottom: isMobile ? '20px' : '10px',
                    fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
                    textAlign: isMobile ? 'center' : 'left'
                  }}
                >
                  {item.title}
                </motion.h3>
                {!isMobile && (
                  <motion.p
                    className="mobile-description"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    viewport={{ once: false }}
                    transition={{ duration: 0.5, delay: 0.7 + (index * 0.2) }}
                    style={{
                      fontSize: '16px',
                      lineHeight: '1.6',
                      color: '#606C72',
                      marginBottom: '15px'
                    }}
                  >
                    {item.description}
                  </motion.p>
                )}
                <motion.a
                  className="mobile-button"
                  href={item.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: false }}
                  transition={{ duration: 0.5, delay: 0.8 + (index * 0.2) }}
                  whileHover={{
                    backgroundColor: '#2D8BBA',
                    color: 'white',
                    scale: 1.05
                  }}
                  style={{
                    backgroundColor: 'transparent',
                    color: '#2D8BBA',
                    border: '2px solid #2D8BBA',
                    padding: '10px 25px',
                    borderRadius: '9999px',
                    fontWeight: '500',
                    fontSize: '14px',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    display: 'inline-block',
                    textDecoration: 'none',
                    textAlign: 'center',
                    minWidth: '160px'
                  }}
                >
                  En savoir plus
                </motion.a>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Section3;
