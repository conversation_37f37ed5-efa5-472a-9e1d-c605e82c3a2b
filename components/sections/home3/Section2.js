import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEnvelope, faPhone } from '@fortawesome/free-solid-svg-icons';
import { ArrowBigDown, ArrowBigRight } from 'lucide-react';
import { motion } from 'framer-motion';

const Section2 = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);
  return (
    <>
      <style jsx global>{`
        @media (max-width: 768px) {
          #matiere {
            padding-left: 0 !important;
            padding-right: 0 !important;
          }
          #matiere .container {
            padding-left: 20px !important;
            padding-right: 20px !important;
            margin-left: auto !important;
            margin-right: auto !important;
            max-width: 100% !important;
          }
          #matiere .row {
            margin-left: 0 !important;
            margin-right: 0 !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
          }
          #matiere .col-lg-6 {
            padding-left: 0 !important;
            padding-right: 0 !important;
            margin-left: auto !important;
            margin-right: auto !important;
          }
          #matiere .content-wrapper {
            padding-left: 0 !important;
            padding-right: 0 !important;
            margin-left: auto !important;
            margin-right: auto !important;
            text-align: center !important;
          }
          #matiere h2 {
            margin-left: auto !important;
            margin-right: auto !important;
            text-align: center !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
          }
        }
      `}</style>
      <section id="matiere" className="section-padding" style={{
        padding: '60px 0',
        background: '#dbdcdb'
      }}>
      <div className="container" style={{
        // maxWidth: '1200px',
        margin: '0 auto',
        padding: isMobile ? '0 20px' : '0 15px 0 0'
      }}>
        <div className="row" style={{
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          flexWrap: 'wrap',
          gap: isMobile ? '30px' : '0',
          alignItems: isMobile ? 'center' : 'flex-start',
          textAlign: isMobile ? 'center' : 'left'
        }}>
          {/* Left Column */}
          <motion.div 
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: false }}
            transition={{ duration: 0.8 }}
            className="col-lg-6"
            style={{
              flex: isMobile ? '1 1 100%' : '0 0 50%',
              padding: isMobile ? '0' : '0 0',
              textAlign: isMobile ? 'center' : 'left'
            }}
          >
            <div className="content-wrapper" style={{
              textAlign: isMobile ? 'center' : 'left',
              maxWidth: '100%',
              display: isMobile ? 'flex' : 'block',
              flexDirection: isMobile ? 'column' : 'row',
              alignItems: isMobile ? 'center' : 'flex-start',
              justifyContent: isMobile ? 'center' : 'flex-start',
              width: '100%',
              margin: isMobile ? '0 auto' : '0',
              padding: isMobile ? '0' : 'inherit'
            }}>
            
              <motion.h2
                className="mobile-title-main"
                initial={{ opacity: 0, y: -20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: false }}
                transition={{ duration: 0.6, delay: 0.2 }}
                style={{
                  color: '#025882',
                  fontSize: isMobile ? '1.5rem' : '3rem',
                  fontWeight: '700',
                  marginBottom: isMobile ? '20px' : '5px',
                  lineHeight: '1.2',
                  fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
                  whiteSpace: isMobile ? 'normal' : 'nowrap',
                  textAlign: isMobile ? 'center' : 'left',
                  wordWrap: 'break-word',
                  overflowWrap: 'break-word',
                  width: isMobile ? '100%' : 'auto',
                  margin: isMobile ? '0 auto 20px auto' : '0 0 5px 0',
                  padding: isMobile ? '0' : 'inherit',
                  display: 'block'
                }}
              >
                EN MATIÈRE DE RECRUTEMENT
              </motion.h2>
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: false }}
                transition={{ duration: 0.6, delay: 0.4 }}
                style={{
                  color: '#2D8BBA',
                  fontSize: isMobile ? '1.2rem' : '1.5rem',
                  fontWeight: '700',
                  marginBottom: isMobile ? '20px' : '25px',
                  lineHeight: '1.2',
                  fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif',
                  textAlign: isMobile ? 'center' : 'left',
                  width: isMobile ? '100%' : 'auto',
                  margin: isMobile ? '0 auto 20px auto' : '0 0 25px 0',
                  padding: isMobile ? '0' : 'inherit',
                  display: 'block'
                }}
              >
                Pour créer de belles rencontres professionnelles, authentiques, riches et durables. 
              </motion.h2>
              {!isMobile && (
                <motion.p
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: false }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                  style={{
                    fontSize: '1.5rem',
                    lineHeight: '1.6',
                    color: '#666',
                    marginBottom: '30px',
                    fontFamily: 'GlacialIndifference-Regular'
                  }}
                >
                 Depuis plus de 20 ans, le groupe ELES met sa passion du recrutement et de l'intérim au service des talents 
et des entreprises, de Marseille à Monaco. 
Notre force ? Une équipe d'experts engagés, à l'écoute, animés par le goût des rencontres humaines et des 
parcours réussis.
                  ans.
                </motion.p>
              )}
            </div>
          </motion.div>

          {/* Right Column */}
          <motion.div 
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: false }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="col-lg-6"
            style={{
              padding: isMobile ? '0' : '0 15px',
              flex: isMobile ? '1 1 100%' : '0 0 50%',
              display: 'flex',
              flexDirection: 'column',
              gap: isMobile ? '30px' : '40px',
              alignItems: isMobile ? 'center' : 'flex-start',
              justifyContent: isMobile ? 'center' : 'flex-start',
              width: isMobile ? '100%' : 'auto',
              margin: isMobile ? '0 auto' : '0'
            }}
          >
            {/* Button at top */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: false }}
              transition={{ duration: 0.6, delay: 0.6 }}
              style={{
                display: 'flex',
                justifyContent: isMobile ? 'center' : 'flex-end',
                alignItems: 'center',
                gap: '15px',
                width: '100%',
                margin: isMobile ? '0 auto' : '0',
                padding: isMobile ? '0' : 'inherit'
              }}
            >
              <Link href="/contact">
                <motion.button
                  whileHover={{ scale: 1.05, y: -5 }}
                  whileTap={{ scale: 0.98 }}
                  style={{
                    background: '#fff',
                    color: '#606C72',
                    padding: isMobile ? '12px 30px' : '15px 40px',
                    border: '2px solid #606C72',
                    borderRadius: '9999px',
                    fontSize: isMobile ? '1rem' : '1.1rem',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '15px',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                    maxWidth: isMobile ? '280px' : 'none',
                    margin: isMobile ? '0 auto' : '0'
                  }}
                >
                  <span>Nous contacter</span>
                  <div style={{ display: 'flex', gap: '10px' }}>
                    <FontAwesomeIcon icon={faPhone} />
                    <FontAwesomeIcon icon={faEnvelope} />
                  </div>
                </motion.button>
              </Link>
            
            </motion.div>

            {/* Image at bottom */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: false }}
              transition={{ duration: 0.8, delay: 0.8 }}
              style={{
                display: 'flex',
                justifyContent: isMobile ? 'center' : 'flex-end',
                width: '100%',
                margin: isMobile ? '0 auto' : '0',
                padding: isMobile ? '0' : 'inherit'
              }}
            >
              <motion.div
                whileHover={{ scale: 1.03 }}
                transition={{ duration: 0.3 }}
                style={{
                  position: 'relative',
                  width: '100%',
                  maxWidth: isMobile ? '400px' : '800px',
                  height: isMobile ? '180px' : '220px',
                  overflow: 'hidden',
                  boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
                  transition: 'box-shadow 0.3s ease',
                  margin: isMobile ? '0 auto' : '0'
                }}
              >
                <Image
                  src="/assets/img/images/imgs-team.png"
                  alt="Accueil 2"
                  fill
                  style={{
                    objectFit: 'cover',
                    objectPosition: 'center'
                  }}
                  quality={100}
                  sizes="(max-width: 1220px) 100vw, 1220px"
                  priority
                />
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
    </>
  );
};

export default Section2;