import {FiAirplay,FiAward, FiAtSign,FiCodesandbox,FiChrome,FiCopy,FiCpu,FiPhone, FiActivity, FiClock,FiFileText, FiUsers, FiUser,FiInbox,FiThumbsUp,FiUmbrella, FiHelpCircle, FiBookmark, FiSettings, FiMail,FiMapPin} from "../assets/icons/vander"

export const categoriesData = [
    {
        icon:FiAirplay,
        title:'Business',
        title2:'Development',
        jobs:'74 Jobs'
    },
    {
        icon:FiAward,
        title:'Marketing &',
        title2:'Communication',
        jobs:'20 Jobs'
    },
    {
        icon:FiAtSign ,
        title:'Project',
        title2:'Management',
        jobs:'35 Jobs'
    },
    {
        icon:FiCodesandbox,
        title:'Customer',
        title2:'Service',
        jobs:'46 Jobs'
    },
    {
        icon:FiChrome,
        title:'Software',
        title2:'Engineering',
        jobs:'60 Jobs'
    },
    {
        icon:FiCopy ,
        title:'Human Resource',
        title2:'HR',
        jobs:'74 Jobs'
    },
    {
        icon:FiC<PERSON>,
        title:'It &',
        title2:'Networking',
        jobs:'20 Jobs'
    },
    {
        icon:FiInbox,
        title:'Sales &',
        title2:'Marketing',
        jobs:'35 Jobs'
    },
    {
        icon:FiThumbsUp,
        title:'Project',
        title2:'Manager',
        jobs:'46 Jobs'
    },
    {
        icon:FiUmbrella,
        title:'Data',
        title2:'Science',
        jobs:'60 Jobs'
    },
]

export const jobData = [
    {
        id:1,
        image:'/images/company/facebook-logo.png',
        name:'Facebook',
        title:'Web Designer / Developer',
        country:'Australia',
        city:'Vienna',    
        applied:'20',
        vacancy:'40',
        date:'13th Sep 2023',
        salary:'950 - 1100',
        posted:'2',
        jobTime:'Full Time',
        desc:'Looking for an experienced Web Designer for an our company.'
    },
    {
        id:2,
        image:'/images/company/google-logo.png',
        name:'Google',
        title:'Marketing Director',
        country:'Russia',
        city:'Moscow',    
        applied:'20',
        vacancy:'40',
        date:'29th Nov 2023',
        salary:'950 - 1100',
        posted:'5',
        jobTime:'Part Time',
        desc:'Looking for an experienced Web Designer for an our company.'
    },
    {
        id:3,
        image:'/images/company/android.png',
        name:'Android',
        title:'Application Developer',
        country:'Germany',
        city:'Berlin',    
        applied:'20',
        vacancy:'40',
        date:'29th Dec 2023',
        salary:'950 - 1100',
        posted:'3',
        jobTime:'Remote',
        desc:'Looking for an experienced Web Designer for an our company.'
    },
    {
        id:4,
        image:'/images/company/lenovo-logo.png',
        name:'Lenovo',
        title:'Senior Product Designer',
        country:'Italy',
        city:'Rome',    
        applied:'20',
        vacancy:'40',
        date:'13th March 2023',
        salary:'950 - 1100',
        posted:'2',
        jobTime:'WFH',
        desc:'Looking for an experienced Web Designer for an our company.'
    },
    {
        id:5,
        image:'/images/company/spotify.png',
        name:'Spotify',
        title:'C++ Developer',
        country:'France',
        city:'Paris',    
        applied:'20',
        vacancy:'40',
        date:'5th May 2023',
        salary:'950 - 1100',
        posted:'3',
        jobTime:'Full Time',
        desc:'Looking for an experienced Web Designer for an our company.'
    },
    {
        id:6,
        image:'/images/company/linkedin.png',
        name:'Linkedin',
        title:'Php Developer',
        country:'Greece',
        city:'Athens',    
        applied:'20',
        vacancy:'40',
        date:'19th June 2023',
        salary:'950 - 1100',
        posted:'7',
        jobTime:'Remote',
        desc:'Looking for an experienced Web Designer for an our company.'
    },
    {
        id:7,
        image:'/images/company/circle-logo.png',
        name:'Circle CI',
        title:'Web Designer / Developer',
        country:'China',
        city:'Beijing',    
        applied:'20',
        vacancy:'40',
        date:'20th June 2023',
        salary:'950 - 1100',
        posted:'1',
        jobTime:'Full Time',
        desc:'Looking for an experienced Web Designer for an our company.'
    },
    {
        id:8,
        image:'/images/company/skype.png',
        name:'Skype',
        title:'Marketing Director',
        country:'Japan',
        city:'Tokyo',    
        applied:'20',
        vacancy:'40',
        date:'31th Aug 2023',
        salary:'950 - 1100',
        posted:'2',
        jobTime:'Part Time',
        desc:'Looking for an experienced Web Designer for an our company.'
    },
    {
        id:9,
        image:'/images/company/snapchat.png',
        name:'Snapchat',
        title:'Application Developer',
        country:'Singapore',
        city:'Singapore',    
        applied:'20',
        vacancy:'40',
        date:'1th Sep 2023',
        salary:'950 - 1100',
        posted:'4',
        jobTime:'Remote ',
        desc:'Looking for an experienced Web Designer for an our company.'
    },
    {
        id:10,
        image:'/images/company/shree-logo.png',
        name:'Shreethemes',
        title:'Senior Product Designer',
        country:'United States',
        city:'Washington',    
        applied:'20',
        vacancy:'40',
        date:'13th May 2023',
        salary:'950 - 1100',
        posted:'3',
        jobTime:'WFH',
        desc:'Looking for an experienced Web Designer for an our company.'
    },
    {
        id:11,
        image:'/images/company/telegram.png',
        name:'Telegram',
        title:'C++ Developer',
        country:'Spain',
        city:'Madrid',    
        applied:'20',
        vacancy:'40',
        date:'13th March 2023',
        salary:'950 - 1100',
        posted:'2',
        jobTime:'Full Time',
        desc:'Looking for an experienced Web Designer for an our company.'
    },
    {
        id:12,
        image:'/images/company/whatsapp.png',
        name:'Whatsapp',
        title:'Php Developer',
        country:'Jordan',
        city:'Amman',    
        applied:'20',
        vacancy:'40',
        date:'13th June 2023',
        salary:'950 - 1100',
        posted:'5',
        jobTime:'Remote',
        desc:'Looking for an experienced Web Designer for an our company.'
    },
]

export const servicesData = [
    {
        icon:FiPhone,
        title:'24/7 Support',
        desc:'Many desktop publishing now use and a search for job.'
    },
    {
        icon:FiCpu,
        title:'Tech & Startup Jobs',
        desc:'Many desktop publishing now use and a search for job.'
    },
    {
        icon:FiActivity,
        title:'Quick & Easy',
        desc:'Many desktop publishing now use and a search for job.'
    },
    {
        icon:FiClock,
        title:'Save Time',
        desc:'Many desktop publishing now use and a search for job.'
    },
    {
        icon:FiFileText,
        title:'Apply with confidence',
        desc:'Many desktop publishing now use and a search for job.'
    },
    {
        icon:FiCodesandbox,
        title:'Reduce Hiring Bias',
        desc:'Many desktop publishing now use and a search for job.'
    },
    {
        icon:FiUsers,
        title:'Proactive Employers',
        desc:'Many desktop publishing now use and a search for job.'
    },
    {
        icon:FiUser,
        title:'Proactive Employers',
        desc:'Many desktop publishing now use and a search for job.'
    },
]

export const blogData = [
    {
      id:1,
      image:'/images/blog/01.jpg',
      title:'11 Tips to Help You Get New Clients Through Cold Calling',
      date:'13th Sep 2023',
      time:'5 min read',
      tag:'Arts',
      company:'Google'
    },
    {
        id:2,
        image:'/images/blog/02.jpg',
        title:'DigitalOcean launches first Canadian data centre in Toronto',
        date:'29th Nov 2023',
        time:'5 min read',
        tag:'Illustration',
        company:'Facebook'
      },
      {
        id:3,
        image:'/images/blog/03.jpg',
        title:'Using Banner Stands To Increase Trade Show Traffic',
        date:'29th Dec 2023',
        time:'5 min read',
        tag:'Music',
        company:'Linkedin'
      },
      {
        id:4,
        image:'/images/blog/04.jpg',
        title:'11 Tips to Help You Get New Clients Through Cold Calling',
        date:'13th March 2023',
        time:'5 min read',
        tag:'Arts',
        company:'Google'
      },
      {
        id:5,
        image:'/images/blog/05.jpg',
        title:'DigitalOcean launches first Canadian data centre in Toronto',
        date:'5th May 2023',
        time:'5 min read',
        tag:'Illustration',
        company:'Facebook'
      },
      {
        id:6,
        image:'/images/blog/06.jpg',
        title:'Using Banner Stands To Increase Trade Show Traffic',
        date:'19th June 2023',
        time:'5 min read',
        tag:'Music',
        company:'Linkedin'
      },
      {
        id:7,
        image:'/images/blog/07.jpg',
        title:'11 Tips to Help You Get New Clients Through Cold Calling',
        date:'20th June 2023',
        time:'5 min read',
        tag:'Arts',
        company:'Google'
      },
      {
        id:8,
        image:'/images/blog/08.jpg',
        title:'DigitalOcean launches first Canadian data centre in Toronto',
        date:'31st Aug 2023',
        time:'5 min read',
        tag:'Illustration',
        company:'Facebook'
      },
      {
        id:9,
        image:'/images/blog/09.jpg',
        title:'Using Banner Stands To Increase Trade Show Traffic',
        date:'1st Sep 2024',
        time:'5 min read',
        tag:'Music',
        company:'Linkedin'
      },
]

export const recentBlog = [
    {
        image:'/images/blog/01.jpg',
        title:'Consultant Business',
        date:'13th March 2023'
    },
    {
        image:'/images/blog/02.jpg',
        title:'Grow Your Business',
        date:'5th May 2023'
    },
    {
        image:'/images/blog/03.jpg',
        title:'Look On The Glorious Balance',
        date:'19th June 2023'
    },
]

export const servicesTwo =[
    {
        image:'/images/work/01.jpg',
        title:'Product & Branding Design'
    },
    {
        image:'/images/work/02.jpg',
        title:'Wordpress Development'
    },
    {
        image:'/images/work/03.jpg',
        title:'Audio & Video Editing'
    },
    {
        image:'/images/work/04.jpg',
        title:'Admin & Customer Support'
    },
    {
        image:'/images/work/05.jpg',
        title:'UX / UI Designer'
    },
    {
        image:'/images/work/06.jpg',
        title:'Digital Marketing'
    },
]

export const categoriesTwoData = [
    {
        title:'Human Resource',
        job:'90 Jobs Available'
    },
    {
        title:'It & Networking',
        job:'90 Jobs Available'
    },
    {
        title:'Sales & Marketing',
        job:'90 Jobs Available'
    },
    {
        title:'Accounting',
        job:'90 Jobs Available'
    },
    {
        title:'Delivery Boy',
        job:'90 Jobs Available'
    },
    {
        title:'Data Science',
        job:'90 Jobs Available'
    },
    {
        title:'Project Manager',
        job:'90 Jobs Available'
    },
    {
        title:'Engineering',
        job:'90 Jobs Available'
    },
    {
        title:'Help Center',
        job:'90 Jobs Available'
    },
    {
        title:'Full Stack Developer',
        job:'90 Jobs Available'
    },
]

export const accordionData = [
    {
        title:'How does it work ?',
        desc:'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form.'
    },
    {
        title:'Do I need a designer to use Jobnova ?',
        desc:'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form.'
    },
    {
        title:'What do I need to do to start selling ?',
        desc:'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form.'
    },
    {
        title:'What happens when I receive an order ?',
        desc:'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form.'
    },
]

export const jobOpenings = [
    {
        title:'Frontend Developer',
        position:'Total Openings: 1'
    },
    {
        title:'Backend Developer',
        position:'Total Openings: 3'
    },
    {
        title:'Web Developer',
        position:'Total Openings: 2'
    },
]

export const candidatesData = [
    {
        id:1,
        image:'/images/team/01.jpg',
        name:'Steven Townsend',
        post:'Marketing Director',
        salary:'$5k - $6k',
        experience:'2 Years',
        rate:true
    },
    {
        id:2,
        image:'/images/team/02.jpg',
        name:'Tiffany Betancourt',
        post:'Application Developer',
        salary:'$4k - $5k',
        experience:'1 Years',
        rate:false
    },
    {
        id:3,
        image:'/images/team/03.jpg',
        name:'Jacqueline Burns',
        post:'Senior Product Designer',
        salary:'$3k - $4k',
        experience:'1 Years',
        rate:false
    },
    {
        id:4,
        image:'/images/team/04.jpg',
        name:'Mari Harrington',
        post:'C++ Developer',
        salary:'$7k - $8k',
        experience:'3 Years',
        rate:true
    },
    {
        id:5,
        image:'/images/team/05.jpg',
        name:'Floyd Glasgow',
        post:'Php Developer',
        salary:'$3k - $4k',
        experience:'2 Years',
        rate:false
    },
    {
        id:6,
        image:'/images/team/06.jpg',
        name:'Donna Schultz',
        post:'Web Designer / Developer',
        salary:'$6k - $7k',
        experience:'3 Years',
        rate:false
    },
    {
        id:7,
        image:'/images/team/07.jpg',
        name:'Joshua Morris',
        post:'Marketing Director',
        salary:'$3k - $4k',
        experience:'1 Years',
        rate:false
    },
    {
        id:8,
        image:'/images/team/08.jpg',
        name:'Rosaria Vargas',
        post:'Application Developer',
        salary:'$5k - $6k',
        experience:'2 Years',
        rate:true
    },
    {
        id:9,
        image:'/images/team/01.jpg',
        name:'Steven Townsend',
        post:'Marketing Director',
        salary:'$5k - $6k',
        experience:'2 Years',
        rate:false
    },
    {
        id:10,
        image:'/images/team/02.jpg',
        name:'Tiffany Betancourt',
        post:'Application Developer',
        salary:'$4k - $5k',
        experience:'1 Years',
        rate:true
    },
    {
        id:11,
        image:'/images/team/03.jpg',
        name:'Jacqueline Burns',
        post:'Senior Product Designer',
        salary:'$3k - $4k',
        experience:'1 Years',
        rate:true
    },
    {
        id:12,
        image:'/images/team/04.jpg',
        name:'Mari Harrington',
        post:'C++ Developer',
        salary:'$7k - $8k',
        experience:'3 Years',
        rate:false
    },
   
]
export const candidateSkill = [
    {
        title:'HTML',
        value:'84%'
    },
    {
        title:'CSS',
        value:'75%'
    },
    {
        title:'JQuery',
        value:'79%'
    },
    {
        title:'WordPress',
        value:'79%'
    },
    {
        title:'Figma',
        value:'85%'
    },
    {
        title:'Illustration',
        value:'65%'
    },
]

export const teamData = [
    {
        image:'/images/team/01.jpg',
        name:'Jack John',
        title:'Job Seeker'
    },
    {
        image:'/images/team/02.jpg',
        name:'Krista John',
        title:'Job Seeker'
    },
    {
        image:'/images/team/03.jpg',
        name:'Roger Jackson',
        title:'Job Seeker'
    },
    {
        image:'/images/team/04.jpg',
        name:'Johnny English',
        title:'Job Seeker'
    },
]
export const helpcenterData = [
    {
        icon:FiHelpCircle,
        title:'FAQs',
        desc:'The phrasal sequence of the is now so that many campaign and benefit'
    },
    {
        icon:FiBookmark ,
        title:'Guides / Support',
        desc:'The phrasal sequence of the is now so that many campaign and benefit'
    },
    {
        icon:FiSettings,
        title:'Support Request',
        desc:'The phrasal sequence of the is now so that many campaign and benefit'
    },
]

export const commentsData = [
    {
        image:'/images/team/01.jpg',
        name:'Lorenzo Peterson',
        date:'13th March 2023 at 1:30 pm',
        desc:'" There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour "'
    },
    {
        image:'/images/team/02.jpg',
        name:'Tammy Camacho',
        date:'5th May 2023 at 10:00 am',
        desc:'" There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour "'
    },
    {
        image:'/images/team/03.jpg',
        name:'Tammy Camacho',
        date:'19th June 2023 at 9:00 am',
        desc:'" There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour "'
    },
    {
        image:'/images/team/04.jpg',
        name:'Lorenzo Peterson',
        date:'1st Sep 2023 at 1:30 pm',
        desc:'" There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour "'
    },
]
export const contactData = [
    {
        icon:FiPhone,
        title:'Phone',
        desc:'Start working with Jobnova that can provide everything',
        link:'tel:+152534-468-854'
    },
    {
        icon:FiMail,
        title:'Email',
        desc:'Start working with Jobnova that can provide everything',
        link:'<EMAIL>'
    },
    {
        icon:FiMapPin,
        title:'Location',
        desc:'Start working with Jobnova that can provide everything',
        link:'View on Google map'
    },
]