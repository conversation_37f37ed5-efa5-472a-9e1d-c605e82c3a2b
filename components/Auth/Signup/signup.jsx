import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import axios from 'axios';
import { useRouter } from 'next/router';
import { MDBContainer, MDBCol, MDBRow, MDBInput, MDBCard, MDBCardBody, MDBCardImage, MDBIcon } from 'mdb-react-ui-kit';
import { ToastContainer, toast } from "react-toastify";
import 'react-toastify/dist/ReactToastify.css';
import styles from './signup.module.css';
import { motion } from 'framer-motion';

function Signup() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    telephone: '',
    password: ''
  });
  const [currentStep, setCurrentStep] = useState(1);
  const [errors, setErrors] = useState({
    firstName: '',
    lastName: '',
    email: '',
    telephone: '',
    password: ''
  });
  const [isMobile, setIsMobile] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState({ level: 0, color: 'red', width: 0 });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  const calculatePasswordStrength = (password) => {
    if (!password) {
      return { level: 0, color: 'red', width: 0 };
    }

    const hasLetter = /[a-zA-Z]/.test(password);
    const hasDigit = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
    const hasMinLength = password.length >= 8;

    // Only letters → red bar, 30% width
    if (hasLetter && !hasDigit && !hasSpecialChar) {
      return { level: 1, color: '#ef4444', width: 30 };
    }

    // Letters + digits OR letters + special characters → yellow bar, 60% width
    // OR letters + digits + special characters but less than 8 characters → yellow, 60% width
    if ((hasLetter && hasDigit && !hasSpecialChar) ||
        (hasLetter && !hasDigit && hasSpecialChar) ||
        (hasLetter && hasDigit && hasSpecialChar && !hasMinLength) || (!hasLetter && hasDigit && hasSpecialChar)) {
      return { level: 2, color: '#eab308', width: 60 };
    }

    // Letters + digits + special characters and at least 8 characters → green bar, 100% width
    if (hasLetter && hasDigit && hasSpecialChar && hasMinLength) {
      return { level: 3, color: '#22c55e', width: 100 };
    }

    // Default case (e.g., only digits, only special chars, etc.)
    return { level: 0, color: '#ef4444', width: 10 };
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    // For telephone field, only allow digits
    if (name === 'telephone') {
      const digitsOnly = value.replace(/\D/g, '');
      setFormData({ ...formData, [name]: digitsOnly });
      // Clear any existing error when user types
      setErrors({ ...errors, [name]: '' });
    } else if (name === 'password') {
      setFormData({ ...formData, [name]: value });
      setErrors({ ...errors, [name]: '' });
      // Update password strength in real-time
      const strength = calculatePasswordStrength(value);
      setPasswordStrength(strength);
    } else {
      setFormData({ ...formData, [name]: value });
      setErrors({ ...errors, [name]: '' });
    }
  };

  const handlePreviousStep = (e) => {
    e.preventDefault();
    setCurrentStep(currentStep - 1);
  };

  const handleNextStep = (e) => {
    e.preventDefault();
    let hasErrors = false;
    const newErrors = { ...errors };

    if (currentStep === 1) {
      if (!formData.firstName) {
        newErrors.firstName = 'Prénom est requis.';
        hasErrors = true;
      }
      if (!formData.lastName) {
        newErrors.lastName = 'Nom de famille est requis.';
        hasErrors = true;
      }
    } else if (currentStep === 2) {
      if (!formData.email) {
        newErrors.email = 'Email est requis.';
        hasErrors = true;
      } else {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email)) {
          newErrors.email = 'Format d\'email invalide.';
          hasErrors = true;
        }
      }
      if (!formData.telephone) {
        newErrors.telephone = 'Numéro de téléphone est requis.';
        hasErrors = true;
      } else {
        // Phone number validation: only digits and more than 6 digits
        const phoneRegex = /^\d{6,}$/;
        if (!phoneRegex.test(formData.telephone)) {
          newErrors.telephone = 'Format de téléphone invalide.';
          hasErrors = true;
        }
      }
    }

    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    setCurrentStep(currentStep + 1);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (currentStep === 3) {
      let hasErrors = false;
      const newErrors = { ...errors };

      if (!formData.password) {
        newErrors.password = 'Le mot de passe est requis.';
        hasErrors = true;
      } else if (passwordStrength.level < 3) {
        // Password must be strong (green level) to submit
        newErrors.password = 'Le mot de passe doit être plus fort.';
        hasErrors = true;
      }

      if (hasErrors) {
        setErrors(newErrors);
        return;
      }

      setIsSubmitting(true);

      try {
        const response = await axios.post('http://*************:5000/api/users/signup', formData);
        if (response.status === 201) {
          toast.success('L\'utilisateur s\'est enregistré avec succès. Veuillez vérifier votre e-mail pour confirmer votre inscription.');
          setTimeout(() => {
            router.push('/email-confirmation?email=' + encodeURIComponent(formData.email));
          }, 2000);
        }
      } catch (err) {
        setIsSubmitting(false);
        if (err.response && err.response.data && err.response.data.message) {
          setErrors({ ...errors, password: err.response.data.message });
        } else {
          setErrors({ ...errors, password: 'Échec de l\'inscription. Veuillez réessayer.' });
        }
      }
    }
  };

  return (
    <div className={styles.container}>
      <MDBContainer fluid>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <MDBCard className={styles.signupCard}>
            <MDBRow className="g-0">
              <MDBCol md="5" className={styles.imageCol}>
                <div className={styles.logoContainer}>
                  <Link href="/" style={{ display: "inline-block", cursor: "pointer" }}>
                    <MDBCardImage
                      src="/assets/img/logo/logov2.png"
                      alt="Sign Up Image"
                      className={styles.logoImage}
                    />
                  </Link>
                  <div className={styles.benefitsList}>
                    <h4 className={styles.benefitsTitle}>Créez votre compte pour :</h4>
                    <ul className={styles.benefits}>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Postulez rapidement en un clic</span>
                      </li>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Enregistrez jusqu'à 3 Cvs</span>
                      </li>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Sauvegardez vos offres d'emploi préférées</span>
                      </li>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Configurez des alertes emails pour être informé(e) des dernières offres disponibles</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </MDBCol>
              <MDBCol md="7">
                <MDBCardBody className={styles.formContainer}>
                  <div className={styles.stepper}>
                    <div className={`${styles.stepIcon} ${currentStep >= 1 ? styles.stepIconActive : ''}`}>
                      <MDBIcon fas icon="user" />
                    </div>
                    <div className={`${styles.stepBar} ${currentStep >= 2 ? styles.stepBarActive : ''}`} />
                    <div className={`${styles.stepIcon} ${currentStep >= 2 ? styles.stepIconActive : ''}`}>
                      <MDBIcon fas icon="envelope" />
                    </div>
                    <div className={`${styles.stepBar} ${currentStep >= 3 ? styles.stepBarActive : ''}`} />
                    <div className={`${styles.stepIcon} ${currentStep >= 3 ? styles.stepIconActive : ''}`}>
                      <MDBIcon fas icon="lock" />
                    </div>
                  </div>

                  <motion.form
                    onSubmit={handleSubmit}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    {currentStep === 1 && (
                      <motion.div
                        className={styles.fadeIn}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                      >
                        <h3 className={styles.stepTitle}>Informations personnelles</h3>
                        <div className="mb-4">
                          <label className="form-label">Prénom</label>
                          <MDBInput
                            wrapperClass={styles.formInput}
                            placeholder={isMobile ? "Prénom" : "Entrez votre prénom"}
                            type='text'
                            name='firstName'
                            value={formData.firstName}
                            onChange={handleChange}
                            contrast
                          />
                          {errors.firstName && <div className="text-danger mt-1">{errors.firstName}</div>}
                        </div>
                        <div className="mb-4">
                          <label className="form-label">Nom de famille</label>
                          <MDBInput
                            wrapperClass={styles.formInput}
                            placeholder={isMobile ? "Nom de famille" : "Entrez votre nom de famille"}
                            type='text'
                            name='lastName'
                            value={formData.lastName}
                            onChange={handleChange}
                            contrast
                          />
                          {errors.lastName && <div className="text-danger mt-1">{errors.lastName}</div>}
                        </div>
                      </motion.div>
                    )}

                    {currentStep === 2 && (
                      <motion.div
                        className={styles.fadeIn}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                      >
                        <h3 className={styles.stepTitle}>Coordonnées</h3>
                        <div className="mb-4">
                          <label className="form-label">Email</label>
                          <MDBInput
                            wrapperClass={styles.formInput}
                            placeholder={isMobile ? "Email" : "Entrez votre adresse email"}
                            type='email'
                            name='email'
                            value={formData.email}
                            onChange={handleChange}
                            contrast
                          />
                          {errors.email && <div className="text-danger mt-1">{errors.email}</div>}
                        </div>
                        <div className="mb-4">
                          <label className="form-label">Téléphone</label>
                          <MDBInput
                            wrapperClass={styles.formInput}
                            placeholder={isMobile ? "Téléphone" : "Entrez votre numéro de téléphone"}
                            type='tel'
                            name='telephone'
                            value={formData.telephone}
                            onChange={handleChange}
                            contrast
                          />
                          {errors.telephone && <div className="text-danger mt-1">{errors.telephone}</div>}
                        </div>
                      </motion.div>
                    )}

                    {currentStep === 3 && (
                      <motion.div
                        className={styles.fadeIn}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                      >
                        <h3 className={styles.stepTitle}>Sécurité</h3>
                        <div className="mb-4">
                          <label className="form-label">Mot de passe</label>
                          <MDBInput
                            wrapperClass={styles.formInput}
                            placeholder={isMobile ? "Mot de passe" : "Créez votre mot de passe"}
                            type='password'
                            name='password'
                            value={formData.password}
                            onChange={handleChange}
                            contrast
                          />

                          {/* Password Strength Progress Bar */}
                          <div style={{
                            marginTop: '4px',
                            width: '100%',
                            position: 'relative'
                          }}>
                            <div style={{
                              width: '100%',
                              height: '6px',
                              backgroundColor: '#e5e7eb',
                              borderRadius: '2px',
                              overflow: 'hidden'
                            }}>
                              <div style={{
                                width: `${passwordStrength.width}%`,
                                height: '100%',
                                backgroundColor: passwordStrength.color,
                                transition: 'all 0.3s ease',
                                borderRadius: '2px'
                              }}></div>
                            </div>
                          </div>

                          {errors.password && <div className="text-danger mt-1">{errors.password}</div>}
                        </div>
                      </motion.div>
                    )}

                    <div className={`d-flex justify-content-between mt-4 ${styles.buttonContainer}`}>
                      {currentStep < 3 ? (
                        <button
                          type="button"
                          className={styles.primaryButton}
                          onClick={handleNextStep}
                        >
                          Suivant
                        </button>
                      ) : (
                        <button
                          type="submit"
                          className={`${styles.primaryButton} ${passwordStrength.level < 3 || isSubmitting ? styles.disabledButton : ''}`}
                          disabled={passwordStrength.level < 3 || isSubmitting}
                        >
                          {isSubmitting ? (
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                              <div className={styles.spinner}></div>
                            </div>
                          ) : (
                            'S\'inscrire'
                          )}
                        </button>
                      )}

                      {currentStep > 1 && (
                        <button
                          type="button"
                          className={styles.secondaryButton}
                          onClick={handlePreviousStep}
                        >
                          Précédent
                        </button>
                      )}
                    </div>

                    {currentStep === 1 && (
                      <div className="text-center mt-4">
                        <p className={styles.stepDescription}>
                          Vous avez déjà un compte ?{' '}
                          <Link href="/signin" className={styles.forgotPassword}>
                            Connectez-vous
                          </Link>
                        </p>
                      </div>
                    )}
                  </motion.form>
                </MDBCardBody>
              </MDBCol>
            </MDBRow>
          </MDBCard>
        </motion.div>
      </MDBContainer>
      <ToastContainer />
    </div>
  );
}

export default Signup;
