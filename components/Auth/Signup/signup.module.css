.container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #cedbf0 100%);
}

.signupCard {
  max-width: 900px;
  width: 90%;
  margin: auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 20px;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.4);
  transform-style: preserve-3d;
  perspective: 1000px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.signupCard:hover {
  transform: translateY(-5px) rotateX(2deg) rotateY(-2deg);
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 0 0 1px rgba(255, 255, 255, 0.5);
}

.imageCol {
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  height: 100%;
  min-height: 630px;
}

.imageCol::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
  animation: pulse 3s infinite;
}

.imageCol img {
  width: auto !important;
  height: auto !important;
  max-width: 80% !important;
  max-height: 80% !important;
  object-fit: contain !important;
  transform-style: preserve-3d;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  animation: logoAnimation 8s ease-in-out infinite;
  filter: drop-shadow(0 10px 20px rgba(0,0,0,0.15));
  position: relative;
  z-index: 2;
}

.imageCol:hover img {
  animation-play-state: paused;
  transform: translateZ(40px) scale(1.08);
  filter: drop-shadow(0 20px 40px rgba(0,0,0,0.25));
}

.imageCol::after {
  content: '';
  position: absolute;
  width: 120%;
  height: 120%;
  background: radial-gradient(circle at center, rgba(52, 133, 182, 0.1) 0%, rgba(255,255,255,0) 70%);
  animation: glowAnimation 4s ease-in-out infinite;
  z-index: 1;
}

@keyframes logoAnimation {
  0% {
    transform: translateY(0) translateZ(0) rotate(0deg);
    filter: drop-shadow(0 10px 20px rgba(0,0,0,0.15));
  }
  25% {
    transform: translateY(-15px) translateZ(20px) rotate(2deg);
    filter: drop-shadow(0 15px 25px rgba(0,0,0,0.2));
  }
  50% {
    transform: translateY(0) translateZ(30px) rotate(0deg);
    filter: drop-shadow(0 20px 30px rgba(0,0,0,0.25));
  }
  75% {
    transform: translateY(-15px) translateZ(20px) rotate(-2deg);
    filter: drop-shadow(0 15px 25px rgba(0,0,0,0.2));
  }
  100% {
    transform: translateY(0) translateZ(0) rotate(0deg);
    filter: drop-shadow(0 10px 20px rgba(0,0,0,0.15));
  }
}

@keyframes glowAnimation {
  0% {
    opacity: 0.5;
    transform: scale(0.8) translateY(0);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1) translateY(-10px);
  }
  100% {
    opacity: 0.5;
    transform: scale(0.8) translateY(0);
  }
}

.formContainer {
  padding: 2.5rem;
  transform-style: preserve-3d;
}

.stepper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
  transform-style: preserve-3d;
}

.stepIcon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #bdbdbd;
  transition: all 0.3s ease;
  transform-style: preserve-3d;
  margin: 0 5px;
}

.stepIconActive {
  background: #2196F3;
  color: white;
  transform: scale(1.1) translateZ(10px);
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.stepBar {
  width: 50px;
  height: 3px;
  background: #f5f5f5;
  margin: 0 5px;
  transition: all 0.3s ease;
}

.stepBarActive {
  background: #2196F3;
  transform: translateZ(5px);
}

.stepTitle {
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
  transform: translateZ(10px);
}

.stepDescription {
  color: #7f8c8d;
  font-size: 1rem;
  text-align: center;
  margin-bottom: 2rem;
  transform: translateZ(5px);
}

.primaryButton {
  background: linear-gradient(45deg, #2196F3, #1976D2);
  border: none;
  border-radius: 25px;
  color: white;
  font-weight: 500;
  padding: 0.8rem 2rem;
  transition: all 0.3s ease;
  box-shadow: 
    0 4px 15px rgba(33, 150, 243, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transform-style: preserve-3d;
  width: 100%;
  margin-bottom: 1rem;
}

.primaryButton:hover {
  background: linear-gradient(45deg, #1976D2, #1565C0);
  transform: translateY(-2px) translateZ(10px);
  box-shadow:
    0 6px 20px rgba(33, 150, 243, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.disabledButton {
  background: linear-gradient(45deg, #9ca3af, #6b7280) !important;
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}

.disabledButton:hover {
  background: linear-gradient(45deg, #9ca3af, #6b7280) !important;
  transform: none !important;
  box-shadow: none !important;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.outlineButton {
  background: transparent;
  border: 2px solid #2196F3;
  border-radius: 25px;
  color: #2196F3;
  font-weight: 500;
  padding: 0.8rem 2rem;
  transition: all 0.3s ease;
  width: 100%;
  transform-style: preserve-3d;
}

.outlineButton:hover {
  background: rgba(33, 150, 243, 0.1);
  transform: translateY(-2px) translateZ(5px);
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.1);
}

.secondaryButton {
  background: linear-gradient(45deg, #78909C, #607D8B);
  border: none;
  border-radius: 25px;
  color: white;
  font-weight: 500;
  padding: 0.8rem 2rem;
  transition: all 0.3s ease;
  box-shadow: 
    0 4px 15px rgba(120, 144, 156, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transform-style: preserve-3d;
  width: 100%;
  margin-bottom: 1rem;
}

.secondaryButton:hover {
  background: linear-gradient(45deg, #607D8B, #546E7A);
  transform: translateY(-2px) translateZ(10px);
  box-shadow: 
    0 6px 20px rgba(120, 144, 156, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.formInput {
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  width: 100%;
  margin-bottom: 1rem;
}

.formInput:focus {
  border-color: #2196F3;
  box-shadow: 
    0 0 0 3px rgba(33, 150, 243, 0.2),
    inset 0 2px 4px rgba(0, 0, 0, 0.05);
  transform: translateZ(5px);
  outline: none;
}

.formGroup {
  margin-bottom: 1.5rem;
  transform-style: preserve-3d;
}

.label {
  display: block;
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-weight: 500;
  transform: translateZ(5px);
}

.errorText {
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  transform: translateZ(5px);
}

.successText {
  color: #27ae60;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  transform: translateZ(5px);
}

.fadeIn {
  animation: fadeIn 0.5s ease-out;
}

@keyframes float {
  0% {
    transform: translateY(0px) translateZ(0);
  }
  50% {
    transform: translateY(-15px) translateZ(20px);
  }
  100% {
    transform: translateY(0px) translateZ(0);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}


.buttonContainer {
  flex-direction: row-reverse !important;
}

@media (max-width: 768px) {
  .signupCard {
    width: 95%;
    margin: 1rem;
  }

  .formContainer {
    padding: 1.5rem;
  }

  .imageCol {
    border-radius: 0;
    padding: 1.5rem;
    min-height: 200px;
  }

  .imageCol .logoImage {
    max-width: 60% !important;
    max-height: 100% !important;
  }

  .logoContainer {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    width: 100% !important;
    height: 100% !important;
  }

  .stepTitle {
    font-size: 1.5rem;
  }

  .stepDescription {
    font-size: 0.9rem;
  }

  .stepper {
    margin-bottom: 1.5rem;
  }

  .stepIcon {
    width: 40px;
    height: 40px;
    font-size: 0.9rem;
  }

  .stepBar {
    width: 30px;
  }

  
  .buttonContainer {
    flex-direction: column !important;
    gap: 15px !important;
    align-items: stretch !important;
  }

  .buttonContainer .secondaryButton,
  .buttonContainer .primaryButton {
    width: 100% !important;
    margin: 0 !important;
  }
}

.logoContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  z-index: 10;
  position: relative;
}

.logoLink {
  display: block;
  cursor: pointer;
  width: 100%;
  text-align: center;
}

.logoImage {
  width: auto !important;
  height: auto !important;
  max-width: 95% !important;
  max-height: 80% !important;
  object-fit: contain !important;
  transform-style: preserve-3d;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  animation: logoAnimation 8s ease-in-out infinite;
  filter: drop-shadow(0 10px 20px rgba(0,0,0,0.15));
  margin-bottom: 30px;
}

@media (max-width: 768px) {
  .imageCol .logoImage {
    margin: 0px auto;
    margin-bottom: 0px;
    max-width: 100% !important;
    max-height: 100% !important;
  }
}

.benefitsList {
  width: 100%;
  max-width: 85%;
  margin-top: 20px;
}

.benefitsTitle {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
  text-align: center;
}

.benefits {
  list-style: none;
  padding: 0;
  margin: 0;
}

.benefitItem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  font-size: 14px;
  color: #546E7A;
  line-height: 1.5;
}

.checkIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  background-color: rgb(45, 139, 186);
  color: white;
  border-radius: 50%;
  margin-right: 10px;
  flex-shrink: 0;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 3px 5px rgba(45, 139, 186, 0.2);
}

@media (max-width: 768px) {
  .benefitsList {
    display: none;
  }
}
