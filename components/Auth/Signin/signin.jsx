import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import axios from 'axios';
import { useRouter } from 'next/router';
import { MDBContainer, MDBCol, MDBRow, MDBInput, MDBCard, MDBCardBody, MDBCardImage, MDBIcon } from 'mdb-react-ui-kit';
import { ToastContainer, toast } from "react-toastify";
import 'react-toastify/dist/ReactToastify.css';
import ForgotPasswordModal from './ForgotPasswordModal';
import styles from './signin.module.css';
import { motion } from 'framer-motion';

function Signin() {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [errors, setErrors] = useState({ email: "", password: "" });
  const [currentStep, setCurrentStep] = useState(1);
  const [showForgotPasswordModal, setShowForgotPasswordModal] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const { confirmation } = router.query;
    if (confirmation === 'success') {
      toast.success('Votre email a été confirmé avec succès ! Vous pouvez maintenant vous connecter.');
    }
  }, [router.query]);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  const handleForgotPasswordClick = () => {
    setShowForgotPasswordModal(true);
  };

  const handleCloseForgotPasswordModal = () => {
    setShowForgotPasswordModal(false);
  };

  const handleNextStep = () => {
    if (currentStep === 1) {
      setCurrentStep(2);
    }
  };

  const handlePreviousStep = () => {
    setCurrentStep(currentStep - 1);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    let hasErrors = false;
    const newErrors = { email: "", password: "" };

    if (!email) {
      newErrors.email = "L'email est requis.";
      hasErrors = true;
    }

    if (!password) {
      newErrors.password = "Le mot de passe est requis.";
      hasErrors = true;
    }

    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    try {
      const response = await axios.post("http://82.165.144.72:5000/api/users/signin", {
        email,
        password,
      });

      if (response.status === 200) {
        toast.success('Connexion réussie');
        localStorage.setItem('token', response.data.token);
        setTimeout(() => {
          router.push('/profile');
        }, 500);
      }
    } catch (error) {
      console.error('Échec :', error);
      if (error.response && error.response.data) {
        if (error.response.data.emailNotConfirmed) {
          toast.warning(error.response.data.message);
          setTimeout(() => {
            router.push('/email-confirmation?email=' + encodeURIComponent(email));
          }, 2000);
        } else if (error.response.data.message) {
          setErrors({ ...errors, password: error.response.data.message });
        }
      } else {
        setErrors({ ...errors, password: 'Échec de la connexion. Veuillez réessayer.' });
      }
    }
  };

  return (
    <div className={styles.container}>
      <MDBContainer fluid>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <MDBCard className={styles.signinCard}>
            <MDBRow className="g-0">
              <MDBCol md="5" className={styles.imageCol}>
                <div className={styles.logoContainer}>
                  <Link href="/" style={{ display: "inline-block", cursor: "pointer" }}>
                    <MDBCardImage
                      src="/assets/img/logo/logov2.png"
                      alt="Professional Image"
                      className={styles.logoImage}
                    />
                  </Link>
                  <div className={styles.benefitsList}>
                    <h4 className={styles.benefitsTitle}>Créez votre compte pour :</h4>
                    <ul className={styles.benefits}>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Postulez rapidement en un clic</span>
                      </li>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Enregistrez jusqu'à 3 Cvs</span>
                      </li>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Sauvegardez vos offres d'emploi préférées</span>
                      </li>
                      <li className={styles.benefitItem}>
                        <div className={styles.checkIcon}>✓</div>
                        <span>Configurez des alertes emails pour être informé(e) des dernières offres disponibles</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </MDBCol>
              <MDBCol md="7">
                <MDBCardBody className={styles.formContainer}>
                  {currentStep === 1 && (
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5 }}
                      className={styles.fadeIn}
                    >
                      <h3 className={styles.stepTitle}>Connectez-vous à votre espace candidat</h3>
                      <div className="text-center">
                        <p className={styles.stepDescription}>Vous avez déjà un compte ?</p>
                        <button 
                          className={styles.primaryButton}
                          onClick={handleNextStep}
                        >
                          Continuer
                        </button>
                        <div className="mt-4">
                          <p className={styles.stepDescription}>Vous n'avez pas encore de compte ?</p>
                          <p className={styles.stepDescription}>Créez-en un dès maintenant !</p>
                          <Link href="/signup">
                            <button className={styles.outlineButton}>
                              S'inscrire
                            </button>
                          </Link>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {currentStep === 2 && (
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5 }}
                      className={styles.slideIn}
                    >
                      <div className={styles.stepper}>
                        <div className={`${styles.stepIcon} ${currentStep >= 1 ? styles.stepIconActive : ''}`}>
                          <MDBIcon fas icon="envelope" />
                        </div>
                        <div className={`${styles.stepBar} ${currentStep >= 2 ? styles.stepBarActive : ''}`} />
                        <div className={`${styles.stepIcon} ${currentStep >= 2 ? styles.stepIconActive : ''}`}>
                          <MDBIcon fas icon="lock" />
                        </div>
                      </div>

                      <form onSubmit={handleSubmit}>
                        <div className="mb-4">
                          <label className="form-label">Adresse email</label>
                          <MDBInput
                            placeholder={isMobile ? "Email" : "Entrez votre adresse email"}
                            type='email'
                            value={email}
                            onChange={(e) => {
                              setEmail(e.target.value);
                              setErrors({ ...errors, email: "" });
                            }}
                            className={styles.formInput}
                            contrast
                          />
                          {errors.email && <div className="text-danger mt-1">{errors.email}</div>}
                        </div>

                        <div className="mb-4">
                          <label className="form-label">Mot de passe</label>
                          <MDBInput
                            placeholder={isMobile ? "Mot de passe" : "Entrez votre mot de passe"}
                            type='password'
                            value={password}
                            onChange={(e) => {
                              setPassword(e.target.value);
                              setErrors({ ...errors, password: "" });
                            }}
                            className={styles.formInput}
                            contrast
                          />
                          {errors.password && <div className="text-danger mt-1">{errors.password}</div>}
                        </div>

                        <div className={`d-flex justify-content-between align-items-center mb-4 ${styles.buttonContainer}`}>
                          <button type="button" className={styles.secondaryButton} onClick={handlePreviousStep}>
                            Précédent
                          </button>
                          <button type="submit" className={styles.primaryButton}>
                            Se connecter
                          </button>
                        </div>

                        <div className="text-center">
                          <a
                            className={styles.forgotPassword}
                            onClick={handleForgotPasswordClick}
                          >
                            Mot de passe oublié ?
                          </a>
                        </div>
                      </form>
                    </motion.div>
                  )}
                </MDBCardBody>
              </MDBCol>
            </MDBRow>
          </MDBCard>
        </motion.div>
      </MDBContainer>

      <ForgotPasswordModal
        show={showForgotPasswordModal}
        handleClose={handleCloseForgotPasswordModal}
      />
      <ToastContainer position="bottom-left" />
    </div>
  );
}

export default Signin;
