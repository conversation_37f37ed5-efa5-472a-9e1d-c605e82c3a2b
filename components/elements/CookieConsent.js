import React, { useState, useEffect } from 'react';
import styles from '@/styles/CookieConsent.module.css';
import { checkConsentExpiry, clearNonEssentialCookies } from '@/utils/cookieUtils';

const CookieConsent = () => {
  const [showPopup, setShowPopup] = useState(false);
  const [showCustomize, setShowCustomize] = useState(false);
  const [expandedCategories, setExpandedCategories] = useState({});
  const [cookiePreferences, setCookiePreferences] = useState({
    essential: true, // Always true, cannot be disabled
    analytics: false,
    marketing: false,
    functional: false,
  });

  const cookieCategories = [
    {
      id: 'essential',
      title: 'Cookies Essentiels',
      description: 'Ces cookies sont nécessaires au fonctionnement du site web et ne peuvent pas être désactivés.',
      required: true,
      cookies: [
        'Session de connexion',
        'Préférences de langue',
        'Consentement aux cookies',
        'Sécurité CSRF'
      ]
    },
    {
      id: 'functional',
      title: 'Cookies Fonctionnels',
      description: 'Ces cookies permettent d\'améliorer votre expérience en mémorisant vos préférences.',
      required: false,
      cookies: [
        'Préférences d\'affichage',
        'Paramètres du compte',
        'Données de formulaire sauvegardées'
      ]
    },
    {
      id: 'analytics',
      title: 'Cookies Analytiques',
      description: 'Ces cookies nous aident à comprendre comment vous utilisez notre site pour l\'améliorer.',
      required: false,
      cookies: [
        'Google Analytics',
        'Statistiques de visite',
        'Analyse du comportement utilisateur'
      ]
    },
    {
      id: 'marketing',
      title: 'Cookies Marketing',
      description: 'Ces cookies sont utilisés pour vous proposer des publicités pertinentes.',
      required: false,
      cookies: [
        'Publicités ciblées',
        'Réseaux sociaux',
        'Partenaires publicitaires'
      ]
    }
  ];

  useEffect(() => {
    // Check if user has already given consent
    const consent = localStorage.getItem('cookieConsent');
    if (!consent) {
      // Show popup after a short delay for better UX
      const timer = setTimeout(() => {
        setShowPopup(true);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      // Check if consent has expired
      if (checkConsentExpiry()) {
        localStorage.removeItem('cookieConsent');
        localStorage.removeItem('cookieConsentDate');
        setShowPopup(true);
        return;
      }
      
      // Load saved preferences
      try {
        const savedPreferences = JSON.parse(consent);
        setCookiePreferences(savedPreferences);
        // Clear non-essential cookies if not consented
        clearNonEssentialCookies();
      } catch (error) {
        console.error('Error parsing cookie preferences:', error);
      }
    }
  }, []);

  const saveConsent = (preferences) => {
    localStorage.setItem('cookieConsent', JSON.stringify(preferences));
    localStorage.setItem('cookieConsentDate', new Date().toISOString());
    
    // Initialize analytics/marketing based on consent
    if (preferences.analytics) {
      // Initialize Google Analytics or other analytics
      console.log('Analytics cookies accepted');
    }
    
    if (preferences.marketing) {
      // Initialize marketing cookies
      console.log('Marketing cookies accepted');
    }
    
    if (preferences.functional) {
      // Initialize functional cookies
      console.log('Functional cookies accepted');
    }
  };

  const handleAcceptAll = () => {
    const allAccepted = {
      essential: true,
      analytics: true,
      marketing: true,
      functional: true,
    };
    setCookiePreferences(allAccepted);
    saveConsent(allAccepted);
    setShowPopup(false);
  };

  const handleRejectAll = () => {
    const onlyEssential = {
      essential: true,
      analytics: false,
      marketing: false,
      functional: false,
    };
    setCookiePreferences(onlyEssential);
    saveConsent(onlyEssential);
    setShowPopup(false);
  };

  const handleCustomize = () => {
    setShowCustomize(true);
  };

  const handleSaveCustom = () => {
    saveConsent(cookiePreferences);
    setShowPopup(false);
    setShowCustomize(false);
  };

  const toggleCategory = (categoryId) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  const toggleCookiePreference = (categoryId) => {
    if (categoryId === 'essential') return; // Cannot disable essential cookies
    
    setCookiePreferences(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  if (!showPopup) return null;

  return (
    <div className={styles.cookieOverlay}>
      <div className={styles.cookiePopup}>
        <div className={styles.cookieHeader}>
          <h2 className={styles.cookieTitle}>
            🍪 Paramètres des Cookies
          </h2>
          <p className={styles.cookieSubtitle}>
            Nous respectons votre vie privée
          </p>
        </div>

        <div className={styles.cookieBody}>
          <p className={styles.cookieDescription}>
            Nous utilisons des cookies pour améliorer votre expérience sur notre site, 
            analyser le trafic et personnaliser le contenu. Vous pouvez choisir quels 
            types de cookies vous souhaitez accepter. 
            <br />
            <a href="/mention-legales" className={styles.privacyLink} target="_blank">
              Consultez notre politique de confidentialité
            </a>
          </p>

          {showCustomize && (
            <div className={styles.cookieCategories}>
              {cookieCategories.map((category) => (
                <div key={category.id} className={styles.categoryItem}>
                  <div 
                    className={styles.categoryHeader}
                    onClick={() => toggleCategory(category.id)}
                  >
                    <h3 className={styles.categoryTitle}>
                      {category.title}
                      {category.required && ' (Requis)'}
                    </h3>
                    <div className={styles.categoryToggle}>
                      <div 
                        className={`${styles.toggleSwitch} ${
                          cookiePreferences[category.id] ? styles.active : ''
                        } ${category.required ? styles.disabled : ''}`}
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleCookiePreference(category.id);
                        }}
                      >
                        <div className={styles.toggleSlider}></div>
                      </div>
                      <span style={{ fontSize: '0.9rem', color: '#666' }}>
                        {expandedCategories[category.id] ? '▼' : '▶'}
                      </span>
                    </div>
                  </div>
                  
                  {expandedCategories[category.id] && (
                    <div className={styles.categoryContent}>
                      <p className={styles.categoryDescription}>
                        {category.description}
                      </p>
                      <ul className={styles.cookieList}>
                        {category.cookies.map((cookie, index) => (
                          <li key={index} className={styles.cookieItem}>
                            • {cookie}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        <div className={styles.cookieActions}>
          {!showCustomize ? (
            <>
              <button 
                className={`${styles.cookieButton} ${styles.rejectAll}`}
                onClick={handleRejectAll}
              >
                Refuser tout
              </button>
              <button 
                className={`${styles.cookieButton} ${styles.customize}`}
                onClick={handleCustomize}
              >
                Personnaliser
              </button>
              <button 
                className={`${styles.cookieButton} ${styles.acceptAll}`}
                onClick={handleAcceptAll}
              >
                Accepter tout
              </button>
            </>
          ) : (
            <>
              <button 
                className={`${styles.cookieButton} ${styles.rejectAll}`}
                onClick={() => setShowCustomize(false)}
              >
                Retour
              </button>
              <button 
                className={`${styles.cookieButton} ${styles.acceptAll}`}
                onClick={handleSaveCustom}
              >
                Sauvegarder
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default CookieConsent;
