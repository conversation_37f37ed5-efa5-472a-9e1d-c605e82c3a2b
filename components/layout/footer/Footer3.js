import Link from "next/link";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFacebookF, faInstagram, faLinkedin, faTwitter, faYoutube, faGoogle } from '@fortawesome/free-brands-svg-icons';
import { faMapMarkerAlt, faPhone, faEnvelope, faStar } from '@fortawesome/free-solid-svg-icons';

export default function Footer3() {
  return (
    <>
      <footer className="footer">
      {/* Wave background */}
      <div className="waves">
        <div className="wave" id="wave1"></div>
        <div className="wave" id="wave2"></div>
        <div className="wave" id="wave3"></div>
        <div className="wave" id="wave4"></div>
      </div>

      {/* Footer content */}
      <div className="footer-area-three">
        <div className="footer-top-three">
          <div className="container">
            <div className="row align-items-start">
              {/* Logo and Social Links section */}
              <div className="col-lg-3 col-md-4 col-12">
                <div className="footer-widget">
                  <div className="footer-content d-flex flex-column align-items-center">
                    <div className=" d-flex justify-content-center">
                      <Link href="/">
                        <img
                          src="/assets/img/logo/logov2.png"
                          alt="Atlantis Conseil"
                          srcSet="/assets/img/logo/logov2.png 2x, /assets/img/logo/logov2.png 3x"
                        />
                      </Link>
                    </div>

                    <div className="footer-social footer-social-three">
                      <ul className="list-wrap d-flex justify-content-center">
                        <li>
                          <Link href="https://www.facebook.com/atlantis.conseil" target="_blank" rel="noopener noreferrer">
                            <FontAwesomeIcon icon={faFacebookF} />
                          </Link>
                        </li>
                      
                        <li>
                          <Link href="https://www.instagram.com/atlantisconseil/" target="_blank" rel="noopener noreferrer">
                            <FontAwesomeIcon icon={faInstagram} />
                          </Link>
                        </li>
                        <li>
                          <Link href="https://www.linkedin.com/company/atlantis-conseil" target="_blank" rel="noopener noreferrer">
                            <FontAwesomeIcon icon={faLinkedin} />
                          </Link>
                        </li>
                       
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Navigation Links - first column - Hidden on mobile */}
              <div className="col-lg-3 col-md-6 col-sm-6 d-none d-lg-block">
                <div className="footer-widget">
                  <h4 className="fw-title mb-4">Menu Principal</h4>
                  <div className="footer-link">
                    <ul className="list-wrap">
                      <li>
                        <Link href="/">BIENVENUE</Link>
                      </li>
                      <li>
                        <Link href="/candidats">CANDIDATS</Link>
                      </li>
                      <li>
                        <Link href="/atlantis">ATLANTIS</Link>
                      </li>
                      <li>
                        <Link href="/vous-recruter">VOUS RECRUTEZ</Link>
                      </li>
                      <li>
                        <Link href="/group-eles">GROUPE ELES</Link>
                      </li>
                      <li>
                        <Link href="/contact">CONTACT</Link>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Contact section - Hidden on mobile */}
              <div className="col-lg-3 col-md-6 col-sm-6 d-none d-lg-block">
                <div className="footer-widget">
                  <h4 className="fw-title">Contact</h4>
                  <div className="footer-info">
                    <ul className="list-wrap">
                      <li className="d-flex align-items-center mb-3">
                        <div className="icon me-3">
                          <FontAwesomeIcon icon={faMapMarkerAlt} style={{ color: '#2D8BBA' }} />
                        </div>
                        <div className="content mt-0">
                          <p>IMMEUBLE PALAZZO<br />29 Avenue Simone Veil<br />06200 NICE</p>
                        </div>
                      </li>
                      <li className="d-flex align-items-center mb-3">
                        <div className="icon me-3">
                          <FontAwesomeIcon icon={faPhone} style={{ color: '#2D8BBA' }} />
                        </div>
                        <div className="content">
                          <Link href="tel:0489977508">04 89 97 75 08</Link>
                        </div>
                      </li>
                      <li className="d-flex align-items-center">
                        <div className="icon me-3">
                          <FontAwesomeIcon icon={faEnvelope} style={{ color: '#2D8BBA' }} />
                        </div>
                        <div className="content">
                          <Link href="mailto:<EMAIL>"><EMAIL></Link>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Google Reviews */}
              <div className="col-lg-3 col-md-6 col-12">
                <div className="footer-widget">
                  <div className="d-flex flex-column align-items-center text-center">
                    <h4 className="fw-title mb-4">Avis Google</h4>
                    <div className="footer-review">
                      <div className="rating mb-2">
                        <FontAwesomeIcon icon={faStar} style={{ color: '#FDCC0D' }} />
                        <FontAwesomeIcon icon={faStar} style={{ color: '#FDCC0D' }} />
                        <FontAwesomeIcon icon={faStar} style={{ color: '#FDCC0D' }} />
                        <FontAwesomeIcon icon={faStar} style={{ color: '#FDCC0D' }} />
                        <FontAwesomeIcon icon={faStar} style={{ color: '#FDCC0D' }} />
                        <span className="ms-2">4.8/5</span>
                      </div>
                      <p>Basé sur 120+ avis</p>
                      <Link
                        href="https://www.google.com/search?q=atlantis+conseil+nice+avis"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="google-review-btn d-inline-flex align-items-center mt-2"
                      >
                        <FontAwesomeIcon icon={faGoogle} className="me-2" /> Voir tous les avis
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Copyright Section */}
          <div className="footer-bottom-three " style={{ borderTop: '1px solid rgba(255,255,255,0.1)' }}>
            <div className="container">
              <div className="row">
                <div className="col-lg-12">
                  <div className="copyright-text-two text-center">
                    <p className="text-black">
                      Atlantis Conseil ©{new Date().getFullYear().toString()} | Tous droits réservés |{" "}
                      <Link href="/mention-legales">Mentions légales</Link>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </footer>

      <style jsx>{`
        /* Mobile-specific styles for footer logo positioning */
        @media (max-width: 991.98px) {
          /* Move logo to the right with left margin */
          .footer .footer-content > div:first-child {
            margin-left: 18px !important;
          }
          
          /* Keep social media buttons centered */
          .footer .footer-social {
            margin-left: 10px !important;
          }
          
          /* Ensure other elements remain centered */
          .footer .footer-widget {
            text-align: center !important;
          }
          
          .footer .copyright-text-two {
            text-align: center !important;
          }
        }
      `}</style>
    </>
  );
}
