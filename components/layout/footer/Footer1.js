import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLocationDot, faPhone, faEnvelope } from '@fortawesome/free-solid-svg-icons';
import { faFacebookF, faInstagram, faLinkedin, faLinkedinIn } from '@fortawesome/free-brands-svg-icons';

const Footer1 = () => {
  const whiteTextStyle = {
    color: '#fff'
  };

  return (
    <footer style={{
      backgroundColor: '#2D8BBA',
      padding: '60px 0 10px',
      color: '#fff'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 10px'
      }}>
        {/* Top Section with Contact Info */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          borderBottom: '1px solid #fff',
          paddingBottom: '40px',
          marginBottom: '6px'
        }}>
          {/* Address Section */}
          <div style={{
            flex: '1',
            borderRight: '2px solid #fff',
            padding: '0 30px'
          }}>
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '15px' }}>
              <FontAwesomeIcon icon={faLocationDot} style={{ fontSize: '24px', color: '#fff' }} />
              <div>
                <h4 style={{ fontWeight: '800', marginBottom: '10px', color: '#fff', fontSize: '1.2rem', fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif' }}>ADRESSE</h4>
                <p style={{ lineHeight: '1.6', color: '#fff', fontWeight: '500', fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif' }}>
                  FLEXO - AIR PROMENADE<br />
                  455 PROMENADE DES ANGLAIS<br />
                  06000 NICE
                </p>
              </div>
            </div>
            {/* Logo and Legal */}
            <div style={{
              flex: '1',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center'
            }}>
              <div style={{ marginBottom: '20px', textAlign: 'center' }}>
                <Image src="/assets/img/logo/logov3.png" alt="Logo" width={250} height={150} />
              </div>
              <Link href="/mentions-legales" style={{
                color: '#fff',
                textDecoration: 'none',
                fontWeight: '500',
                textAlign: 'center',
                fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif'
              }}>
                MENTIONS LÉGALES
              </Link>
            </div>
          </div>

          {/* Phone Section */}
          <div style={{
            flex: '1',
            borderRight: '2px solid #fff',
            padding: '0 30px'
          }}>
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '15px' }}>
              <FontAwesomeIcon icon={faPhone} style={{ fontSize: '24px', color: '#fff' }} />
              <div>
                <h4 style={{ fontWeight: '800', marginBottom: '10px', color: '#fff', fontSize: '1.2rem', fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif' }}>TÉLÉPHONE</h4>
                <p style={{ color: '#fff', fontWeight: '500', fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif' }}>04 89 97 75 08</p>
              </div>
            </div>
            {/* Navigation */}
            <div style={{ flex: '1' }}>
              <h4 style={{ fontWeight: '800', marginTop: '45px', color: '#fff', fontSize: '1.2rem', fontFamily: 'Helvetica World Bold, Helvetica, Arial, sans-serif' }}>NAVIGATION</h4>
              <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
                {['BIENVENUE', 'CANDIDATS', 'ATLANTIS', 'VOUS RECUTREZ', 'GROUPE ELES', 'ACTUS', 'CONTACT'].map((item) => (
                  <li key={item} style={{ marginBottom: '4px' }}>
                    <Link href="#" style={{ color: '#fff', textDecoration: 'none', fontWeight: '500', fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif' }}>
                      {item}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Email Section */}
          <div style={{
            flex: '1',
            padding: '0 30px'
          }}>
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '15px' }}>
              <FontAwesomeIcon icon={faEnvelope} style={{ fontSize: '24px', color: '#fff' }} />
              <div>
                <h4 style={{ fontWeight: '800', marginBottom: '10px', color: '#fff', fontSize: '1.2rem' }}>MAIL</h4>
                <p style={{ color: '#fff', fontWeight: '500' }}><EMAIL></p>
              </div>
            </div>
            {/* Qualification and Social */}
            <div style={{ flex: '1' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginTop: '45px'
              }}>
                <h4 style={{ fontWeight: '800', color: '#fff', fontSize: '1.2rem', margin: 0 }}>QUALIFICATION</h4>
                <div style={{ display: 'flex', gap: '15px' }}>
                  <Link href="#" style={{ color: '#fff', fontSize: '24px', opacity: 0.9 }}>
                    <FontAwesomeIcon icon={faInstagram} />
                  </Link>
                  <Link href="#" style={{ color: '#fff', fontSize: '24px', opacity: 0.9 }}>
                    <FontAwesomeIcon icon={faFacebookF} />
                  </Link>
                  <Link href="#" style={{ color: '#fff', fontSize: '24px', opacity: 0.9 }}>
                    <FontAwesomeIcon icon={faLinkedin} />
                  </Link>
                </div>
              </div>
              <p style={{ color: '#fff', fontWeight: '500', fontFamily: 'Helvetica World Regular, Helvetica, Arial, sans-serif' }}>FAQ</p>
            </div>
          </div>
        </div>
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          copyrights
        </div>
      </div>
    </footer>
  );
};

export default Footer1;