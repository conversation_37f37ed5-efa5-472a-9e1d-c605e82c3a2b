import Link from "next/link"
import { useState } from "react"
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faSignOutAlt, faUser, faAngleDown, faTimes } from '@fortawesome/free-solid-svg-icons'

export default function MobileMenu({ isOpen, handleMobileMenu, token, handleLogout }) {
    const [isActive, setIsActive] = useState({
        status: false,
        key: "",
    })

    const handleToggle = (key) => {
        if (isActive.key === key) {
            setIsActive({
                status: false,
            })
        } else {
            setIsActive({
                status: true,
                key,
            })
        }
    }
    
    return (
        <div className={`mobile-menu-wrapper ${isOpen ? 'active' : ''}`}>
            <div className="mobile-menu-overlay" onClick={handleMobileMenu}></div>
            <div className="mobile-menu-container">
                <button className="mobile-menu-close" onClick={handleMobileMenu}>
                    <FontAwesomeIcon icon={faTimes} />
                </button>
                <div className="mobile-menu-logo">
                    <Link href="/">
                        <img src="/assets/img/logo/logo.png" alt="Logo" />
                    </Link>
                </div>
                <nav className="mobile-nav">
                    <ul className="mobile-menu-list">
                        <li className="mobile-menu-item has-submenu">
                            <div className="mobile-menu-link-wrapper">
                                <Link href="/candidats" className="mobile-menu-link">
                                    CANDIDATS
                                </Link>
                                <button 
                                    className={`submenu-toggle ${isActive.key == 1 ? 'active' : ''}`} 
                                    onClick={() => handleToggle(1)}
                                >
                                    <FontAwesomeIcon icon={faAngleDown} />
                                </button>
                            </div>
                            <ul className={`mobile-submenu ${isActive.key == 1 ? 'active' : ''}`}>
                                <li><Link href="/candidats#emploi" onClick={handleMobileMenu}>Trouvez votre emploi</Link></li>
                                <li><Link href="/candidats#cv" onClick={handleMobileMenu}>Déposez votre CV</Link></li>
                                <li><Link href="/candidats#alerte" onClick={handleMobileMenu}>Nos actions pour vous</Link></li>
                                <li><Link href="/candidats#avantage-alerte" onClick={handleMobileMenu}>Créez une alerte emploi</Link></li>
                                <li><Link href="/candidats#actions" onClick={handleMobileMenu}>Avantage alerte emploi</Link></li>
                            </ul>
                        </li>
                        <li className="mobile-menu-item has-submenu">
                            <div className="mobile-menu-link-wrapper">
                                <Link href="/atlantis" className="mobile-menu-link">
                                    ATLANTIS
                                </Link>
                                <button 
                                    className={`submenu-toggle ${isActive.key == 2 ? 'active' : ''}`} 
                                    onClick={() => handleToggle(2)}
                                >
                                    <FontAwesomeIcon icon={faAngleDown} />
                                </button>
                            </div>
                            <ul className={`mobile-submenu ${isActive.key == 2 ? 'active' : ''}`}>
                                <li><Link href="/atlantis#a-propos" onClick={handleMobileMenu}>A propos de nous</Link></li>
                                <li><Link href="/atlantis#historique" onClick={handleMobileMenu}>Historique</Link></li>
                                <li><Link href="/atlantis#quelque-chiffre" onClick={handleMobileMenu}>Quelques chiffres</Link></li>
                                <li><Link href="/atlantis#partenaires" onClick={handleMobileMenu}>Nos partenaires</Link></li>
                            </ul>
                        </li>
                        <li className="mobile-menu-item has-submenu">
                            <div className="mobile-menu-link-wrapper">
                                <Link href="/vous-recruter" className="mobile-menu-link">
                                    VOUS RECRUTEZ
                                </Link>
                                <button 
                                    className={`submenu-toggle ${isActive.key == 3 ? 'active' : ''}`} 
                                    onClick={() => handleToggle(3)}
                                >
                                    <FontAwesomeIcon icon={faAngleDown} />
                                </button>
                            </div>
                            <ul className={`mobile-submenu ${isActive.key == 3 ? 'active' : ''}`}>
                                <li><Link href="/vous-recruter#recrutement" onClick={handleMobileMenu}>Recruter le bon candidat</Link></li>
                                <li><Link href="/vous-recruter#chasseur-tete" onClick={handleMobileMenu}>CDD ? CDI ?</Link></li>
                                <li><Link href="/vous-recruter#creer-compte" onClick={handleMobileMenu}>créer un compte</Link></li>
                                <li><Link href="/vous-recruter#quelque-chiffre" onClick={handleMobileMenu}>Quelques chiffres</Link></li>
                                <li><Link href="/vous-recruter#group-eles" onClick={handleMobileMenu}>groupe ELES</Link></li>
                            </ul>
                        </li>
                        <li className="mobile-menu-item has-submenu">
                            <div className="mobile-menu-link-wrapper">
                                <Link href="/group-eles" className="mobile-menu-link">
                                    GROUPE ELES
                                </Link>
                                <button 
                                    className={`submenu-toggle ${isActive.key == 4 ? 'active' : ''}`} 
                                    onClick={() => handleToggle(4)}
                                >
                                    <FontAwesomeIcon icon={faAngleDown} />
                                </button>
                            </div>
                            <ul className={`mobile-submenu ${isActive.key == 4 ? 'active' : ''}`}>
                                <li><Link href="/group-eles#eles" onClick={handleMobileMenu}>ELES</Link></li>
                                <li><Link href="/group-eles#matiere" onClick={handleMobileMenu}>matiére de recrutement</Link></li>
                                <li><Link href="/group-eles#group-eles" onClick={handleMobileMenu}>groupe ELES</Link></li>
                            </ul>
                        </li>
                        <li className="mobile-menu-item">
                            <Link href="/contact" onClick={handleMobileMenu} className="mobile-menu-link">
                                CONTACT
                            </Link>
                        </li>
                    </ul>
                </nav>
                
                <div className="mobile-menu-auth">
                    {token ? (
                        <div className="mobile-auth-buttons">
                            <Link href="/profile" className="mobile-menu-btn" onClick={handleMobileMenu}>
                                <FontAwesomeIcon icon={faUser} className="me-2" />
                                Mon Compte
                            </Link>
                            <Link href="/offres" className="mobile-menu-btn" onClick={handleMobileMenu}>
                                Mes offres
                            </Link>
                            <button onClick={() => { handleLogout(); handleMobileMenu(); }} className="mobile-menu-btn mobile-menu-btn-logout">
                                <FontAwesomeIcon icon={faSignOutAlt} className="me-2" />
                                Déconnexion
                            </button>
                        </div>
                    ) : (
                        <div className="mobile-auth-buttons">
                            <Link href="/signin" className="mobile-menu-btn" onClick={handleMobileMenu}>
                                Se connecter
                            </Link>
                            <Link href="/signup" className="mobile-menu-btn mobile-menu-btn-secondary" onClick={handleMobileMenu}>
                                S'inscrire
                            </Link>
                        </div>
                    )}
                </div>
            </div>
            
            <style jsx>{`
                .mobile-menu-wrapper {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 9999;
                    visibility: hidden;
                    opacity: 0;
                    transition: all 0.3s ease;
                }
                
                .mobile-menu-wrapper.active {
                    visibility: visible;
                    opacity: 1;
                }
                
                .mobile-menu-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                }
                
                .mobile-menu-container {
                    position: fixed;
                    top: 0;
                    right: -300px;
                    width: 300px;
                    height: 100%;
                    background: #fff;
                    padding: 20px;
                    overflow-y: auto;
                    transition: all 0.3s ease;
                }
                
                .active .mobile-menu-container {
                    right: 0;
                }
                
                .mobile-menu-close {
                    position: absolute;
                    top: 20px;
                    right: 20px;
                    background: none;
                    border: none;
                    font-size: 24px;
                    color: #333;
                    cursor: pointer;
                    z-index: 2;
                }
                
                .mobile-menu-logo {
                    margin-bottom: 30px;
                    text-align: center;
                    padding: 10px 0;
                }
                
                .mobile-menu-logo img {
                    max-width: 150px;
                    height: auto;
                }

                .mobile-nav {
                    margin-bottom: 30px;
                }

                .mobile-menu-list {
                    list-style: none;
                    padding: 0;
                    margin: 0;
                }

                .mobile-menu-item {
                    border-bottom: 1px solid #eee;
                }

                .mobile-menu-link-wrapper {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .mobile-menu-link {
                    display: block;
                    padding: 15px 0;
                    color: #333;
                    text-decoration: none;
                    font-size: 16px;
                    font-weight: 500;
                    transition: color 0.3s ease;
                }

                .mobile-menu-link:hover {
                    color: #2D8BBA;
                }

                .submenu-toggle {
                    background: none;
                    border: none;
                    padding: 15px;
                    color: #333;
                    cursor: pointer;
                    transition: transform 0.3s ease;
                }

                .submenu-toggle.active {
                    transform: rotate(180deg);
                }

                .mobile-submenu {
                    display: none;
                    list-style: none;
                    padding: 0;
                    margin: 0;
                    background: #f8f9fa;
                }

                .mobile-submenu.active {
                    display: block;
                }

                .mobile-submenu li {
                    border-top: 1px solid #eee;
                }

                .mobile-submenu li a {
                    display: block;
                    padding: 12px 20px;
                    color: #666;
                    text-decoration: none;
                    font-size: 14px;
                    transition: color 0.3s ease;
                }

                .mobile-submenu li a:hover {
                    color: #2D8BBA;
                }
                
                .mobile-menu-auth {
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #eee;
                }
                
                .mobile-auth-buttons {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                }
                
                .mobile-menu-btn {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 12px 20px;
                    background: #2D8BBA;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    text-decoration: none;
                    font-weight: 500;
                    transition: background 0.3s ease;
                }
                
                .mobile-menu-btn:hover {
                    background: #2a6b92;
                }

                .mobile-menu-btn-secondary {
                    background: transparent;
                    color: #2D8BBA;
                    border: 2px solid #2D8BBA;
                }

                .mobile-menu-btn-secondary:hover {
                    background: #2D8BBA;
                    color: white;
                }

                .mobile-menu-btn-logout {
                    background: #dc3545;
                }

                .mobile-menu-btn-logout:hover {
                    background: #bb2d3b;
                }
            `}</style>
        </div>
    )
}
