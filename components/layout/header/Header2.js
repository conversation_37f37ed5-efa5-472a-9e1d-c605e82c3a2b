import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import MobileMenu from '../MobileMenu';
import OffcanvusMenu from '../OffcanvusMenu';
import SearchPopup from '../SearchPopup';
import { useRouter } from 'next/router';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFacebookF, faInstagram, faLinkedin, faLinkedinIn } from '@fortawesome/free-brands-svg-icons';
import { faSignOutAlt, faUser, faStar, faBriefcase } from '@fortawesome/free-solid-svg-icons';
import { faUser as farUser, faRightFromBracket as farRightFromBracket, faStar as farStar } from '@fortawesome/free-regular-svg-icons';
import { jwtDecode } from 'jwt-decode';
import axios from 'axios';
import 'primeicons/primeicons.css';

export default function Header2({ isMobileMenu, handleMobileMenu, isSearch, handleSearch, isOffcanvus, handleOffcanvus }) {
  const token = localStorage.getItem('token');
  const [activeLink, setActiveLink] = useState('/');
  const [isScrolled, setIsScrolled] = useState(false);
  const [userName, setUserName] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isHeaderVisible, setIsHeaderVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const router = useRouter();

  const closeDropdown = () => {
    setIsDropdownOpen(false);
  };

  useEffect(() => {
    setActiveLink(router.pathname);

    // Check if device is mobile
    const checkMobile = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
      // Set initial sticky state for mobile
      if (mobile) {
        setIsScrolled(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      // Different scroll behavior for mobile vs desktop
      if (isMobile) {
        // Mobile: always sticky/scrolled state
        setIsScrolled(true);
        
        // Auto-hide behavior on mobile
        if (currentScrollY > lastScrollY && currentScrollY > 100) {
        
          setIsHeaderVisible(true);
        }
      } else {
        // Desktop: original behavior - change background when scrollY > 50
        if (currentScrollY > 50) {
          setIsScrolled(true);
        } else {
          setIsScrolled(false);
        }
        // Always visible on desktop
        setIsHeaderVisible(true);
      }

      setLastScrollY(currentScrollY);
    };

    // Get user's name if logged in
    const fetchUserData = async () => {
      if (token) {
        try {
          const decoded = jwtDecode(token);
          const userId = decoded.id;

          const config = {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          };

          const response = await axios.get(
            `http://82.165.144.72:5000/api/users/${userId}`,
            config
          );

          if (response.data && response.data.firstName) {
            setUserName(response.data.firstName);
          }
        } catch (error) {
          console.error("Error fetching user data:", error);
        }
      }
    };

    fetchUserData();
    window.addEventListener('scroll', handleScroll);

    // Close dropdown when clicking outside
    const handleClickOutside = (event) => {
      const userMenu = document.getElementById('user-dropdown-container');
      if (userMenu && !userMenu.contains(event.target)) {
        closeDropdown();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', checkMobile);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [router.pathname, token, lastScrollY, isMobile]);

  const handleLogout = () => {
    localStorage.removeItem('token');
    setUserName('');
    closeDropdown();
    router.push('/');
  };

  return (
    <>
      <header style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 9999,
        background: isScrolled ? '#fff' : 'rgba(255, 255, 255, 0)',
        transition: 'all 0.3s ease',
        boxShadow: isScrolled ? '0 2px 10px rgba(0,0,0,0.1)' : 'none',
        height: isScrolled ? '70px' : 'auto',
        padding: isScrolled ? '5px 0' : '15px 0',
        transform: isHeaderVisible ? 'translateY(0)' : 'translateY(-100%)'
      }}>
        <div id="sticky-header" className="menu-area" style={{
          background: 'transparent',
          height: isScrolled ? '60px' : 'auto',
          display: 'flex',
          alignItems: 'center'
        }}>
          <div className="container custom-container" style={{
            maxWidth: '100%',
            paddingLeft: '0px',
            paddingRight: '4px',
            right: '0',
            margin: '0 0',
            transition: 'all 0.3s ease'
          }}>
            <div className="row" style={{
              alignItems: 'center',
              minHeight: isScrolled ? '60px' : '80px'
            }}>
              <div className="col-lg-2 col-7">
                <div className="logo">
                  <Link href="/">
                    {isScrolled ? (
                      <img
                        style={{
                          height: 'auto',
                          maxWidth: isScrolled ? '180px' : 'auto',
                          marginLeft: '10px'
                        }}
                        src={"/assets/img/logo/logo-1.png"}
                        alt="Logo"
                      />
                    ) : (
                      <img
                        style={{
                          height: 'auto',
                          maxWidth: '250px',
                          marginTop: '-27px',
                          marginLeft: '25px'
                        }}
                        src={"/assets/img/logo/logov2.png"}
                        alt="Logo"
                      />
                    )}
                  </Link>
                </div>
              </div>
              
              {/* Mobile burger menu column */}
              <div className="col-5 d-lg-none" style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-end',
                height: '100%'
              }}>
                <div className="mobile-nav-toggler" onClick={handleMobileMenu} style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  padding: '10px',
                  borderRadius: '4px',
                  marginRight: '12px',
                  marginBottom:'15px'
                }}>
                  <i className="fas fa-bars" style={{
                    fontSize: '20px',
                    color: isScrolled ? '#2D8BBA' : '#606C72'
                  }} />
                </div>
              </div>
              <div className="col-lg-8 col-md-8">
                <div className="menu-wrap ">
                  <nav className="menu-nav ">
                    <div className="navbar-wrap main-menu d-none d-lg-flex">
                      <ul className="navigation d-flex justify-content-center" style={{ color: isScrolled ? '#2D8BBA' : '#fff', transition: 'all 0.3s ease' }}>

                        <li className="menu-item-has-children">
                          <Link href="/candidats" style={{ color: isScrolled ? '#2D8BBA' : '#606C72', transition: 'all 0.3s ease' }}>
                            CANDIDATS
                          </Link>
                          <ul className="submenu" style={{
                            backgroundColor: '#2D8BBA',
                            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                            padding: '15px 0',
                            minWidth: '200px'
                          }}>
                            <li><Link href="/candidats#emploi" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>Trouvez votre emploi</Link></li>
                            <li><Link href="/candidats#cv" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>Déposez votre CV</Link></li>
                            <li><Link href="/candidats#alerte" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>Nos actions pour vous</Link></li>
                            <li><Link href="/candidats#avantage-alerte" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>Créez une alerte emploi</Link></li>
                            <li><Link href="/candidats#actions" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>Avantage alerte emploi</Link></li>
                          </ul>
                        </li>
                        <li className="menu-item-has-children">
                          <Link href="/atlantis" style={{ color: isScrolled ? '#2D8BBA' : '#606C72', transition: 'all 0.3s ease' }}>
                            ATLANTIS
                          </Link>
                          <ul className="submenu" style={{
                            backgroundColor: '#2D8BBA',
                            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                            padding: '15px 0',
                            minWidth: '200px'
                          }}>
                            <li><Link href="/atlantis#a-propos" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>A propos de nous</Link></li>
                            <li><Link href="/atlantis#historique" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>Note Histoire</Link></li>
                            <li><Link href="/atlantis#quelque-chiffre" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>Quelques chiffres</Link></li>
                            <li><Link href="/atlantis#partenaires" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>Nos partenaires</Link></li>

                          </ul>
                        </li>
                        <li className="menu-item-has-children">
                          <Link href="/vous-recruter" style={{ color: isScrolled ? '#2D8BBA' : '#606C72', transition: 'all 0.3s ease' }}>
                            VOUS RECRUTEZ
                          </Link>
                          <ul className="submenu" style={{
                            backgroundColor: '#2D8BBA',
                            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                            padding: '15px 0',
                            minWidth: '200px'
                          }}>
                            <li><Link href="/vous-recruter#recrutement" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>Recruter le bon candidat</Link></li>
                            <li><Link href="/vous-recruter#chasseur-tete" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>CDD ? CDI ?</Link></li>
                            <li><Link href="/vous-recruter#creer-compte" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>créer un compte</Link></li>
                            <li><Link href="/vous-recruter#quelque-chiffre" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>Quelques chiffres</Link></li>
                            <li><Link href="/vous-recruter#group-eles" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>groupe ELES</Link></li>

                          </ul>
                        </li>
                        <li className="menu-item-has-children">
                          <Link href="/group-eles" style={{ color: isScrolled ? '#2D8BBA' : '#606C72', transition: 'all 0.3s ease' }}>
                            GROUPE ELES
                          </Link>
                          <ul className="submenu" style={{
                            backgroundColor: '#2D8BBA',
                            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                            padding: '15px 0',
                            minWidth: '200px'
                          }}>
                            <li><Link href="/group-eles#eles" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>ELES</Link></li>
                            <li><Link href="/group-eles#matiere" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>matiére de recrutement</Link></li>
                            <li><Link href="/group-eles#group-eles" style={{ color: '#fff', padding: '8px 10px', display: 'block' }}>groupe ELES</Link></li>
                          </ul>
                        </li>
                        {/* <li>
                          <Link href="/404" style={{ color: isScrolled ? '#2D8BBA' : '#606C72', transition: 'all 0.3s ease' }}>
                            ACTUS
                          </Link>
                        </li> */}
                        <li>
                          <Link href="/contact" style={{ color: isScrolled ? '#2D8BBA' : '#606C72', transition: 'all 0.3s ease' }}>
                            CONTACT
                          </Link>
                        </li>

                      </ul>
                    </div>
                  </nav>
                </div>
              </div>
              <div className="col-lg-2 col-md-6 d-none d-lg-block">
                <div className="header-action">
                  {token ? (
                    <div className="header-btn d-flex align-items-center justify-content-end gap-2" style={{
                      marginRight: isScrolled ? '20px' : '0'
                    }}>
                      <button
                        onClick={() => router.push('/offres')}
                        className="btn-account-square"
                        aria-label="Mes offres"
                        style={{
                          background: isScrolled ? '#ffffff' : 'transparent',
                          border: isScrolled ? '1px solid #f0f0f0' : 'none',
                          color: 'rgb(45, 139, 186)',
                          transition: 'all 0.3s ease',
                          width: isScrolled ? '40px' : '100px',
                          height: isScrolled ? '40px' : '70px',
                          padding: isScrolled ? '5px' : '10px',
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          gap: '8px'
                        }}
                      >
                        <div className="btn-account-icon" style={{
                          margin: '0',
                          height: '40px',
                          width: '40px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          backgroundColor: 'rgba(255, 255, 255, 0.2)',
                          borderRadius: '50%'
                        }}>
                          <FontAwesomeIcon icon={faBriefcase} />
                        </div>
                        <div className="account-text-container" style={{
                          display: isScrolled ? 'none' : 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <span>Mes offres</span>
                        </div>
                      </button>

                      <div className="user-dropdown-container" id="user-dropdown-container">
                        <button
                          className="btn-account-square"
                          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                          aria-label="User menu"
                          aria-expanded={isDropdownOpen}
                          style={{
                            background: isScrolled ? '#ffffff' : 'transparent',
                            border: isScrolled ? '1px solid #f0f0f0' : 'none',
                            color: 'rgb(45, 139, 186)',
                            transition: 'all 0.3s ease',
                            width: isScrolled ? '40px' : '100px',
                            height: isScrolled ? '40px' : '70px',
                            padding: isScrolled ? '5px' : '10px',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '8px'
                          }}
                        >
                          <div className="btn-account-icon" style={{
                            margin: '0',
                            height: '40px',
                            width: '40px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'rgba(255, 255, 255, 0.2)',
                            borderRadius: '50%'
                          }}>
                            <FontAwesomeIcon icon={farUser} />
                          </div>
                          <div className="account-text-container" style={{
                            display: isScrolled ? 'none' : 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            <span>{'Compte'}</span>
                          </div>
                        </button>

                        {isDropdownOpen && (
                          <div className="user-dropdown">
                            <div className="user-dropdown-header">
                              <div 
                                className="user-dropdown-avatar"
                                style={{
                                  backgroundColor: '#2D8BBA',
                                  color: 'white',
                                  borderRadius: '50%',
                                  width: '50px',
                                  height: '50px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  fontSize: '1.5rem',
                                  fontWeight: 'bold'
                                }}
                              >
                                {userName ? userName.charAt(0).toUpperCase() : 'U'}
                              </div>
                              <div className="user-dropdown-info">
                                <div className="user-dropdown-name">{userName}</div>
                                <div className="user-dropdown-role">Utilisateur</div>
                              </div>
                            </div>
                            <div className="user-dropdown-divider"></div>
                            <div className="user-dropdown-body">
                              <button
                                onClick={() => {
                                  closeDropdown();
                                  router.push('/profile');
                                }}
                                className="user-dropdown-item"
                              >
                               
                                <span>Mon Compte</span>
                              </button>
                              <button onClick={handleLogout} className="user-dropdown-item user-dropdown-logout">
                                <FontAwesomeIcon icon={farRightFromBracket} className="user-dropdown-icon" />
                                <span>Déconnexion</span>
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="header-btn d-flex align-items-center justify-content-end gap-2" style={{
                      marginRight: isScrolled ? '20px' : '0'
                    }}>
                      <button
                        onClick={() => router.push('/signin')}
                        className="btn-account-square"
                        aria-label="Voir les offres"
                        style={{
                          background: isScrolled ? '#ffffff' : 'transparent',
                          border: isScrolled ? '1px solid #f0f0f0' : 'none',
                          color: 'rgb(45, 139, 186)',
                          boxShadow: isScrolled ? '0 4px 10px rgba(0, 0, 0, 0.05)' : 'none',
                          transition: 'all 0.3s ease',
                          width: isScrolled ? '40px' : '90px',
                          height: isScrolled ? '40px' : '90px',
                          padding: isScrolled ? '5px' : '10px'
                        }}
                      >
                        <div className="btn-account-icon" style={{
                          marginBottom: isScrolled ? '0' : '8px',
                        
                          height: '40px'
                        }}>
                          <FontAwesomeIcon icon={faBriefcase} />
                        </div>
                        <div className="account-text-container" style={{
                          display: isScrolled ? 'none' : 'flex'
                        }}>
                        
                          <span className="btn-account-text-bottom"> Mes offres</span>
                        </div>
                      </button>

                      <button
                        onClick={() => router.push('/signin')}
                        className="btn-account-square"
                        aria-label="Se connecter"
                        style={{
                          background: isScrolled ? '#ffffff' : 'transparent',
                          border: isScrolled ? '1px solid #f0f0f0' : 'none',
                          color: 'rgb(45, 139, 186)',
                          boxShadow: isScrolled ? '0 4px 10px rgba(0, 0, 0, 0.05)' : 'none',
                          transition: 'all 0.3s ease',
                          width: isScrolled ? '40px' : '90px',
                          height: isScrolled ? '40px' : '90px',
                          padding: isScrolled ? '5px' : '10px'
                        }}
                      >
                        <div className="btn-account-icon" style={{
                          marginBottom: isScrolled ? '0' : '8px',
                         
                          height: '40px'
                        }}>
                          <FontAwesomeIcon icon={farUser} />
                        </div>
                        <div className="account-text-container" style={{
                          display: isScrolled ? 'none' : 'flex'
                        }}>
                          <span className="btn-account-text-top">Mon</span>
                          <span className="btn-account-text-bottom">compte</span>
                        </div>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

      </header>
      <MobileMenu isOpen={isMobileMenu} handleMobileMenu={handleMobileMenu} token={token} handleLogout={handleLogout} />
      <SearchPopup isSearch={isSearch} handleSearch={handleSearch} />
      <OffcanvusMenu isOffcanvus={isOffcanvus} handleOffcanvus={handleOffcanvus} />

      <style jsx>{`
        .mobile-nav-toggler {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          height: 100%;
          cursor: pointer;
          padding: 10px;
        }

        .mobile-nav-toggler i {
          font-size: 24px;
          color: #1a365d;
        }

        .header-btn {
          gap: 10px;
        }

        .btn {
          padding: 8px 16px;
          font-size: 14px;
          font-weight: 500;
          border-radius: 6px;
          transition: all 0.3s ease;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          text-decoration: none;
        }

        .btn-personal-wrapper {
          position: relative;
          text-decoration: none;
          overflow: hidden;
          display: block;
          border-radius: 9999px;
          background: linear-gradient(135deg, #2D8BBA 0%, #1a365d 100%);
          padding: 2px;
          transition: all 0.3s ease;
          box-shadow: 0 4px 10px rgba(45, 139, 186, 0.2);
        }

        .btn-personal-wrapper:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 15px rgba(45, 139, 186, 0.3);
        }

        .btn-personal-content {
          display: flex;
          align-items: center;
          padding: 8px 18px;
          background-color: #2D8BBA;
          color: white;
          border-radius: 9999px;
          gap: 10px;
          transition: all 0.3s ease;
        }

        .btn-personal-wrapper:hover .btn-personal-content {
          background-color: #236a8e;
        }

        .btn-personal-icon {
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
        }

        .btn-personal-wrapper:hover .btn-personal-icon {
          transform: scale(1.1);
          background-color: rgba(255, 255, 255, 0.3);
        }

        .btn-personal-text {
          font-weight: 600;
          font-size: 14px;
          transition: all 0.3s ease;
        }
        
        .btn-logout {
          position: relative;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #2D8BBA 0%, #1a365d 100%);
          color: #fff;
          border: none;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 4px 10px rgba(45, 139, 186, 0.2);
        }

        .btn-logout:hover {
          transform: translateY(-2px) rotate(90deg);
          box-shadow: 0 6px 15px rgba(45, 139, 186, 0.3);
        }

        .btn-logout-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
        }
        
        .btn-logout-tooltip {
          position: absolute;
          bottom: -32px;
          left: 50%;
          transform: translateX(-50%) scale(0.8);
          background-color: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 4px 8px;
          font-size: 12px;
          border-radius: 4px;
          white-space: nowrap;
          opacity: 0;
          visibility: hidden;
          transition: all 0.2s ease;
          pointer-events: none;
        }
        
        .btn-logout:hover .btn-logout-tooltip {
          opacity: 1;
          visibility: visible;
          transform: translateX(-50%) scale(1);
        }

        .navigation {
          list-style: none;
          margin: 0;
          padding: 0;
          gap: 12px;
          flex-wrap: nowrap;
          width: 100%;
        }

        .navigation > li {
          position: relative;
          padding: isScrolled ? '8px 0' : '15px 0';
          white-space: nowrap;
        }

        .navigation > li > a {
          color: #1a365d;
          font-size: 11px;
          font-weight: 500;
          padding: isScrolled ? '8px 0' : '15px 0';
          display: inline-block;
          position: relative;
          transition: all 0.3s ease;
        }

        .menu-item-has-children > a:after {
          content: '▾';
          margin-left: 2px;
          font-size: 8px;
          display: inline-block;
          transition: transform 0.3s ease;
        }

        .submenu {
          position: absolute;
          top: 100px;
          left: 0;
          transform: translateX(0);
          background: #2D8BBA;
          min-width: 180px;
          padding: 4px 0;
          border-radius: 6px;
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
          opacity: 0;
          visibility: hidden;
          transition: all 0.3s ease;
          margin-top: 8px;
          border: 1px solid #e2e8f0;
          z-index: 10000;
        }

        .submenu li a {
          color: #fff;
        }

        .menu-wrap {
          display: flex;
          justify-content: center;
          width: 100%;
        }

        .header-action {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          height: 100%;
        }

        .menu-nav {
          width: 100%;
        }

        .navbar-wrap {
          width: 100%;
        }

        .navigation > li > a:hover {
          color: #2D8BBA;
        }

        .menu-item-has-children:hover > a:after {
          transform: rotate(180deg);
        }

        .menu-item-has-children:hover .submenu {
          opacity: 1;
          visibility: visible;
          margin-top: 0;
        }

        .submenu:before {
          content: '';
          position: absolute;
          top: -4px;
          left: 50%;
          transform: translateX(-50%) rotate(45deg);
          width: 8px;
          height: 8px;
          background: #2D8BBA;
          border-left: 1px solid #e2e8f0;
          border-top: 1px solid #e2e8f0;
        }

        .submenu li {
          padding: 0 15px;
          margin: 0;
          list-style: none;
        }

        .submenu li a {
          display: block;
          padding: 6px 0;
          color: #fff;
          font-size: 11px;
          font-weight: 500;
          transition: all 0.3s ease;
          position: relative;
        }

        .submenu li a:before {
          content: '';
          position: absolute;
          left: -10px;
          top: 50%;
          transform: translateY(-50%);
          width: 0;
          height: 2px;
          background: #fff;
          transition: all 0.3s ease;
        }

        .submenu li a:hover {
          color: #fff;
          padding-left: 3px;
        }

        .submenu li a:hover:before {
          width: 8px;
        }

        @media (max-width: 1200px) {
          .navigation {
            gap: 8px;
          }

          .navigation > li > a {
            font-size: 10px;
          }
        }

        .btn-account-square {
          position: relative;
          text-decoration: none;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: white;
          border-radius: 8px;
          padding: 10px;
          width: 90px;
          height: 90px;
          transition: all 0.3s ease;
          box-shadow: 0 4px 10px rgba(45, 139, 186, 0.2);
          text-align: center;
          border: none;
          cursor: pointer;
        }
        
        .btn-account-square:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 15px rgba(45, 139, 186, 0.2);
        }

        .btn-account-icon {
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;
          font-size: 18px;
          transition: all 0.3s ease;
          margin-left: auto;
          margin-right: auto;
        }
        
        .btn-account-square:hover .btn-account-icon {
          transform: scale(1.1);
          background-color: rgba(255, 255, 255, 0.3);
        }

        .account-text-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;
          line-height: 1;
        }

        .btn-account-text-top {
          font-weight: 400;
          font-size: 16px;
          text-align: center;
          display: block;
          margin-bottom: 3px;
        }

        .btn-account-text-bottom {
          font-weight: 400;
          font-size: 16px;
          text-align: center;
          display: block;
        }

        .user-dropdown-container {
          position: relative;
        }
        
        .user-dropdown {
          position: absolute;
          top: calc(100% + 12px);
          right: 0;
          width: 280px;
          background-color: white;
          border-radius: 12px;
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
          z-index: 10000;
          overflow: hidden;
          animation: dropdownFadeIn 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          border: 1px solid rgba(228, 233, 237, 0.8);
        }
        
        .user-dropdown:before {
          content: '';
          position: absolute;
          top: -6px;
          right: 30px;
          width: 12px;
          height: 12px;
          background: white;
          transform: rotate(45deg);
          border-left: 1px solid rgba(228, 233, 237, 0.8);
          border-top: 1px solid rgba(228, 233, 237, 0.8);
          z-index: 0;
        }
        
        @keyframes dropdownFadeIn {
          from {
            opacity: 0;
            transform: translateY(-15px) scale(0.98);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
        
        .user-dropdown-header {
          padding: 22px 20px;
          display: flex;
          align-items: center;
          background-color: rgba(250, 252, 254, 0.7);
          border-bottom: 1px solid rgba(228, 233, 237, 0.6);
          position: relative;
        }
        
        .user-dropdown-avatar {
          width: 48px;
          height: 48px;
          border-radius: 50%;
         
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          margin-right: 15px;
          font-size: 18px;
          box-shadow: 0 4px 10px rgba(45, 139, 186, 0.2);
        }
        
        .user-dropdown-info {
          flex: 1;
        }
        
        .user-dropdown-name {
          font-weight: 700;
          color: #1a365d;
          font-size: 16px;
          margin-bottom: 3px;
          letter-spacing: -0.2px;
        }
        
        .user-dropdown-role {
          color: #606C72;
          font-size: 13px;
          opacity: 0.8;
        }
        
        .user-dropdown-divider {
          height: 1px;
          background-color: #e8eef4;
          margin: 0;
        }
        
        .user-dropdown-body {
          padding: 10px 0;
        }
        
        .user-dropdown-item {
          display: flex;
          align-items: center;
          padding: 12px 20px;
          color: #1a365d;
          text-decoration: none;
          transition: all 0.2s ease;
          background: none;
          border: none;
          width: 100%;
          text-align: left;
          cursor: pointer;
          font-size: 15px;
          position: relative;
          overflow: hidden;
        }
        
        .user-dropdown-item:hover {
          background-color: rgba(45, 139, 186, 0.05);
          color: rgb(45, 139, 186);
        }
        
        .user-dropdown-item:after {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          width: 3px;
          background: rgb(45, 139, 186);
          transform: translateX(-4px);
          opacity: 0;
          transition: all 0.2s ease;
        }
        
        .user-dropdown-item:hover:after {
          transform: translateX(0);
          opacity: 1;
        }
        
        .user-dropdown-icon {
          margin-right: 12px;
          width: 20px;
          height: 20px;
          color: rgb(45, 139, 186);
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;
        }
        
        .user-dropdown-item:hover .user-dropdown-icon {
          transform: scale(1.1);
        }
        
        .user-dropdown-logout {
          margin-top: 5px;
          border-top: 1px solid rgba(228, 233, 237, 0.6);
          padding-top: 12px;
        }
        
        .user-dropdown-logout .user-dropdown-icon {
          color: #dc3545;
        }
        
        .user-dropdown-logout:hover:after {
          background: #dc3545;
        }
        
        .user-dropdown-logout:hover {
          color: #dc3545;
          background-color: rgba(220, 53, 69, 0.05);
        }
      `}</style>
    </>
  );
}
