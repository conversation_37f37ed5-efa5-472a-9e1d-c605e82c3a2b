import React, { useEffect, useState } from 'react';
import <PERSON> from 'next/link';
import MobileMenu from '../MobileMenu';
import OffcanvusMenu from '../OffcanvusMenu';
import SearchPopup from '../SearchPopup';
import { useRouter } from 'next/router';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSignOutAlt, faUser } from '@fortawesome/free-solid-svg-icons';

export default function Header6({ scroll, isMobileMenu, handleMobileMenu, isSearch, handleSearch, isOffcanvus, handleOffcanvus }) {
  const token = localStorage.getItem('token');
  const [activeLink, setActiveLink] = useState('/');
  const [isScrolled, setIsScrolled] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setActiveLink(router.pathname);
    
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [router.pathname]);

  const handleLogout = () => {
    localStorage.removeItem('token');
    router.push('/signin');
  };

  return (
    <>
      <header id="sticky-header" className={`header-style-three sticky-menu scrolled mb-4`}>
      <div id="sticky-header" className="menu-area" style={{height: 'auto', minHeight: '80px'}}>
          <div className="container custom-container">
            <div className="row align-items-center" style={{minHeight: '60px'}}>
              <div className="col-lg-2 col-md-6 col-7">
                <div className="logo">
                  <Link href="/"><img style={{height: '400px', width: '500px'}} src="/assets/img/logo/logo-1.png" alt="Logo" /></Link>
                </div>
              </div>
              <div className="col-lg-7 col-md-6 col-5 d-none d-md-block">
                <div className="menu-wrap">
                  <nav className="menu-nav">
                    <div className="navbar-wrap main-menu d-none d-lg-flex">
                      <ul className="navigation">
                        <li className={activeLink === '/' ? 'active' : ''}><Link href="/">Accueil</Link></li>
                        <li className={activeLink === '/vous-recruter' ? 'active' : ''}><Link href="/vous-recruter">Entreprises</Link></li>
                        <li className={activeLink === '/candidats' ? 'active' : ''}><Link href="/candidats">Candidats</Link></li>
                        <li className={activeLink === '/blog' ? 'active' : ''}><Link href="/blog">Blog</Link></li>
                        <li className={activeLink === '/contact' ? 'active' : ''}><Link href="/contact">Contact</Link></li>
                      </ul>
                    </div>
                  </nav>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 d-none d-lg-block">
                <div className="header-action">
                  {token ? (
                    <div className="header-btn d-flex align-items-center justify-content-end gap-3">
                      <Link href="/profile" className="btn btn-personal">
                        <FontAwesomeIcon icon={faUser} className="me-2" />
                        <span>Espace personnel</span>
                      </Link>
                      <button onClick={handleLogout} className="btn-circle" title="Déconnexion">
                        <FontAwesomeIcon icon={faSignOutAlt} />
                      </button>
                    </div>
                  ) : (
                    <div className="header-btn">
                      <Link href="/signin" className="btn">Se connecter</Link>
                    </div>
                  )}
                </div>
              </div>
              <div className="col-5 d-lg-none">
                <div className="mobile-nav-toggler" onClick={handleMobileMenu}>
                  <i className="fas fa-bars" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <MobileMenu isOpen={isMobileMenu} handleMobileMenu={handleMobileMenu} token={token} handleLogout={handleLogout} />
      <SearchPopup isSearch={isSearch} handleSearch={handleSearch} />
      <OffcanvusMenu isOffcanvus={isOffcanvus} handleOffcanvus={handleOffcanvus} />

      <style jsx>{`
        .mobile-nav-toggler {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          height: 100%;
          cursor: pointer;
          padding: 10px;
        }

        .mobile-nav-toggler i {
          font-size: 24px;
          color: #1a365d;
        }

        .header-btn {
          gap: 10px;
        }

        .btn {
          padding: 8px 16px;
          font-size: 14px;
          font-weight: 500;
          border-radius: 6px;
          transition: all 0.3s ease;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          text-decoration: none;
        }

        .btn-personal {
          background: #2D8BBA;
          color: #fff;
          border: none;
          padding: 8px 16px;
        }

        .btn-personal:hover {
          background: #1a365d;
        }

        .btn-circle {
          width: 50px;
          height: 35px;
          border-radius: 50%;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
           background: #2D8BBA;
          color: #fff;
          border: 1px solid #1a365d;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .btn-circle:hover {
          background: #1a365d;
          color: #fff;
          transform: rotate(180deg);
        }

        @media (max-width: 991px) {
          .btn {
            padding: 6px 12px;
            font-size: 13px;
          }

          .btn-circle {
            width: 30px;
            height: 30px;
          }
        }
      `}</style>
    </>
  );
}
