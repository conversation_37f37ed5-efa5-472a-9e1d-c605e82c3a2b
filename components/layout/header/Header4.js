import Link from "next/link";
import MobileMenu from "../MobileMenu";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSignOutAlt, faUser } from '@fortawesome/free-solid-svg-icons';

export default function Header4({ scroll, isMobileMenu, handleMobileMenu, isSearch, handleSearch, isOffcanvus, handleOffcanvus }) {
    const token = localStorage.getItem('token');
    
    const handleLogout = () => {
        localStorage.removeItem('token');
        window.location.href = '/';
    };
    
    return (
        <>
            <header className="header-style-five transparent-header scrolled mb-4">
                <div className="header-wrap">
                    <div className="container">
                        <div className="row align-items-center">
                            <div className="col-lg-3 col-md-6 col-7">
                                <div className="logo">
                                    <Link href="/"><img src="/assets/img/logo/logo.png" alt="Logo" /></Link>
                                </div>
                            </div>
                            <div className="col-lg-6 col-md-6 col-5 d-none d-md-block">
                                <div className="menu-wrap">
                                    <nav className="menu-nav">
                                        <div className="navbar-wrap main-menu d-none d-lg-flex">
                                            <ul className="navigation">
                                                <li><Link href="/">Accueil</Link></li>
                                                <li><Link href="/vous-recruter">Entreprises</Link></li>
                                                <li><Link href="/candidats">Candidats</Link></li>
                                                <li><Link href="/blog">Blog</Link></li>
                                                <li><Link href="/contact">Contact</Link></li>
                                            </ul>
                                        </div>
                                    </nav>
                                </div>
                            </div>
                            <div className="col-lg-3 col-md-6 d-none d-lg-block">
                                <div className="header-action">
                                    {token ? (
                                        <div className="header-btn">
                                            <Link href="/dashboard" className="btn">
                                                <FontAwesomeIcon icon={faUser} className="me-2" />
                                                Espace personnel
                                            </Link>
                                            <button onClick={handleLogout} className="btn btn-logout ms-2">
                                                <FontAwesomeIcon icon={faSignOutAlt} className="me-2" />
                                                Exit
                                            </button>
                                        </div>
                                    ) : (
                                        <div className="header-btn">
                                            <Link href="/login" className="btn">Se connecter</Link>
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="col-5 d-lg-none">
                                <div className="mobile-nav-toggler" onClick={handleMobileMenu}>
                                    <i className="fas fa-bars" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            <MobileMenu isOpen={isMobileMenu} handleMobileMenu={handleMobileMenu} token={token} handleLogout={handleLogout} />

            <style jsx>{`
                .header-wrap {
                    background: #fff;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }

                .logo {
                    padding: 15px 0;
                }

                .logo img {
                    max-height: 60px;
                    width: auto;
                }

                .mobile-nav-toggler {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    height: 100%;
                    cursor: pointer;
                    padding: 10px;
                }

                .mobile-nav-toggler i {
                    font-size: 24px;
                    color: #333;
                }

                .btn {
                    display: inline-flex;
                    align-items: center;
                    padding: 10px 20px;
                    background: #2D8BBA;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    font-weight: 500;
                    text-decoration: none;
                    transition: background 0.3s ease;
                }

                .btn:hover {
                    background: #2a6b92;
                }

                .btn-logout {
                    background: #dc3545;
                }

                .btn-logout:hover {
                    background: #bb2d3b;
                }

                @media (max-width: 991px) {
                    .logo img {
                        max-height: 50px;
                    }
                }

                .navigation {
                    display: flex;
                    list-style: none;
                    margin: 0;
                    padding: 0;
                }

                .navigation li {
                    margin: 0 15px;
                }

                .navigation li a {
                    color: #333;
                    text-decoration: none;
                    font-weight: 500;
                    transition: color 0.3s ease;
                }

                .navigation li a:hover {
                    color: #2D8BBA;
                }
            `}</style>
        </>
    );
}
